"""Manages the core logic for handling a single turn of conversation."""

import asyncio
import logging
import json
from typing import List, Dict, Any, Optional, Callable

# Actual imports (restored)
from llm.client import LLMClient
from llm.errors import LLMClientError # Added
from tts import TTSClient
from utils.audio_new_player import AudioNewPlayer, PlayerState
from utils.function_call.function_call_tools import FunctionRegistry
# MCP增强支持
try:
    from utils.mcp.client.mcp_function_adapter import compatible_function_call
    MCP_ENHANCED_AVAILABLE = True
except ImportError:
    MCP_ENHANCED_AVAILABLE = False
from app import shared_state # Keep for history trimming if needed, but history is passed
from app.tts_player import stream_tts_and_play # Import the actual function
from utils.config_loader import TTSConfig # Corrected import path

logger = logging.getLogger("TURN_MANAGER")

# WebSocket Message Types (mirroring server.py for clarity, though not strictly needed here if passed as string types)
MSG_TYPE_LLM_CHUNK = "LLM_CHUNK"
MSG_TYPE_LLM_FINAL_RESPONSE = "LLM_FINAL_RESPONSE"
MSG_TYPE_TOOL_CALL_START = "TOOL_CALL_START"
MSG_TYPE_TOOL_CALL_RESULT = "TOOL_CALL_RESULT"
MSG_TYPE_ERROR_MESSAGE = "ERROR_MESSAGE"

# Added for Task 5.2
MSG_TYPE_SYSTEM_STATUS = "SYSTEM_STATUS"

async def handle_turn(
    user_input: str,
    llm: LLMClient, # Pass LLM client
    tts: TTSClient, # Pass TTS client
    player: AudioNewPlayer, # Pass Audio player
    text_queue: asyncio.Queue, # Pass text queue for this turn
    stop_event: asyncio.Event, # Pass stop event for this turn
    tools: list, # Pass tools
    tts_config: TTSConfig, # Added tts_config parameter
    system_prompt: str = "", # 新增参数，默认空
    websocket_callback: Optional[Callable[[str, Dict], Any]] = None # Added websocket_callback
):
    """Handles the logic for a single turn of the conversation.
       Adapted from test_llm_tts.py handle_turn.
       Now uses websocket_callback for outputs.
    """
    logger.info(f"--- Starting Turn --- Input: '{user_input}'")
    if websocket_callback is None:
        # Fallback to print if no callback is provided, for testing or other uses.
        # However, in the server context, this should always be provided.
        async def dummy_callback(event_type: str, data: dict):
            print(f"[DUMMY_CALLBACK] Event: {event_type}, Data: {data}")
        websocket_callback = dummy_callback
        logger.warning("websocket_callback not provided to handle_turn, using dummy printer.")

    await websocket_callback(MSG_TYPE_SYSTEM_STATUS, {"module": "turn_manager", "status": "logic_started", "current_input": user_input})

    # 1. Clear the text queue (important for interruptions)
    while not text_queue.empty():
        try:
            text_queue.get_nowait()
        except asyncio.QueueEmpty:
            break
    logger.debug("Text queue cleared for the new turn.")

    # 1. 为整个多轮回合初始化消息列表
    messages_history_for_this_turn: List[Dict[str, Any]] = []
    if system_prompt:
        messages_history_for_this_turn.append({"role": "system", "content": system_prompt})
    messages_history_for_this_turn.append({"role": "user", "content": user_input})

    # 最大工具调用轮数，防止无限循环
    MAX_TOOL_CALL_ROUNDS = 5
    tool_call_round_count = 0
    content_for_tts_history = "" # 用于存储最终要进行TTS的文本
    final_assistant_message_for_history = None # 用于存储最终的助手消息对象
    assistant_output_started = False # No longer needed, callback handles discrete messages

    # playback_task 和 playback_task_started 将在实际需要播放TTS时初始化
    playback_task: Optional[asyncio.Task] = None
    playback_task_started: bool = False

    try:
        while tool_call_round_count < MAX_TOOL_CALL_ROUNDS:
            if stop_event.is_set():
                logger.info("多轮函数调用循环在大语言模型调用前被stop_event中断。")
                break

            logger.info(f"--- 多轮函数调用：第 {tool_call_round_count + 1} 轮 ---")
            # Log only last 3 messages to avoid excessive logging, or adjust as needed
            log_messages = messages_history_for_this_turn[-3:]
            try:
                logger.debug(f"LLM messages (last {len(log_messages)}): {json.dumps(log_messages, ensure_ascii=False, indent=2)}")
            except TypeError as e:
                logger.error(f"Error serializing messages for logging: {e}. Messages: {log_messages}")


            # 当前大语言模型流迭代的变量
            accumulated_content_this_iteration = ""
            accumulated_reasoning_this_iteration = ""  # 新增：思维链累积
            tool_calls_buffer_this_iteration = {} # Stores tool calls by index {0: {...}, 1: {...}}
            tool_calls_final_this_iteration = [] # Final list of tool calls for this iteration
            # Initialize assistant_message for current iteration, content might be None if only tool_calls
            assistant_message_for_current_iteration = {"role": "assistant", "content": None}
            current_finish_reason = None
            llm_error_this_iteration = None
            reasoning_sent = False  # 标记思维链是否已发送

            # --- 当前轮次的大语言模型流式传输 ---
            tts_task_started_this_iteration = False  # 标记是否已启动TTS任务
            has_tool_calls_this_iteration = False  # 标记本轮是否检测到工具调用

            async for chunk in llm.stream_chat(messages=messages_history_for_this_turn, tools=tools):
                if stop_event.is_set():
                    logger.info("多轮函数调用期间，大语言模型流式传输被停止事件中断。")
                    # 如果TTS任务已启动，需要取消
                    if tts_task_started_this_iteration and playback_task and not playback_task.done():
                        playback_task.cancel()
                    await websocket_callback(MSG_TYPE_ERROR_MESSAGE, {"source": "turn_manager", "message": "LLM stream interrupted by stop_event."})
                    return # 退出handle_turn

                if chunk.get("error"):
                    llm_error_this_iteration = chunk.get("hint", "未知的大语言模型错误")
                    logger.error(f"多轮函数调用中的大语言模型错误：{llm_error_this_iteration}")
                    await websocket_callback(MSG_TYPE_ERROR_MESSAGE, {"source": "llm_stream", "message": llm_error_this_iteration})
                    content_for_tts_history = llm_error_this_iteration
                    final_assistant_message_for_history = {"role": "assistant", "content": f"[大语言模型错误]：{content_for_tts_history}"}
                    current_finish_reason = "error"
                    break

                # 处理思维链内容（doubao-seed-1.6 thinking模式）
                reasoning_content = chunk.get("reasoning_content")
                if reasoning_content:
                    accumulated_reasoning_this_iteration += reasoning_content

                content_piece = chunk.get("content")
                if content_piece:
                    # 如果有思维链内容且还未发送，先发送完整的思维链
                    if accumulated_reasoning_this_iteration and not reasoning_sent:
                        await websocket_callback("REASONING_COMPLETE", {"reasoning_content": accumulated_reasoning_this_iteration})
                        reasoning_sent = True

                    accumulated_content_this_iteration += content_piece
                    await websocket_callback(MSG_TYPE_LLM_CHUNK, {"content_delta": content_piece, "is_final": False})

                    # 🎯 修改：暂时不启动TTS，等流结束后根据是否有工具调用决定
                    # 先将内容累积，稍后决定是否启动TTS

                tool_calls_chunk = chunk.get("tool_calls")
                if tool_calls_chunk:
                    has_tool_calls_this_iteration = True  # 检测到工具调用
                    for tc in tool_calls_chunk:
                        idx = tc.get("index")
                        # Ensure tc is a dict and 'function' is a key before deepcopying or accessing
                        if isinstance(tc, dict) and "function" in tc and isinstance(tc["function"], dict):
                            # Deep copy to avoid modifying original chunk if it's reused (though usually not)
                            tc_copy = json.loads(json.dumps(tc))
                            if idx is not None: # Ensure index is present
                                if idx not in tool_calls_buffer_this_iteration:
                                    tool_calls_buffer_this_iteration[idx] = tc_copy
                                else:
                                    # Append arguments if they are streaming
                                    existing_args = tool_calls_buffer_this_iteration[idx]["function"].get("arguments", "")
                                    new_args_piece = tc_copy["function"].get("arguments", "")
                                    tool_calls_buffer_this_iteration[idx]["function"]["arguments"] = existing_args + new_args_piece
                        else:
                            logger.warning(f"Skipping malformed tool_call entry in chunk: {tc}")

                
                chunk_finish_reason = chunk.get("finish_reason")
                if chunk_finish_reason and chunk_finish_reason != 'null': # 'null' can be a string from some providers
                    current_finish_reason = chunk_finish_reason
                    logger.info(f"大语言模型流此迭代以原因结束：{current_finish_reason}")
                    if current_finish_reason == "tool_calls":
                        if not tool_calls_buffer_this_iteration:
                            logger.error("结束原因'tool_calls'但工具调用缓冲区为空。将其视为'stop'。")
                            current_finish_reason = "stop"
                            content_for_tts_history = accumulated_content_this_iteration
                            final_assistant_message_for_history = {"role": "assistant", "content": content_for_tts_history}
                        else:
                            tool_calls_final_this_iteration = [tool_calls_buffer_this_iteration[k] for k in sorted(tool_calls_buffer_this_iteration.keys())]
                            assistant_message_for_current_iteration = {
                                "role": "assistant",
                                "content": accumulated_content_this_iteration if accumulated_content_this_iteration else None,
                                "tool_calls": tool_calls_final_this_iteration
                            }
                    elif current_finish_reason == "stop":
                        content_for_tts_history = accumulated_content_this_iteration
                        final_assistant_message_for_history = {"role": "assistant", "content": content_for_tts_history}
                    # For any other finish_reason, if it's not 'error' (handled above) or 'tool_calls' or 'stop',
                    # it will be handled by the 'else' block after the loop.
                    break # Exit the async for chunk loop

            # --- 当前轮次大语言模型流式传输结束 ---

            # 🎯 新逻辑：根据是否有工具调用决定是否启动TTS
            if not tts_task_started_this_iteration and not stop_event.is_set() and accumulated_content_this_iteration:
                # 只有在没有工具调用时才启动TTS
                if not has_tool_calls_this_iteration:
                    logger.info("LLM流结束，无工具调用，启动TTS播放")

                    # 清空文本队列
                    while not text_queue.empty():
                        try:
                            text_queue.get_nowait()
                        except asyncio.QueueEmpty:
                            break

                    # 启动TTS任务
                    playback_task = asyncio.create_task(
                        stream_tts_and_play(tts, player, text_queue, stop_event, tts_config, websocket_callback),
                        name=f"TTSPlayerStreaming-{user_input[:10]}"
                    )
                    playback_task_started = True
                    tts_task_started_this_iteration = True

                    # 将累积的内容放入TTS队列
                    try:
                        text_queue.put_nowait(accumulated_content_this_iteration)
                        text_queue.put_nowait(None)  # 发送结束标记
                        logger.debug("累积内容已放入TTS队列")
                    except Exception as e:
                        logger.error(f"将累积内容放入TTS队列时出错: {e}")
                        if playback_task and not playback_task.done():
                            playback_task.cancel()
                else:
                    logger.info("LLM流结束，检测到工具调用，跳过TTS启动")

            # 🎯 原有逻辑：如果已经启动了TTS，发送结束标记
            elif tts_task_started_this_iteration and not stop_event.is_set():
                try:
                    text_queue.put_nowait(None)  # 发送结束标记
                    logger.debug("LLM流结束，已发送TTS结束标记")
                except Exception as e:
                    logger.error(f"发送TTS结束标记时出错: {e}")

            if stop_event.is_set():
                logger.info("多轮函数调用循环在大语言模型流之后被中断。")
                # 如果TTS任务已启动，需要取消
                if tts_task_started_this_iteration and playback_task and not playback_task.done():
                    playback_task.cancel()
                break # Exit the while loop

            if current_finish_reason == "error":
                # final_assistant_message_for_history 和 content_for_tts_history 已在流错误处理中设置
                if final_assistant_message_for_history: # Ensure it was set
                     messages_history_for_this_turn.append(final_assistant_message_for_history)
                # Error already sent via callback in the stream.
                # The content_for_tts_history (which is the error message) will be handled by the TTS part later.
                break # Exit the while loop

            if current_finish_reason == "tool_calls" and assistant_message_for_current_iteration.get("tool_calls"):
                logger.info(f"处理工具调用(s)：{json.dumps(tool_calls_final_this_iteration, ensure_ascii=False, indent=2)}")
                # Tool names are logged internally by the caller (server.py) based on TOOL_CALL_START if needed.
                # Here we just proceed with execution.
                
                # Send accumulated text before tool call message, if any
                if accumulated_content_this_iteration: # This is content LLM generated *before* deciding to call tools
                    # This content is part of the assistant's thought process leading to tool use.
                    # It was already sent via LLM_CHUNK. We don't send it as LLM_FINAL_RESPONSE here.
                    pass


                messages_history_for_this_turn.append(assistant_message_for_current_iteration)

                tool_messages_for_history = []
                for call_idx, call in enumerate(tool_calls_final_this_iteration):
                    if not (isinstance(call, dict) and "function" in call and isinstance(call["function"], dict) and "name" in call["function"] and "id" in call):
                        logger.warning(f"Skipping malformed tool call object: {call}")
                        error_msg = f"Malformed tool call structure from LLM: {call}"
                        await websocket_callback(MSG_TYPE_ERROR_MESSAGE, {"source": "tool_call_parser", "message": error_msg, "tool_id": call.get("id", f"unknown_malformed_call_{call_idx}")})
                        tool_messages_for_history.append({
                            "role": "tool",
                            "tool_call_id": call.get("id", f"unknown_malformed_call_{call_idx}"),
                            "content": "Malformed tool call structure from LLM.",
                        })
                        continue
                    
                    function_name = call["function"]["name"]
                    tool_call_id = call["id"]
                    arguments_str = call["function"].get("arguments", "{}")
                    tool_result_content = ""

                    await websocket_callback(MSG_TYPE_TOOL_CALL_START, {"name": function_name, "id": tool_call_id, "args_str": arguments_str})
                    
                    try:
                        arguments = json.loads(arguments_str) if arguments_str else {}
                        logger.info(f"调用函数：{function_name}，参数：{arguments}")

                        # 使用MCP增强的函数调用（如果可用）
                        if MCP_ENHANCED_AVAILABLE:
                            result = await compatible_function_call(function_name, **arguments)
                        else:
                            result = await FunctionRegistry.call(function_name, **arguments)

                        # 检查函数返回值是否包含错误状态
                        is_error = False
                        error_message = None
                        
                        if isinstance(result, dict):
                            # 检查标准错误格式: {"status": "error", "message": "..."}
                            if result.get("status") == "error":
                                is_error = True
                                error_message = result.get("message", "工具调用失败")
                            # 检查另一种错误格式: {"success": False, "message": "..."}
                            elif result.get("success") is False:
                                is_error = True
                                error_message = result.get("message", "工具调用失败")
                        
                        if is_error:
                            # 函数返回了错误状态
                            tool_result_content = error_message
                            logger.warning(f"函数 {function_name} 返回错误：{error_message}")
                            await websocket_callback(MSG_TYPE_TOOL_CALL_RESULT, {"name": function_name, "id": tool_call_id, "error": tool_result_content, "status": "error"})
                        else:
                            # 函数执行成功
                            tool_result_content = str(result) if result is not None else "工具执行成功。"
                            logger.info(f"函数 {function_name} 的结果：{tool_result_content}")
                            await websocket_callback(MSG_TYPE_TOOL_CALL_RESULT, {"name": function_name, "id": tool_call_id, "result": tool_result_content, "status": "success"})
                    except json.JSONDecodeError as e:
                        logger.warning(f"解析函数 {function_name} 的JSON参数失败：'{arguments_str}'。错误：{e}")
                        tool_result_content = f"工具调用错误：无效的JSON参数 - {e}"
                        await websocket_callback(MSG_TYPE_TOOL_CALL_RESULT, {"name": function_name, "id": tool_call_id, "error": tool_result_content, "status": "error"})
                    except Exception as e:
                        logger.error(f"执行函数 {function_name} 时出错：{e}", exc_info=True)
                        tool_result_content = f"工具调用错误：{e}"
                        await websocket_callback(MSG_TYPE_TOOL_CALL_RESULT, {"name": function_name, "id": tool_call_id, "error": tool_result_content, "status": "error"})
                    
                    tool_messages_for_history.append({
                        "role": "tool",
                        "tool_call_id": tool_call_id,
                        "content": tool_result_content,
                    })

                messages_history_for_this_turn.extend(tool_messages_for_history)
                tool_call_round_count += 1
                # 🎯 修改：只在工具调用轮次中重置，最终轮次的content_for_tts_history会在stop分支中正确设置
                content_for_tts_history = ""
                final_assistant_message_for_history = None
                # assistant_output_started = False # Reset for next potential LLM text output, or keep true if we want continuous output feel
                                                # Plan suggests resetting (line 175), let's follow.
                # assistant_output_started = False # No longer needed


            elif current_finish_reason == "stop":
                # content_for_tts_history 和 final_assistant_message_for_history 已在流处理中设置
                # All content_delta chunks have been sent.
                # Now send a final response marker if content_for_tts_history is not empty.
                if content_for_tts_history: # Ensure it was set and is not just empty string
                    await websocket_callback(MSG_TYPE_LLM_FINAL_RESPONSE, {"content": content_for_tts_history})

                if final_assistant_message_for_history: # Ensure it was set
                    messages_history_for_this_turn.append(final_assistant_message_for_history)
                # Print removed, handled by callback
                break # Exit the while loop
                
            else:
                # This case handles unexpected finish_reason or if LLM stream ended without a clear finish_reason
                # (e.g. stream just ended, or finish_reason was something else like 'length')
                logger.error(f"意外状态：大语言模型循环在没有'tool_calls'或'stop'的情况下结束。实际原因：'{current_finish_reason}', 累积内容：'{accumulated_content_this_iteration}'")
                if accumulated_content_this_iteration: # If there was content, treat it as the final answer
                    content_for_tts_history = accumulated_content_this_iteration
                    final_assistant_message_for_history = {"role": "assistant", "content": content_for_tts_history}
                else: # No content, so it's an error/unexpected situation
                    content_for_tts_history = "助手响应意外结束。"
                    final_assistant_message_for_history = {"role": "assistant", "content": content_for_tts_history}
                
                if final_assistant_message_for_history:
                    messages_history_for_this_turn.append(final_assistant_message_for_history)
                
                if content_for_tts_history: # Send whatever was accumulated as final if not empty
                     await websocket_callback(MSG_TYPE_LLM_FINAL_RESPONSE, {"content": content_for_tts_history})
                else: # If truly nothing, send an error about unexpected end.
                    await websocket_callback(MSG_TYPE_ERROR_MESSAGE, {"source": "turn_manager", "message": "LLM response ended unexpectedly without content or tool_calls."})

                break # Exit the while loop

        # --- 多轮函数调用while循环结束 ---

        if tool_call_round_count >= MAX_TOOL_CALL_ROUNDS:
            logger.warning(f"达到最大工具调用轮数（{MAX_TOOL_CALL_ROUNDS}）。中止。")
            content_for_tts_history = "我似乎在尝试使用工具时陷入了循环。我们试试别的吧。"
            # Ensure the last assistant message in history reflects this
            # If the last message was already an assistant message (e.g. from a failed tool call that led to max rounds), update it.
            # Otherwise, append a new one.
            if messages_history_for_this_turn and messages_history_for_this_turn[-1]["role"] == "assistant":
                messages_history_for_this_turn[-1]["content"] = content_for_tts_history
                if "tool_calls" in messages_history_for_this_turn[-1]:
                    del messages_history_for_this_turn[-1]["tool_calls"] # Remove any pending tool_calls
                final_assistant_message_for_history = messages_history_for_this_turn[-1]
            else:
                final_assistant_message_for_history = {"role": "assistant", "content": content_for_tts_history}
                messages_history_for_this_turn.append(final_assistant_message_for_history)

            # if not assistant_output_started: print("Assistant: ", end='', flush=True); assistant_output_started = True # Removed
            # print(content_for_tts_history) # Removed
            await websocket_callback(MSG_TYPE_LLM_FINAL_RESPONSE, {"content": content_for_tts_history})


        # --- 文本转语音播放部分 ---
        # 🎯 关键改动：检查是否已经在流式过程中启动了TTS

        if not stop_event.is_set() and content_for_tts_history:
            # 如果还没有启动TTS任务（比如没有流式内容的情况），则启动
            if not playback_task_started:
                logger.info("LLM流式过程中未启动TTS，现在启动最终TTS播放。")

                while not text_queue.empty():
                    try:
                        text_queue.get_nowait()
                    except asyncio.QueueEmpty:
                        break
                logger.debug("为最终TTS播放清空文本队列。")

                playback_task = asyncio.create_task(
                    stream_tts_and_play(tts, player, text_queue, stop_event, tts_config, websocket_callback),
                    name=f"TTSPlayerFinal-{user_input[:10]}"
                )
                playback_task_started = True

                try:
                    text_queue.put_nowait(content_for_tts_history)
                    text_queue.put_nowait(None)
                    logger.debug("最终内容和哨兵已放入TTS队列。")
                except asyncio.QueueFull:
                    logger.error("严重：尝试为文本转语音添加最终内容时文本队列已满！")
                    if playback_task and not playback_task.done(): playback_task.cancel()
                except Exception as e:
                    logger.error(f"将最终内容添加到文本转语音队列时出错：{e}")
                    if playback_task and not playback_task.done(): playback_task.cancel()
            else:
                logger.info("TTS任务已在LLM流式过程中启动，无需重复启动。")
        elif stop_event.is_set():
            logger.info("由于stop_event已设置，跳过最终TTS播放。")
        elif not content_for_tts_history:
            logger.info("没有用于TTS的最终内容，跳过播放。")


    except LLMClientError as e:
        logger.error(f"LLMClientError在回合处理中发生: {e.hint}", exc_info=True)
        content_for_tts_history = e.hint if e.hint else "LLM客户端发生错误。"
        # if not assistant_output_started: print("Assistant: ", end='', flush=True); assistant_output_started = True # Removed
        # print(content_for_tts_history) # Removed
        await websocket_callback(MSG_TYPE_ERROR_MESSAGE, {"source": "llm_client", "message": content_for_tts_history})
        await websocket_callback(MSG_TYPE_LLM_FINAL_RESPONSE, {"content": content_for_tts_history}) # Treat error message as final response for TTS
        
        final_assistant_message_for_history = {"role": "assistant", "content": f"[LLM客户端错误]: {content_for_tts_history}"}
        messages_history_for_this_turn.append(final_assistant_message_for_history)

        if not stop_event.is_set() and content_for_tts_history:
            while not text_queue.empty():
                try: text_queue.get_nowait()
                except asyncio.QueueEmpty: break
            
            playback_task = asyncio.create_task(
                stream_tts_and_play(tts, player, text_queue, stop_event, tts_config, websocket_callback),
                name=f"TTSPlayerLLMClientError-{user_input[:10]}"
            )
            playback_task_started = True
            try:
                text_queue.put_nowait(content_for_tts_history)
                text_queue.put_nowait(None)
            except Exception as q_err:
                logger.error(f"将LLMClientError提示放入TTS队列时出错: {q_err}")
                if playback_task and not playback_task.done(): playback_task.cancel()

    except Exception as e:
        logger.error(f"在回合处理中发生意外错误: {e}", exc_info=True)
        content_for_tts_history = "抱歉，处理您的请求时发生了意外错误。"
        # if not assistant_output_started: print("Assistant: ", end='', flush=True); assistant_output_started = True # Removed
        # print(content_for_tts_history) # Removed
        await websocket_callback(MSG_TYPE_ERROR_MESSAGE, {"source": "turn_manager_exception", "message": content_for_tts_history})
        await websocket_callback(MSG_TYPE_LLM_FINAL_RESPONSE, {"content": content_for_tts_history}) # Treat error message as final response for TTS

        final_assistant_message_for_history = {"role": "assistant", "content": content_for_tts_history}
        # Avoid appending duplicate if an error within the loop already set this.
        if not messages_history_for_this_turn or messages_history_for_this_turn[-1].get("content") != content_for_tts_history:
            messages_history_for_this_turn.append(final_assistant_message_for_history)

        if not stop_event.is_set() and content_for_tts_history:
            while not text_queue.empty():
                try: text_queue.get_nowait()
                except asyncio.QueueEmpty: break

            playback_task = asyncio.create_task(
                stream_tts_and_play(tts, player, text_queue, stop_event, tts_config, websocket_callback),
                name=f"TTSPlayerGenericError-{user_input[:10]}"
            )
            playback_task_started = True
            try:
                text_queue.put_nowait(content_for_tts_history)
                text_queue.put_nowait(None)
            except Exception as q_err:
                logger.error(f"将通用错误提示放入TTS队列时出错: {q_err}")
                if playback_task and not playback_task.done(): playback_task.cancel()
    finally:
        if playback_task and playback_task_started and not playback_task.done():
            task_name = playback_task.get_name()
            logger.info(f"等待最终播放任务 ({task_name}) 完成...")
            try:
                await asyncio.wait_for(playback_task, timeout=None)
                logger.info(f"播放任务 ({task_name}) 完成。")
            except asyncio.CancelledError:
                logger.info(f"播放任务 ({task_name}) 被取消。")
            except asyncio.TimeoutError:
                logger.warning(f"等待播放任务 ({task_name}) 超时。")
            except Exception as e_pb:
                logger.error(f"等待播放任务 ({task_name}) 时出错: {e_pb}", exc_info=True)
        
        # shared_state.conversation_history.extend(messages_history_for_this_turn)
        # logger.debug(f"本轮消息已添加到全局历史。当前历史长度: {len(shared_state.conversation_history)}")
        # shared_state.trim_conversation_history()

        # logger.info(f"--- 结束回合 --- Stop event set: {stop_event.is_set()}.") # This will be printed by the line after the diff

    logger.info(f"--- Ending Turn --- Stop event set: {stop_event.is_set()}.") 