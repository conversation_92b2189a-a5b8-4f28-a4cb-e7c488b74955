#####################################################################################
# 应用通用配置
app:
  input_mode: "asr" # 可选值: "keyboard", "asr"  （键盘输入或语音输入）


#####################################################################################

# ASR 语音识别配置
asr:
  # 音频输入参数 (PyAudio & sherpa-ncnn)
  sample_rate: 16000        # ASR 模型期望的采样率 (Hz)
  channels: 1               # ASR 模型期望的通道数
  vad_model_path: "./model/vad/silero_vad.onnx"

  # sherpa-ncnn ASR 模型参数
  model_dir: "./model/asr/sherpa-onnx-paraformer-zh-2024-03-09"
  model_files:
    tokens: "tokens.txt"
    paraformer: "model.int8.ort"
  num_threads: 6            # ASR 使用的线程数
  decoding_method: "greedy_search" # 解码方法
  feature_dim: 80

  # 内置端点检测规则参数
  endpoint_rule1_min_trailing_silence: 2.4 # 规则1：尾随静音时间 (秒)
  endpoint_rule2_min_trailing_silence: 1.2 # 规则2：尾随静音时间 (秒)
  endpoint_rule3_min_utterance_length: 0.3 # 规则3：最短有效语音时长 (秒)

  # ALSA 配置 (仅 Linux)
  # alsa_device_name: "hw:0,0" # 指定 ALSA 输入设备名 (运行 arecord -l 查看) - 尝试使用 hw 而不是 plughw

  # 麦克风选择配置
  target_microphone_keyword: "UGREEN" # 指定麦克风名称中包含的关键词，可以通过 arecord -l 查看，例如 "UGREEN" 或 "Yundea"。留空则使用系统默认输入设备。

  # 临时文件输出 (可选，如果需要调试, 主要在 VAD 模式下有用 - 但此处保留以备将来可能切换回)
  output_dir: "./output_wavs_asr" # 保存临时语音段的目录 (如果启用)
  save_temp_wav: false       # 是否保存 VAD 切分的临时 WAV 文件 (True/False)


#####################################################################################


# LLM 大语言模型配置
llm:
  # 火山引擎账号配置
  volcengine:
    api_key: ""
    api_url: "https://ark.cn-beijing.volces.com"

  # 模型配置
  model:
    name: "doubao-seed-1.6"  # 全新多模态深度思考模型

  # 推理参数
  parameters:
    # 基础采样参数
    temperature: 0.7    # 温度参数，控制随机性 [0.0-2.0]
    top_p: 0.95         # Top-p采样参数 [0.0-1.0]
    max_tokens: 4096    # 最大输出token数 (回答长度，最大32k)
    stream: true        # 是否使用流式响应

    # doubao-seed-1.6 专用参数
    thinking_mode: "non-thinking"  # 思考模式: "thinking", "non-thinking", "auto"
    # thinking: 显示思维链过程
    # non-thinking: 静止思考，不显示思维链（推荐用于生产环境）
    # auto: 自动选择是否显示思维链

    # 高级参数（可选）
    frequency_penalty: 0.0    # 频率惩罚 [-2.0-2.0]，降低重复内容
    presence_penalty: 0.0     # 存在惩罚 [-2.0-2.0]，鼓励谈论新话题
    # max_completion_tokens: 32768  # 最大回答长度（不包含思维链）
    # max_tokens_total: 65536       # 最大总输出长度（回答+思维链）

    # 其他可选参数
    # seed: 42                # 随机种子，用于可重现的结果
    # stop: ["<|end|>"]       # 停止词列表
    # logit_bias: {}          # 词汇偏置设置

  # 客户端配置
  timeout: 60         # 客户端请求超时时间(秒)，思考模型可能需要更长时间
  system_prompt: |

    你是主人的专属AI助手，名叫小U。你需要用俏皮可爱的语气回应主人，回复不应过长，并严格遵循以下规则：
    1.  **工具调用是你的核心能力**：你的主要任务是理解主人的意图，并从你的工具箱（Function Calling）中选择最合适的工具来完成任务。当用户的请求可以通过工具实现时，你必须优先调用工具，绝对不允许直接用自然语言回复"我做不到"或敷衍。
    2.  **理解并匹配工具**：你的工具箱非常强大，包含了本地设备控制（如音乐、机械臂）和通过MCP连接的外部网络服务（如地图导航、天气查询、网络知识检索等）。你需要仔细阅读每个工具的功能描述（description），并选择与用户意图最匹配的一个或多个工具来执行。
    3.  **重要：多轮工具调用的静默模式**：当需要调用多个工具来完成任务时，请在工具调用阶段保持静默，不要输出中间说明文字。直接调用所需的工具，然后在所有工具调用完成后，基于所有工具的结果给出一个完整、连贯的总结回复。例如：
       - 用户："查询深圳天气并计算456+465"
       - 你应该：静默调用天气工具 → 静默调用计算器工具 → 最后输出："主人，深圳明天多云27-33℃，456+465=921哦！"
       - 而不是：输出说明 → 调用工具 → 输出说明 → 调用工具 → 输出总结
    4.  **结果汇报**：工具调用完成后，再用自然语言简要、清晰地向主人汇报结果。如果工具调用失败，请友好地告知主人，并说明原因（如果工具返回了错误信息）。
    5.  **主动澄清**：当用户的指令信息不足以调用工具时（例如，缺少必要的参数），你必须主动向主人提问，获取足够的信息。例如，问路线时需要明确的起点和终点。
    6.  **一步步来**：对于需要多个步骤才能完成的复杂任务，你应该一步步地调用工具，并在每一步完成后向主人汇报进展。

#####################################################################################


# 音频播放配置
audio_player:
  device: null            # 输出设备索引或名称，null为默认（可以使用 aplay -l 查看）
  device_keyword: "pulse" # 设备名称关键词，用于按关键词查找设备，优先级高于device参数
  blocksize: 2048       # 回调帧数 (增加到2048减少underrun)
  buffer_size: 150      # 队列最大缓冲块数 (增加缓冲)
  min_prebuffer: 15     # 最小预缓冲块数 (增加启动前缓冲)
  volume: 0.5           # 默认音量（0.0-1.0）

# 音乐播放器配置
music_player:
  resource_dir: "resource"  # 音乐资源目录路径（相对于项目根目录）
  volume: 0.7                         # 默认音量（0.0-1.0）
  supported_formats: [".mp3", ".wav", ".flac"]  # 支持的音乐文件格式


#####################################################################################


tts:
  # 火山引擎配置
  volcengine:
    app_id: "" # 从 VOLCENGINE_TTS_APP_ID 环境变量加载
    access_token: "" # 从 VOLCENGINE_TTS_ACCESS_TOKEN 环境变量加载
    resource_id: "volc.service_type.10029"
    ws_url: "wss://openspeech.bytedance.com/api/v3/tts/bidirection" # 火山平台双向流式API 地址： `https://www.volcengine.com/docs/6561/1329505` 
    max_size: 1000000000

  # 音频配置
  audio:
    format: "wav"  # 使用wav格式避免解码问题，流式场景推荐
    sample_rate: 24000  # 推荐采样率，可选: 8000,16000,22050,24000,32000,44100,48000
    speech_rate: -50  # 语速调节 [-50,100]，
    loudness_rate: 0  # 音量调节 [-50,100]，0为正常音量
    enable_timestamp: true  # 启用时间戳，便于同步
    # 新增参数优化语音自然度
    emotion: "neutral"  # 情感设置，中性情感
    emotion_scale: 3  # 情绪值 [1-5]，3为适中
    silence_duration: 300  # 句尾静音时长(ms)，增加自然停顿

  # 默认音色配置
  voice:
    default_speaker: "zh_female_wanwanxiaohe_moon_bigtts"
    available_speakers:
      - "zh_female_shuangkuaisisi_moon_bigtts"
      - "zh_female_roumeinvyou_emo_v2_mars_bigtts"
      - "zh_female_wanwanxiaohe_moon_bigtts"
      # 可以添加更多音色

  # 会话配置
  session:
    user_id: "1234"
    namespace: "BidirectionalTTS"


#####################################################################################


# 新增：远程 YOLO Demo 控制配置
remote_yolo:
  base_url: "http://***********:5000" # YOLO Demo Flask 服务的地址（替换为板卡B的实际IP）
  timeout: 10.0                        # 请求超时时间 (秒)

#####################################################################################


# 形状检测和机械臂控制系统配置已迁移到MCP服务器

#####################################################################################

# MCP (Model Context Protocol) 配置
mcp:
  # 是否启用MCP功能
  enabled: true

  # 机械臂控制MCP服务器配置
  vision_arm_control:
    enabled: true
    server_name: "vision-arm-control"
    description: "智能视觉机械臂控制系统MCP服务器"

  # LLM视觉控制MCP服务器配置
  llm_vision_control:
    enabled: true
    server_name: "llm-vision-control"
    description: "LLM视觉控制系统MCP服务器 - 基于Qwen2.5-VL的智能视觉理解"

  # MCP工具调用超时配置
  timeout:
    connection: 10    # 连接超时（秒）
    health_check: 2   # 健康检查超时（秒）
    tool_call: 60     # 工具调用超时（秒）
