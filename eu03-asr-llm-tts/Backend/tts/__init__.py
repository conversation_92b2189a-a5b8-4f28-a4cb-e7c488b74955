"""
TTS模块 - 基于官方协议完全重构
"""

from .client import TTSClient
from .protocols import Message, MsgType, EventType
from .errors import (
    TTSError,
    TTSConnectionError,
    TTSAuthenticationError,
    TTSQuotaExceededError,
    TTSRateLimitError,
    TTSServerError,
    TTSSessionError,
    TTSErrorCode,
)

__all__ = [
    'TTSClient',
    'Message',
    'MsgType',
    'EventType',
    'TTSError',
    'TTSConnectionError',
    'TTSAuthenticationError',
    'TTSQuotaExceededError',
    'TTSRateLimitError',
    'TTSServerError',
    'TTSSessionError',
    'TTSErrorCode',
]
