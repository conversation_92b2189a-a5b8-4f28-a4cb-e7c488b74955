# TTS配置优化指南

## 🎯 音频格式选择

### 推荐配置
```yaml
audio:
  format: "wav"  # 推荐：无压缩，直接播放
  sample_rate: 24000  # 推荐：性能和质量平衡
```

### 格式对比

| 格式 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| **wav** | 无压缩，直接播放，兼容性好 | 文件较大 | **推荐**：流式播放 |
| **pcm** | 原始数据，最小延迟 | 文件最大，需要额外处理 | 低延迟要求 |
| **mp3** | 文件小，压缩率高 | 需要解码，可能有延迟 | 存储优先 |

### ⚠️ 重要提醒
- **流式场景**: 使用 `wav` 或 `pcm`，避免 `mp3`
- **MP3问题**: 流式场景下会多次返回header，可能导致播放异常
- **解码需求**: MP3需要额外解码步骤，增加复杂度

## 🔊 采样率选择

### 可选值
```
8000, 16000, 22050, 24000, 32000, 44100, 48000
```

### 推荐配置

| 采样率 | 音质 | 文件大小 | CPU消耗 | 适用场景 |
|--------|------|----------|---------|----------|
| **24000** | 高 | 中等 | 中等 | **推荐**：通用场景 |
| 16000 | 中等 | 小 | 低 | 语音通话 |
| 44100 | 很高 | 大 | 高 | 音乐播放 |
| 48000 | 最高 | 最大 | 最高 | 专业音频 |

## 🎛️ 语音参数调节

### 语速控制 (speech_rate)
```yaml
speech_rate: 0  # 范围: [-50, 100]
```

| 值 | 效果 | 适用场景 |
|----|------|----------|
| -50 | 0.5倍速 | 学习、老人 |
| 0 | 正常语速 | **推荐**：通用 |
| 50 | 1.5倍速 | 快速播报 |
| 100 | 2.0倍速 | 极速播报 |

### 音量控制 (loudness_rate)
```yaml
loudness_rate: 0  # 范围: [-50, 100]
```

| 值 | 效果 | 适用场景 |
|----|------|----------|
| -30 | 0.7倍音量 | 安静环境 |
| 0 | 正常音量 | **推荐**：通用 |
| 30 | 1.3倍音量 | 嘈杂环境 |
| 50 | 1.5倍音量 | 户外使用 |

## 🔧 高级参数

### 时间戳 (enable_timestamp)
```yaml
enable_timestamp: true  # 推荐开启
```
- **用途**: 字与音素时间戳
- **好处**: 便于音视频同步
- **建议**: 流式场景建议开启

### 语言检测 (enable_language_detector)
```yaml
additions:
  enable_language_detector: false  # 默认关闭
```
- **用途**: 自动识别语种
- **注意**: 可能影响性能

### Markdown过滤 (disable_markdown_filter)
```yaml
additions:
  disable_markdown_filter: false  # 默认开启过滤
```
- **true**: 解析并过滤markdown，`**你好**` → "你好"
- **false**: 不过滤，`**你好**` → "星星你好星星"

### 静音处理 (silence_duration)
```yaml
additions:
  silence_duration: 0  # 范围: 0-30000ms
```
- **用途**: 句尾增加静音时长
- **建议**: 对话场景可设置100-300ms

## 📊 性能优化建议

### 低延迟配置
```yaml
audio:
  format: "pcm"
  sample_rate: 16000
  enable_timestamp: false
additions:
  enable_language_detector: false
```

### 高质量配置
```yaml
audio:
  format: "wav"
  sample_rate: 48000
  enable_timestamp: true
  speech_rate: 0
  loudness_rate: 0
```

### 平衡配置（推荐）
```yaml
audio:
  format: "wav"
  sample_rate: 24000
  enable_timestamp: true
  speech_rate: 0
  loudness_rate: 0
additions:
  disable_markdown_filter: false
  disable_emoji_filter: false
```

## 🚀 最佳实践

### 1. 流式播放场景
- ✅ 使用 `wav` 格式
- ✅ 采样率 24000 Hz
- ✅ 开启时间戳
- ❌ 避免使用 `mp3`

### 2. 存储优化场景
- ✅ 使用 `mp3` 格式
- ✅ 采样率 16000-24000 Hz
- ✅ 适当压缩参数

### 3. 实时对话场景
- ✅ 使用 `pcm` 或 `wav`
- ✅ 采样率 16000-24000 Hz
- ✅ 关闭不必要的处理
- ✅ 设置适当的静音时长

### 4. 多语言场景
- ✅ 开启语言检测
- ✅ 设置明确语种
- ✅ 配置参考语种

## 🔍 故障排除

### 播放噪音
- **原因**: 音频格式不匹配
- **解决**: 使用 `wav` 格式
- **检查**: 确认播放器期望的格式

### 延迟过高
- **原因**: 采样率过高或格式需要解码
- **解决**: 降低采样率，使用 `pcm` 格式
- **优化**: 关闭不必要的处理

### 音质不佳
- **原因**: 采样率过低
- **解决**: 提高采样率到 24000 或更高
- **平衡**: 考虑性能和质量的权衡

### 配额超限
- **原因**: API调用频率过高
- **解决**: 实现缓存机制
- **优化**: 合并短文本请求

## 📝 配置模板

### 开发环境
```yaml
tts:
  volcengine:
    ws_url: "wss://openspeech.bytedance.com/api/v3/tts/bidirection"
    default_speaker: "zh_female_shuangkuaisisi_moon_bigtts"
  audio:
    format: "wav"
    sample_rate: 24000
    speech_rate: 0
    loudness_rate: 0
    enable_timestamp: true
```

### 生产环境
```yaml
tts:
  volcengine:
    ws_url: "wss://openspeech.bytedance.com/api/v3/tts/bidirection"
    default_speaker: "zh_female_shuangkuaisisi_moon_bigtts"
  audio:
    format: "wav"
    sample_rate: 24000
    speech_rate: 0
    loudness_rate: 0
    enable_timestamp: true
  additions:
    disable_markdown_filter: false
    silence_duration: 200
    cache_config:
      text_type: 1
      use_cache: true
```
