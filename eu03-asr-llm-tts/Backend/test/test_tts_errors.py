#!/usr/bin/env python3
"""
TTS错误处理测试脚本
"""
import asyncio
import logging
import os
import sys

# 添加项目路径 - 从test目录回到Backend目录
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

# 加载环境变量
from dotenv import load_dotenv
from pathlib import Path
dotenv_path = Path('.env')
if dotenv_path.exists():
    load_dotenv(dotenv_path=dotenv_path)

from utils.config_loader import load_config
from tts import (
    TTSClient, 
    TTSError, 
    TTSQuotaExceededError, 
    TTSAuthenticationError,
    TTSConnectionError,
    TTSErrorCode
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_error_handling():
    """测试错误处理功能"""
    logger.info("开始测试TTS错误处理...")
    
    try:
        # 加载配置
        config_loader = load_config()
        tts_config = config_loader.get_tts_config()
        
        if not tts_config:
            logger.error("无法加载TTS配置")
            return False
        
        # 创建TTS客户端
        tts_client = TTSClient(tts_config)
        
        # 测试文本
        test_text = "错误处理测试"
        
        logger.info(f"尝试合成文本: {test_text}")
        
        try:
            audio_data = bytearray()
            chunk_count = 0
            
            async for chunk in tts_client.synthesize_stream(test_text):
                audio_data.extend(chunk)
                chunk_count += 1
                logger.info(f"收到音频块 {chunk_count}, 大小: {len(chunk)} 字节")
            
            logger.info(f"✅ 合成成功，收到 {chunk_count} 个音频块，总大小: {len(audio_data)} 字节")
            
        except TTSQuotaExceededError as e:
            logger.warning(f"⚠️  配额超限错误: {e}")
            logger.info(f"错误码: {e.error_code}")
            logger.info(f"错误详情: {e.details}")
            logger.info("这是预期的错误，说明错误处理正常工作")
            
        except TTSAuthenticationError as e:
            logger.error(f"❌ 认证错误: {e}")
            logger.info(f"错误码: {e.error_code}")
            return False
            
        except TTSConnectionError as e:
            logger.error(f"❌ 连接错误: {e}")
            logger.info(f"错误码: {e.error_code}")
            return False
            
        except TTSError as e:
            logger.warning(f"⚠️  TTS错误: {e}")
            logger.info(f"错误码: {e.error_code}")
            logger.info(f"是否可重试: {e.is_retryable}")
            
        except Exception as e:
            logger.error(f"❌ 未知错误: {e}")
            return False
        
        # 测试关闭连接
        await tts_client.close()
        logger.info("✅ 连接已关闭")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)
        return False


async def test_error_code_mapping():
    """测试错误码映射"""
    logger.info("开始测试错误码映射...")
    
    test_codes = [
        TTSErrorCode.SUCCESS,
        TTSErrorCode.CLIENT_QUOTA_EXCEEDED,
        TTSErrorCode.CLIENT_AUTH_FAILED,
        TTSErrorCode.CLIENT_RATE_LIMIT,
        TTSErrorCode.SERVER_SESSION_ERROR,
        TTSErrorCode.SERVER_INTERNAL_ERROR,
        999999,  # 未知错误码
    ]
    
    for code in test_codes:
        message = TTSErrorCode.get_error_message(code)
        is_client = TTSErrorCode.is_client_error(code)
        is_server = TTSErrorCode.is_server_error(code)
        is_retryable = TTSErrorCode.is_retryable(code)
        
        logger.info(f"错误码 {code}:")
        logger.info(f"  消息: {message}")
        logger.info(f"  客户端错误: {is_client}")
        logger.info(f"  服务端错误: {is_server}")
        logger.info(f"  可重试: {is_retryable}")
        logger.info("")
    
    return True


async def test_invalid_credentials():
    """测试无效凭据"""
    logger.info("开始测试无效凭据处理...")
    
    try:
        # 加载配置
        config_loader = load_config()
        tts_config = config_loader.get_tts_config()
        
        if not tts_config:
            logger.error("无法加载TTS配置")
            return False
        
        # 修改为无效的凭据
        tts_config.app_id = "invalid_app_id"
        tts_config.access_token = "invalid_access_token"
        
        # 创建TTS客户端
        tts_client = TTSClient(tts_config)
        
        try:
            await tts_client.connect()
            logger.warning("⚠️  预期认证失败，但连接成功了")
            await tts_client.close()
            return False
            
        except TTSAuthenticationError as e:
            logger.info(f"✅ 正确捕获认证错误: {e}")
            return True
            
        except Exception as e:
            logger.warning(f"⚠️  捕获到其他错误（可能网络问题）: {e}")
            return True  # 网络问题也算正常
        
    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)
        return False


async def main():
    """主测试函数"""
    logger.info("开始TTS错误处理测试")
    
    # 检查环境变量
    if not os.getenv('VOLCENGINE_TTS_APP_ID') or not os.getenv('VOLCENGINE_TTS_ACCESS_TOKEN'):
        logger.error("请设置环境变量 VOLCENGINE_TTS_APP_ID 和 VOLCENGINE_TTS_ACCESS_TOKEN")
        return
    
    tests = [
        ("错误码映射", test_error_code_mapping),
        ("错误处理", test_error_handling),
        ("无效凭据", test_invalid_credentials),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"运行测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
        
        # 测试间隔
        await asyncio.sleep(1)
    
    # 输出测试结果
    logger.info(f"\n{'='*50}")
    logger.info("测试结果汇总")
    logger.info(f"{'='*50}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        logger.info("🎉 所有错误处理测试都通过了！")
    else:
        logger.warning(f"⚠️  有 {total - passed} 个测试失败。")


if __name__ == "__main__":
    asyncio.run(main())
