# 更新日志

记录更新内容，提交的时候无需将日期一起放入commit当中。
 
**更新类型说明：**
- 类型(type)：
  - feat: 功能变更（新增/修改功能）
  - remove: 删除内容（文件/代码/资源）
  - refactor: 代码重构（结构调整/优化）
  - docs: 文档相关（README/注释/手册）  
  - fix: 问题修复（BUG/逻辑错误）
  - ui: 界面调整（布局/样式/交互）
  - chore: 日常维护（构建/配置/清理）
  - test: 测试相关（用例/框架）

项目版本号管理说明：
本项目采用语义化版本控制（Semantic Versioning）的概念进行版本管理，并结合自定义的递增规则，以反映项目的开发进度和功能变化。版本号格式为：V{MAJOR}.{MINOR}.{PATCH}.{BUILD}。

**版本号组成说明：**

*   **MAJOR (主版本号)**：
    *   当进行了**不兼容的 API 修改**或**引入了重大新功能**时，主版本号会递增。这通常意味着需要对依赖此版本的外部组件或用户进行修改才能兼容。
    *   **递增时机：** 当有全局性的、底层架构的、或对外部接口/用户体验有重大影响的不兼容变更时。

*   **MINOR (次版本号)**：
    *   当以**向下兼容**的方式添加了**新功能**或进行了**功能性改进**时，次版本号会递增。
    *   **递增时机：** 当添加了新的功能模块、新的 API 端点、或者对现有功能进行了显著的、向下兼容的增强时。

*   **PATCH (补丁版本号)**：
    *   当进行了**向下兼容的问题修复**时，补丁版本号会递增。
    *   **递增时机：** 当修复了 BUG、逻辑错误、UI 问题等不引入新功能且不破坏兼容性的变更时。

*   **BUILD (构建版本号)**：
    *   此版本号用于区分在**同一个 PATCH 版本号下**进行的**多次提交**。通常用于**同一天**内进行的小幅迭代、测试或连续的 BUG 修复，但这些变更不足以独立提升 PATCH 版本号。
    *   **递增时机：** 当在同一天内，对代码进行了多次小幅度的修改、调试或连续的提交，这些变更属于同一个 PATCH 版本范围，且希望区分每次具体的提交时。 BUILD 版本号会从 `1` 开始递增。

**版本号递增规则总结：**

1.  **MAJOR 递增时：** MINOR、PATCH 和 BUILD 版本号**重置为 0**。
2.  **MINOR 递增时：** PATCH 和 BUILD 版本号**重置为 0**。
3.  **PATCH 递增时：** BUILD 版本号**重置为 0**。
4.  **BUILD 递增时：** 仅 BUILD 版本号递增。

**示例：**

*   `V1.0.0` -> `V2.0.0` (重大不兼容变更)
*   `V1.0.0` -> `V1.1.0` (新增向下兼容功能)
*   `V1.0.0` -> `V1.0.1` (修复向下兼容 BUG)
*   `V1.0.1` -> `V1.0.1.1` (同一天在 V1.0.1 版本下进行了小改动)
*   `V1.0.1.1` -> `V1.0.1.2` (同一天在 V1.0.1 版本下又进行了小改动)
*   `V1.0.1.2` -> `V1.0.2` (修复了另一个 BUG，或进入了新的一天进行 PATCH 修复)
*   `V1.0.2` -> `V1.1.0` (添加了新的向下兼容功能)

---

**V1.6.2 fix(tts): 优化TTS语速和语音自然度配置**

日期：2025-08-10

**类型**: `fix`
**范围**: `tts`

**说明**:

修复TTS V3 API重构后语速过快和语音不够自然的问题。问题根源在于新协议实现后的默认语速设置与用户期望不符，需要通过调整语速参数和增加语音自然度配置来改善用户体验。通过优化语速控制、添加情感参数和静音时长配置，使TTS语音输出更加自然流畅。

**变更内容**:

*   **1. 优化TTS语速控制参数 (`fix`, `tts`)**:
    *   **文件**: `Backend/config.yaml`
    *   **变更**: 调整语速参数以获得更自然的播放效果
    *   **配置优化**:
        - `speech_rate`: 0 → -50（0.5倍语速，最慢设置）
        - 解决V3 API重构后语速过快的问题
        - 提供更舒适的语音播放体验

*   **2. 增强TTS语音自然度配置 (`feat`, `tts`)**:
    *   **文件**: `Backend/config.yaml`, `Backend/tts/client.py`
    *   **变更**: 添加情感控制和静音时长参数
    *   **新增配置**:
        - `emotion`: "neutral"（中性情感设置）
        - `emotion_scale`: 3（适中的情绪值，范围1-5）
        - `silence_duration`: 300ms（句尾静音时长，增加自然停顿）
        - 支持更细粒度的语音表现力控制

*   **3. 更新TTS客户端参数支持 (`feat`, `tts`)**:
    *   **文件**: `Backend/tts/client.py`
    *   **变更**: 扩展音频参数处理逻辑
    *   **技术改进**:
        - 添加情感参数（emotion、emotion_scale）的处理
        - 支持静音时长配置的传递
        - 完善参数验证和默认值处理
        - 确保新参数与火山引擎V3 API兼容

**技术要点**:

- **语速优化**: 使用最慢语速设置(-50)解决播放过快问题
- **情感控制**: 通过中性情感设置提高语音自然度
- **停顿优化**: 300ms句尾静音增加语音节奏感
- **参数扩展**: 支持火山引擎V3 API的完整音频参数集
- **向后兼容**: 保持现有配置的兼容性

**改进效果**:

- 🎵 **语速舒适**: 0.5倍语速提供更舒适的听觉体验
- 🎭 **情感自然**: 中性情感设置避免过度表现
- ⏸️ **节奏优化**: 句尾停顿增加语音节奏感
- 🔧 **精细控制**: 支持更多音频参数的个性化调节

---

**V1.6.1 feat(mcp): 集成LLM视觉控制系统MCP服务器**

日期：2025-08-10

**类型**: `feat`
**范围**: `mcp`

**说明**:

集成proj_llm-vision-control项目作为新的MCP服务器，扩展EU03系统的视觉理解能力。该服务器基于Qwen2.5-VL多模态大语言模型，提供实时视频流处理、智能物体检测和视觉理解功能。通过MCP协议集成，AI助手可以直接调用视觉分析工具，实现更丰富的多模态交互体验。

**变更内容**:

*   **1. 添加LLM视觉控制MCP服务器配置 (`feat`, `mcp`)**:
    *   **文件**: `Backend/utils/mcp/config/mcp_servers.json`
    *   **变更**: 新增llm-vision-control服务器配置
    *   **配置详情**:
        - 服务器名称：`llm-vision-control`
        - 连接地址：`http://***********:8003/mcp/`
        - 传输协议：`streamable_http`
        - 连接超时：10秒，健康检查超时：2秒
        - 描述：基于Qwen2.5-VL的智能视觉理解系统

*   **2. 优化TTS时间戳配置 (`fix`, `tts`)**:
    *   **文件**: `Backend/config.yaml`
    *   **变更**: 启用TTS时间戳功能
    *   **配置优化**:
        - `enable_timestamp`: false → true
        - 便于音视频同步和多模态交互
        - 支持更精确的语音合成时序控制

**新增MCP工具能力**:

通过集成llm-vision-control服务器，EU03系统新增以下视觉理解工具：

- **`detect_objects`**: 检测图像中的物体和场景
- **`get_latest_measurements`**: 获取最新的视觉检测结果
- **`get_system_status`**: 获取视觉系统运行状态
- **`get_video_stream_url`**: 获取实时视频流访问地址

**技术要点**:

- **多模态集成**: 结合语音识别、语言理解和视觉分析的完整AI助手
- **实时视觉处理**: 支持实时摄像头画面分析和物体检测
- **统一MCP架构**: 与现有vision-arm-control服务器保持一致的集成方式
- **高性能模型**: 基于Qwen2.5-VL-72B-Instruct大模型的视觉理解
- **灵活配置**: 支持独立启停和健康检查机制

**使用场景**:

- 🎯 **智能监控**: "帮我看看摄像头里有什么"
- 🔍 **物体识别**: "识别一下桌子上的物品"
- 📊 **场景分析**: "分析当前环境的安全状况"
- 🤖 **多模态交互**: 结合语音和视觉的智能对话

**系统架构增强**:
```
EU03 AI助手系统
├── 语音识别 (ASR)
├── 语言理解 (LLM)
├── 语音合成 (TTS) ✅ 已优化
├── MCP服务集成
│ ├── 高德地图服务 (amap-maps)
│ ├── 机械臂控制 (vision-arm-control)
│ └── 视觉理解 (llm-vision-control) ✨ 新增
└── 音频播放控制
```

---

**V1.6.0 feat(tts): 修复TTS V3 API协议兼容性和音频播放噪音问题**

日期：2025-08-10

**类型**: `feat`
**范围**: `tts`

**说明**:

修复了火山引擎TTS V3 API协议兼容性问题和音频播放噪音问题。问题根源在于原有TTS实现使用的协议格式与官方V3 API不兼容，导致WebSocket连接异常关闭（错误码1000），同时MP3格式音频数据被直接当作PCM播放导致噪音。通过基于官方协议完全重构TTS模块，实现标准化的协议处理、完善的错误码管理和音频格式优化，确保TTS服务的稳定性和音频质量。

**变更内容**:

*   **1. 基于官方协议完全重构TTS模块 (`feat`, `tts`)**:
    *   **文件**: `Backend/tts/protocols.py`, `Backend/tts/client.py`
    *   **变更**: 基于官方volcengine_bidirection_demo重新实现协议处理
    *   **技术改进**:
        - 实现标准的二进制帧格式解析和构建
        - 支持完整的事件类型和消息类型枚举
        - 添加正确的协议头部结构和字段序列化
        - 实现标准的WebSocket连接管理和会话处理
        - 支持双向流式合成和单次合成模式

*   **2. 实现完善的错误码处理系统 (`feat`, `tts`)**:
    *   **文件**: `Backend/tts/errors.py`
    *   **变更**: 添加火山引擎TTS API标准错误码处理
    *   **技术改进**:
        - 定义完整的错误码枚举（成功、客户端错误、服务端错误）
        - 实现错误码到异常类的自动映射
        - 提供详细的错误描述和解决方案
        - 支持错误重试判断和配额问题诊断
        - 添加特定异常类：认证错误、配额超限、频率限制等

*   **3. 优化音频格式配置解决播放噪音 (`fix`, `tts`)**:
    *   **文件**: `Backend/config.yaml`, `Backend/tts/client.py`
    *   **变更**: 调整音频格式从MP3到WAV，优化音频参数配置
    *   **配置优化**:
        - 音频格式：mp3 → wav（避免解码问题）
        - 采样率：保持24000 Hz（性能和质量平衡）
        - 新增语速控制：speech_rate（范围-50到100）
        - 新增音量控制：loudness_rate（范围-50到100）
        - 启用时间戳：enable_timestamp（便于音视频同步）

*   **4. 更新模块导入和接口兼容性 (`fix`, `tts`)**:
    *   **文件**: `Backend/tts/__init__.py`, `Backend/server.py`, `Backend/app/turn_manager.py`
    *   **变更**: 更新导入路径，保持向后兼容
    *   **接口改进**:
        - 统一TTS客户端接口，保持原有API兼容
        - 导出错误类供上层应用使用
        - 更新服务器初始化代码使用新TTS模块
        - 清理过时的协议和常量文件

*   **5. 添加测试和文档 (`feat`, `tts`)**:
    *   **文件**: `Backend/test/test_tts_errors.py`, `Backend/test/test_tts_integration.py`, `Backend/tts/TTS_CONFIG_GUIDE.md`
    *   **变更**: 提供完整的测试套件和配置指南
    *   **测试覆盖**:
        - 错误处理测试：验证各种错误码的正确处理
        - 集成测试：验证连接、合成、播放完整流程
        - 配置优化指南：详细的参数说明和最佳实践
        - 故障排除文档：常见问题和解决方案

**技术要点**:

- **协议标准化**: 完全符合火山引擎V3 API官方协议规范
- **音频格式优化**: WAV格式确保流式播放兼容性，避免MP3解码问题
- **错误处理增强**: 20+种错误码分类处理，提供具体解决建议
- **向后兼容**: 保持原有API接口不变，平滑升级
- **配置灵活性**: 支持语速、音量、采样率等多种音频参数调节
- **文档完善**: 提供详细的配置指南和故障排除文档

---

**V1.5.9 refactor(function-call): 清理已迁移到MCP的vision-arm-control代码**

日期：2025-08-03

**类型**: `refactor`
**范围**: `function-call`

**说明**:

完成vision-arm-control功能从Function Calling到MCP协议的迁移后续清理工作。移除了所有已废弃的Function Calling实现代码，包括主要实现文件、测试文件、schema定义和相关配置，确保代码库的整洁性和一致性。此次清理避免了新旧实现的混淆，提升了代码可维护性。

**变更内容**:

*   **1. 删除主要实现文件 (`refactor`, `function-call`)**:
    *   **文件**: `Backend/utils/function_call/vision_arm_control.py`
    *   **变更**: 完全删除660行的vision-arm-control Function Calling实现
    *   **清理内容**:
        - 移除所有形状检测相关函数（shape_get_all_objects等）
        - 移除所有机械臂控制函数（robot_arm_catch_object等）
        - 移除所有方向控制函数（robot_arm_move_*等）
        - 清理相关的Python缓存文件

*   **2. 清理测试文件 (`refactor`, `test`)**:
    *   **文件**: `Backend/test/test_shape_detection.py`
    *   **变更**: 删除依赖vision_arm_control模块的测试文件
    *   **影响**: 移除了对已删除模块的测试引用

*   **3. 清理Schema定义 (`refactor`, `function-call`)**:
    *   **文件**: `Backend/utils/function_call/function_schema.py`
    *   **变更**: 移除所有vision-arm-control相关的schema定义
    *   **技术改进**:
        - 删除 `get_shape_detection_schemas()` 函数的所有schema内容
        - 删除 `get_robot_arm_control_schemas()` 函数的所有schema内容
        - 保留函数框架但返回空数组，添加MCP迁移说明
        - 在 `get_all_schemas()` 中保持注释状态

*   **4. 清理配置文件 (`refactor`, `config`)**:
    *   **文件**: `Backend/config.yaml`
    *   **变更**: 简化已注释的intelligent_vision_arm_control配置
    *   **配置优化**:
        - 移除详细的注释配置内容
        - 替换为简洁的迁移说明注释
        - 保持MCP配置部分不变

*   **5. 更新项目文档 (`docs`, `refactor`)**:
    *   **文件**: `README.md`, `Backend/README.md`
    *   **变更**: 更新功能说明和文件结构文档
    *   **文档改进**:
        - 更新机械臂控制功能说明，标注已迁移到MCP
        - 修改文件结构说明，移除vision_arm_control.py引用
        - 保持功能描述的准确性和一致性

**技术要点**:

- **完整清理**: 移除所有Function Calling相关的vision-arm-control代码
- **架构统一**: 确保系统只保留MCP实现，避免新旧架构混淆
- **文档一致**: 所有文档都反映当前的MCP架构状态
- **向前兼容**: 保留必要的函数框架和注释，便于理解迁移历史
- **代码质量**: 通过语法检查，确保清理过程不引入错误

---

**V1.5.8 fix(mcp): 修复MCP服务器离线导致TTS播放异常问题**

日期：2025-08-03

**类型**: `fix`
**范围**: `mcp`

**说明**:

修复了MCP服务器离线时导致TTS音频播放出现ALSA underrun错误的严重问题。问题根源在于MCP连接超时时间过长（30秒），阻塞了异步事件循环，影响TTS音频流的正常播放。通过实现快速健康检查机制、优化连接超时配置和并发连接处理，确保离线MCP服务器不会影响系统其他功能的正常运行。

**变更内容**:

*   **1. 实现MCP连接健康检查机制 (`feat`, `mcp`)**:
    *   **文件**: `Backend/utils/mcp/client/mcp_client_manager.py`
    *   **变更**: 添加快速TCP连接检查，在完整连接前预检测服务器可达性
    *   **技术改进**:
        - 新增 `health_check()` 方法：使用socket进行快速连接测试
        - 支持自定义健康检查超时时间（默认3秒）
        - 解析URL并提取主机名和端口进行连接测试
        - 区分网络连接错误和其他类型错误

*   **2. 优化MCP初始化流程和错误处理 (`fix`, `mcp`)**:
    *   **文件**: `Backend/utils/mcp/client/mcp_client_manager.py`
    *   **变更**: 改进连接逻辑，实现优雅降级和并发连接
    *   **技术改进**:
        - 在连接前强制执行健康检查
        - 增强错误分类：区分超时错误、连接错误和其他异常
        - 实现并发连接：多个MCP服务器同时连接，避免串行阻塞
        - 添加 `_connect_server_with_timeout()` 方法进行总体超时控制
        - 连接失败时立即跳过，不影响其他服务器连接

*   **3. 调整MCP超时配置参数 (`fix`, `mcp`)**:
    *   **文件**: `Backend/utils/mcp/config/mcp_servers.json`, `Backend/config.yaml`
    *   **变更**: 优化超时时间设置，减少阻塞时间
    *   **配置优化**:
        - `vision-arm-control` 连接超时：30秒 → 10秒
        - 新增 `health_check_timeout`: 2秒
        - 主配置文件添加健康检查超时配置
        - 保持SSE连接的原有配置不变

**技术要点**:

- **快速故障检测**: 2秒健康检查替代30秒连接超时，减少99%的阻塞时间
- **并发连接架构**: 使用 `asyncio.gather()` 实现多服务器并发连接
- **优雅降级机制**: 单个服务器失败不影响其他服务器和系统功能
- **事件循环保护**: 避免长时间网络I/O阻塞影响TTS等实时功能
- **错误分类处理**: 区分连接超时、健康检查失败等不同错误类型

---

**V1.5.7 fix(backend): 修复工具调用状态显示错误问题**

日期：2025-08-02

**类型**: `fix`
**范围**: `backend`

**说明**:

修复了工具调用失败时状态仍显示为"成功"的严重问题。问题根源在于后端的工具调用结果处理逻辑无法正确识别函数返回的错误状态格式，导致所有非异常的错误都被误判为成功。通过增强错误检测机制和统一错误返回格式，确保前端能够准确显示工具调用的真实执行状态。

**变更内容**:

*   **1. 增强工具调用错误检测逻辑 (`fix`, `backend`)**:
    *   **文件**: `Backend/app/turn_manager.py`
    *   **变更**: 扩展错误状态检测，支持多种错误格式
    *   **技术改进**:
        - 检测标准错误格式：`{"status": "error", "message": "..."}`
        - 检测另一种错误格式：`{"success": False, "message": "..."}`
        - 统一错误消息发送：确保 `TOOL_CALL_RESULT` 正确传递 `status: "error"`
        - 增加详细的错误日志记录

*   **2. 修复YOLO控制函数错误返回格式 (`fix`, `backend`)**:
    *   **文件**: `Backend/utils/function_call/remote_yolo_control.py`
    *   **变更**: 统一错误返回格式，保留完整状态信息
    *   **技术改进**:
        - `yolo_get_face_count`: 失败时返回字典而不是字符串
        - `yolo_move_servo_relative`: 参数验证错误返回标准格式
        - `yolo_reset_servo`: 统一成功/失败处理逻辑
        - `yolo_toggle_tracking`: 保持状态信息完整性

*   **3. 更新函数包装器类型约束 (`fix`, `backend`)**:
    *   **文件**: `Backend/utils/function_call/function_call_tools.py`
    *   **变更**: 移除强制字符串返回类型，支持字典返回
    *   **技术改进**:
        - 移除 `-> str` 类型注解限制
        - 允许包装函数传递完整的字典状态信息
        - 保持向后兼容性

**技术要点**:

- **多格式错误检测**: 支持 `{"status": "error"}` 和 `{"success": False}` 两种错误格式
- **状态信息完整性**: 确保错误状态在整个调用链中不丢失
- **类型系统优化**: 移除过度严格的类型约束，提高灵活性
- **日志增强**: 添加详细的错误状态记录便于调试

---

**V1.5.6 feat(frontend): 重构消息气泡布局系统，解决工具调用宽度溢出问题**

日期：2025-08-02

**类型**: `feat`
**范围**: `frontend`

**说明**:

重构了前端聊天界面的消息气泡布局系统，彻底解决了工具调用组件宽度溢出导致用户头像和对话内容被挤到屏幕外的问题。使用CSS Grid布局替代Flex布局，实现了更健壮的宽度约束机制，同时保持原有的视觉风格和交互体验。

**变更内容**:

*   **1. 重构消息气泡布局系统 (`feat`, `frontend`)**:
    *   **文件**: `Frontend/components/chat-interface.tsx`
    *   **变更**: 使用CSS Grid布局替代Flex布局，解决宽度约束问题
    *   **技术改进**:
        - 用户消息：`grid-cols-[minmax(0,1fr)_auto] justify-items-end`
        - 助手消息：`grid-cols-[auto_minmax(0,1fr)] justify-items-start`
        - 添加容器约束：`overflow-x-hidden` 和 `max-w-full`
        - 用户气泡自适应宽度：`w-auto inline-block` 替代固定宽度

*   **2. 优化工具调用组件宽度约束 (`feat`, `frontend`)**:
    *   **文件**: `Frontend/components/tool-call-display.tsx`
    *   **变更**: 增强工具调用组件的宽度约束和文本处理
    *   **技术改进**:
        - 根容器添加 `overflow-hidden` 和 `max-w-full`
        - 工具名称使用 `truncate` 截断，`title` 属性显示完整名称
        - 所有内容区域添加 `min-w-0` 和 `overflow-wrap-anywhere`
        - 状态徽章设为 `flex-shrink-0` 防止被压缩

*   **3. 增强响应式布局支持 (`feat`, `frontend`)**:
    *   **文件**: `Frontend/components/chat-interface.tsx`
    *   **变更**: 优化不同屏幕尺寸下的显示效果
    *   **改进内容**:
        - 消息容器添加 `overflow-x-hidden` 防止水平滚动
        - 用户气泡宽度根据文字长度自适应调整
        - 助手气泡保持全宽以容纳工具调用等复杂内容

**技术要点**:

- **CSS Grid布局**: 使用现代Grid布局替代Flex，提供更可预测的宽度约束
- **宽度约束机制**: 多层 `min-w-0`、`max-w-full`、`overflow-hidden` 约束
- **文本处理优化**: `break-words`、`overflow-wrap-anywhere` 确保长文本正确换行
- **响应式设计**: 保持在不同屏幕尺寸下的良好显示效果

---

**V1.5.5 feat(mcp): 完成机械臂功能从Function Calling到MCP的完整迁移**

日期：2025-08-02

**类型**: `feat`
**范围**: `mcp`

**说明**:

完成了机械臂相关功能从传统Function Calling到MCP协议的完整迁移，实现了FC和MCP共存的架构。添加了局域网实现的MCP服务器连接的配置文件，修复了MCP连接的测试文件和代理设置问题。现在LLM可以通过MCP协议直接调用所有机械臂控制功能，包括物体检测、抓取放置、初始化动作、特殊放置模式和方向控制等。

**变更内容**:

*   **1. 添加局域网实现的MCP服务器连接的配置文件 (`feat`, `mcp`)**:
    *   **文件**: `Backend/utils/mcp/config/mcp_servers_example.json`
    *   **变更**: 添加了局域网实现的MCP服务器连接的配置文件，给后续的使用提供示例

*   **2. 在`mcp_servers.json`当中添加了局域网实现的MCP服务器连接的配置内容 (`feat`, `mcp`)**:
    *   **文件**: `Backend/utils/mcp/config/mcp_servers.json`
    *   **变更**: 在`mcp_servers.json`当中添加了局域网实现的MCP服务器连接的配置内容，方便在局域网内实现MCP服务器连接
    *   **重要信息**: FastMCP服务器需要客户端同时接受`application/json`和`text/event-stream`两个请求头，否则会报错；使用的传输协议是`streamable-http`。

*   **3. 修复了mcp连接的测试文件 (`fix`, `mcp`)**:
    *   **文件**: `Backend/utils/mcp/tools/final_mcp_test.py`
    *   **变更**: 修复了mcp连接的测试文件，修复了异步清理错误的问题

*   **4. 将机械臂相关功能从Function Calling迁移到MCP (`feat`, `mcp`)**:
    *   **文件**: `Backend/utils/function_call/function_schema.py`
    *   **变更**: 注释掉机械臂相关的Function Calling schemas，包括`get_shape_detection_schemas()`和`get_robot_arm_control_schemas()`
    *   **影响**: 机械臂功能不再通过传统Function Calling提供，改为使用MCP协议

*   **5. 注释机械臂相关的Function Registry注册 (`feat`, `mcp`)**:
    *   **文件**: `Backend/utils/function_call/function_call_tools.py`
    *   **变更**: 注释掉所有机械臂相关的Function Registry注册，包括导入语句和函数注册
    *   **影响**: 清理了旧的Function Calling实现，避免与MCP功能冲突

*   **6. 添加MCP配置到主配置文件 (`feat`, `mcp`)**:
    *   **文件**: `Backend/config.yaml`
    *   **变更**: 添加了完整的MCP配置部分，包括vision_arm_control服务器配置和超时设置
    *   **新增配置**:
        - `mcp.enabled`: 启用MCP功能
        - `mcp.vision_arm_control`: 机械臂控制MCP服务器配置
        - `mcp.timeout`: MCP工具调用超时配置
    *   **注释**: 注释掉旧的`intelligent_vision_arm_control`配置，避免配置冲突

**技术要点**:

- **架构迁移**: 实现了FC和MCP共存的架构，机械臂功能完全迁移到MCP协议
- **代理问题解决**: 发现并解决了HTTP代理对局域网MCP连接的影响问题
- **工具完整性**: 确保MCP工具完全覆盖了原有Function Calling的所有机械臂功能
- **配置管理**: 通过配置文件实现了灵活的MCP服务器管理

**测试验证**:

- ✅ MCP服务器连接成功（绕过代理设置）
- ✅ 15个vision-arm-control工具全部可用
- ✅ 基础功能测试通过（系统状态、物体检测、机械臂初始化、方向控制）
- ✅ LLM可以通过MCP协议调用所有机械臂功能

---

**V1.5.4 ui(options): 优化设置选项，合并语言和皮肤切换功能**

日期：2025-07-27

**类型**: `ui`
**范围**: `options`

**说明**:

优化了前端设置选项的用户体验，将语言切换和皮肤切换功能合并到一个下拉菜单中，并修复了相关问题。

**变更内容**:

*   **1. 合并设置选项 (`feat`, `ui`)**:
    *   **文件**: `Frontend/app/page.tsx`
    *   **变更**: 将语言切换器和皮肤切换按钮合并到一个名为“选项”的下拉菜单中
    *   **影响**: 简化了界面，使设置选项更加集中

*   **2. 优化下拉菜单交互 (`feat`, `ui`)**:
    *   **文件**: `Frontend/app/page.tsx`, `Frontend/components/language-switcher.tsx`
    *   **变更**: 
        *   为下拉菜单添加了展开和收起的动画效果
        *   为下拉菜单添加了箭头展开和收起的切换动画
        *   修复了皮肤下拉菜单无法收起的问题
    *   **影响**: 提升了下拉菜单的交互体验

*   **3. 优化皮肤切换功能 (`feat`, `ui`)**:
    *   **文件**: `Frontend/app/page.tsx`
    *   **变更**: 
        *   将皮肤名称调整为更具体的描述，如“蓝紫渐变”、“红紫渐变”、“绿黑渐变”
        *   在皮肤选项中添加了圆形的颜色预览
    *   **影响**: 提升了皮肤切换功能的用户体验

*   **4. 修复依赖安装问题 (`fix`, `deps`)**:
    *   **文件**: `Frontend/package.json`, `Frontend/package-lock.json`
    *   **变更**: 修复了`@types/three`依赖安装在错误位置的问题
    *   **影响**: 确保了项目依赖的正确性

---

**V1.5.3 ui(chat): 优化聊天界面UI，新增头像、气泡样式和动画皮肤**

日期：2025-07-27

**类型**: `ui`
**范围**: `chat`

**说明**:

优化了前端聊天界面和3D球体动画的用户体验，新增了LLM头像显示、消息气泡样式调整、动画皮肤切换等功能，提升了界面的美观度和交互性。

**变更内容**:

*   **1. 新增LLM头像显示 (`feat`, `ui`)**:
    *   **文件**: `Frontend/components/chat-interface.tsx`
    *   **变更**: 在LLM回复消息的左侧新增了头像显示，用户消息则在右侧显示"我"的标识
    *   **影响**: 提升了对话的可读性和角色区分度

*   **2. 调整消息气泡样式 (`feat`, `ui`)**:
    *   **文件**: `Frontend/components/chat-interface.tsx`
    *   **变更**: 用户消息气泡调整为黑色背景、白色文字，LLM回复气泡调整为白色背景、黑色文字
    *   **影响**: 增强了对话双方的视觉区分，使界面更加清晰

*   **3. 优化3D球体动画 (`feat`, `ui`)**:
    *   **文件**: `Frontend/components/sphere.tsx`
    *   **变更**: 
        *   加快了`idle`状态下的动画速度，提升视觉反馈
        *   新增了一套`alternative`皮肤，并提供了切换按钮
        *   调整了`alternative`皮肤的`idle`状态颜色，与默认皮肤保持一致
    *   **影响**: 丰富了动画效果，提升了用户体验

*   **4. 新增头像资源 (`feat`, `assets`)**:
    *   **文件**: `Frontend/public/user-default-avatar.png`
    *   **变更**: 新增了LLM头像图片资源
    *   **影响**: 为LLM头像显示提供了必要的图片资源

---

**V1.5.2 docs(structure): 重构文档结构，精简技术文档并新增FC&MCP集成指南**

日期：2025-07-27

**类型**: `docs`
**范围**: `structure`

**说明**:

重构了项目文档结构，删除冗余文档，精简技术文档内容，并新增Function Calling和MCP集成的专门指南文档。

**变更内容**:

*   **1. 删除冗余和过时的文档 (`remove`, `docs`)**:
    *   **文件**: `docs/README.md`, `docs/frontend_localization_summary.md`, `docs/internationalization_implementation.md`, `docs/llm_module.md`, `docs/mcp_tool_call_ui_enhancement.md`, `docs/tts_module.md`
    *   **变更**: 删除了重复、过时或已整合到其他文档中的说明文件
    *   **影响**: 简化文档结构，避免信息冗余和维护负担

*   **2. 重组音频相关文档 (`refactor`, `docs`)**:
    *   **文件**: `docs/audio_fix_report.md` -> `docs/audio/audio_fix_report.md`, `docs/audio_library_comparison_report.md` -> `docs/audio/audio_library_comparison_report.md`
    *   **变更**: 将音频相关文档移动到专门的audio目录下
    *   **影响**: 提高文档组织结构的清晰度

*   **3. 创建模块技术指南目录 (`feat`, `docs`)**:
    *   **文件**: `docs/module_guide/asr_module_technical_guide.md`, `docs/module_guide/llm_module_technical_guide.md`, `docs/module_guide/tts_module_technical_guide.md`
    *   **变更**: 重构并精简了ASR、LLM、TTS三个核心模块的技术文档，删除了大量具体实现细节，保留核心架构和使用要点
    *   **影响**: 文档更加简洁实用，聚焦架构和核心概念，便于快速理解

*   **4. 新增FC&MCP集成指南 (`feat`, `docs`)**:
    *   **文件**: `docs/module_guide/fc_mcp_integration_guide.md`
    *   **变更**: 创建了Function Calling和MCP集成的专门技术文档，包含系统架构、实现原理、使用示例、开发指南等完整内容
    *   **影响**: 为开发者提供了FC&MCP系统的完整技术参考

*   **5. 删除Docker相关内容 (`remove`, `docs`)**:
    *   **文件**: 所有模块技术文档
    *   **变更**: 删除了所有Docker容器化部署相关的配置和说明，替换为RDKX5板卡直接部署方案
    *   **影响**: 文档内容更符合项目实际部署环境

**文档优化效果**:
*   **结构清晰**: 按功能模块重新组织文档结构
*   **内容精简**: 技术文档从1000+行精简到200行左右，提高可读性
*   **实用性强**: 保留核心架构和使用要点，删除冗余实现细节
*   **完整覆盖**: 新增FC&MCP集成指南，补充了重要技术模块的文档空白
*   **环境适配**: 删除Docker内容，专注RDKX5板卡部署环境

---

**V1.5.1 feat(docs): 删除一些无用的文档**

日期：2025-07-27

**类型**: `feat`
**范围**: `docs`

**说明**:

删除一些无用的文档。

---

**V1.5.0 feat(mcp): 优化多轮工具调用体验，实现静默工具调用和智能TTS控制**

日期：2025-07-26

**类型**: `feat`
**范围**: `mcp`

**说明**:

优化了多轮MCP工具调用的用户体验，实现了静默工具调用模式和智能TTS控制机制。解决了多轮工具调用时TTS冲突、内容重复显示等问题，提供更流畅、专业的语音助手交互体验。

**变更内容**:

*   **1. 实现后端智能TTS控制机制 (`feat`, `mcp`)**:
    *   **文件**: `Backend/app/turn_manager.py`
    *   **变更**: 添加工具调用检测标志`has_tool_calls_this_iteration`，修改TTS启动逻辑在工具调用阶段不启动TTS，只在最终总结时启动TTS播放
    *   **影响**: 避免了多轮工具调用时的TTS冲突问题，提供连贯的语音体验

*   **2. 配置LLM静默工具调用模式 (`feat`, `mcp`)**:
    *   **文件**: `Backend/config.yaml`
    *   **变更**: 更新system prompt，要求LLM在多轮工具调用时保持静默，不输出中间说明文字，只在所有工具调用完成后输出完整总结
    *   **影响**: 引导LLM采用更专业的工具调用模式，减少冗余输出

*   **3. 实现前端智能内容保护机制 (`feat`, `mcp`)**:
    *   **文件**: `Frontend/components/app-state-provider.tsx`
    *   **变更**: 添加事件去重机制防止重复处理LLM_FINAL_RESPONSE；实现LLM_CHUNK静默模式，在工具调用阶段不更新内容；添加智能累积机制防止内容覆盖
    *   **影响**: 解决了多轮调用时内容重复显示和覆盖问题

*   **4. 优化前端状态管理 (`feat`, `mcp`)**:
    *   **文件**: `Frontend/lib/app-state-manager.ts`
    *   **变更**: 添加`hasToolCallsInCurrentTurn`字段跟踪当前turn的工具调用状态，优化TOOL_CALL_START事件处理逻辑
    *   **影响**: 提供更精确的工具调用状态管理，支持智能内容保护

*   **5. 代码清理和优化 (`chore`, `mcp`)**:
    *   **文件**: `Frontend/lib/app-state-manager.ts`, `Frontend/components/app-state-provider.tsx`, `Frontend/components/chat-interface.tsx`
    *   **变更**: 移除未使用的状态字段和action，清理调试信息，简化代码逻辑
    *   **影响**: 提高代码可维护性和运行效率

**用户体验改善**:
*   **TTS体验**: 消除了多轮工具调用时的语音冲突，提供连贯的最终总结播放
*   **界面显示**: 解决了内容重复显示和覆盖问题，用户可以看到完整的工具调用过程
*   **交互流程**: 实现了专业的静默工具调用模式，减少冗余信息，提高效率
*   **系统稳定性**: 通过事件去重和智能保护机制，提高了多轮调用的稳定性
*   **代码质量**: 清理冗余代码，提高了系统的可维护性和性能

---

**V1.4.2 feat(mcp): 调整前端处理MCP工具的状态检测，使用后端返回的status字段判断工具的执行状态**

日期：2025-07-26

**类型**: `feat`
**范围**: `mcp`

**说明**:

调整了前端处理MCP工具的状态检测，使用后端返回的status字段判断工具的执行状态。

**变更内容**:

*   **1. 调整前端处理MCP工具的状态检测 (`feat`, `mcp`)**:
    *   **文件**: `Frontend/components/app-state-provider.tsx`
    *   **变更**: 在TOOL_CALL_RESULT分支当中使用后端返回的status字段判断工具的执行状态

*   **2. 调整前端处理MCP工具的状态检测 (`feat`, `mcp`)**:
    *   **文件**: `Frontend/components/tool-call-display.tsx`
    *   **变更**: 在TOOL_CALL_RESULT分支当中使用后端返回的status字段判断工具的执行状态

*   **3. 调整前端应用状态管理器 (`feat`, `mcp`)**:
    *   **文件**: `Frontend/lib/app-state-manager.ts`
    *   **变更**: 新增TOOL_CALL_ERROR分支，用于处理工具调用错误的情况

**影响**:
* 调整了前端处理MCP工具的状态检测，使用后端返回的status字段判断工具的执行状态，解决了工具调用状态检测不准确的问题

---

**V1.4.1 feat(mcp): 调整MCP服务器配置，移除无用的服务器配置**

日期：2025-07-26

**类型**: `feat`
**范围**: `mcp`

**说明**:

调整了MCP服务器配置，移除了无用的服务器配置，只保留了高德地图MCP服务器。

**变更内容**:

*   **1. 移除无用的服务器配置 (`feat`, `mcp`)**:
    *   **文件**: `Backend/utils/mcp/config/mcp_servers.json`
    *   **变更**: 移除了无用的服务器配置，只保留了高德地图MCP服务器

**影响**:
* 移除了无用的服务器配置，只保留了高德地图MCP服务器

---

**V1.4.0 feat(performance): 实现MCP服务器异步加载优化，大幅提升后端启动速度**

日期：2025-07-26

**类型**: `feat`
**范围**: `performance`

**说明**:

实现了MCP服务器连接的异步加载优化，将原本阻塞主服务启动的MCP连接改为后台异步进行，使服务启动时间从48秒大幅缩短到1.3秒，提升37倍性能。通过渐进式功能就绪策略，确保用户可以立即使用基础功能，高级MCP工具在后台加载完成后自动可用。

**变更内容**:

*   **1. 实现MCP异步初始化架构 (`feat`, `performance`)**:
    *   **文件**: `Backend/server.py`
    *   **变更**: 创建`initialize_mcp_tools_async()`异步函数，实现MCP工具的后台加载机制；添加详细的加载时间统计和工具数量统计
    *   **影响**: MCP连接不再阻塞主服务启动，实现真正的异步加载

*   **2. 重构服务启动流程 (`refactor`, `performance`)**:
    *   **文件**: `Backend/server.py`
    *   **变更**: 修改启动逻辑，先加载本地工具确保基础功能立即可用，然后使用`asyncio.create_task()`启动MCP异步任务；优化全局变量管理和logger初始化顺序
    *   **影响**: 服务启动时间从48秒缩短到1.3秒，用户可以立即开始使用

*   **3. 完善异步任务生命周期管理 (`feat`, `performance`)**:
    *   **文件**: `Backend/server.py`
    *   **变更**: 在shutdown事件中添加MCP异步任务的优雅关闭逻辑，确保系统关闭时正确清理异步任务；添加超时和异常处理机制
    *   **影响**: 提供完整的异步任务生命周期管理，确保系统稳定性

*   **4. 实现渐进式功能就绪策略 (`feat`, `architecture`)**:
    *   **文件**: `Backend/server.py`
    *   **变更**: 设计分层功能可用性，基础功能(29个本地工具)立即可用，高级功能(19个MCP工具)后台加载；添加详细的加载进度日志
    *   **影响**: 用户体验大幅改善，无需等待即可开始使用基础功能

**性能提升效果**:
*   **启动速度**: 主服务启动时间从48秒缩短到1.3秒，提升37倍性能
*   **用户可用时间**: 从48秒等待缩短到1.4秒即可开始使用，提升34倍响应速度
*   **资源利用**: MCP连接与ASR模型加载并行进行，充分利用系统资源
*   **功能可用性**: 基础对话功能立即可用，MCP工具25秒后动态加载完成
*   **系统架构**: 建立了可扩展的异步加载架构，为后续优化奠定基础
*   **用户体验**: 服务几乎瞬间启动，支持渐进式功能增强，无感知后台加载

---

**V1.3.1 fix(input): 修复键盘输入模式ASR冲突和语言切换后提示语言不更新问题**

日期：2025-07-26

**类型**: `fix`
**范围**: `input`

**说明**:

修复了键盘输入模式下ASR语音识别结果干扰用户输入的问题，以及语言切换后模式切换提示仍显示原语言的问题。通过优化ASR结果处理逻辑和完善国际化依赖管理，确保用户在不同输入模式和语言设置下都能获得一致、流畅的体验。

**变更内容**:

*   **1. 修复键盘模式ASR结果干扰问题 (`fix`, `input`)**:
    *   **文件**: `Frontend/components/chat-interface.tsx`
    *   **变更**: 移除键盘模式下对ASR结果的处理，只在语音模式且正在监听时处理ASR结果；添加isInputDisabled状态支持，优化输入框禁用逻辑
    *   **影响**: 解决键盘模式下ASR结果覆盖用户输入的问题，确保用户可以正常输入、编辑、删除文字

*   **2. 优化ASR结果状态管理保护 (`fix`, `input`)**:
    *   **文件**: `Frontend/components/app-state-provider.tsx`
    *   **变更**: 在WebSocket消息处理中添加输入模式检查，只有语音模式下才设置ASR结果；切换到键盘模式时自动清除ASR结果；修复变量名冲突问题
    *   **影响**: 在状态管理层面防止ASR结果干扰键盘输入，提供更可靠的输入模式隔离

*   **3. 修复语言切换后提示语言不更新问题 (`fix`, `i18n`)**:
    *   **文件**: `Frontend/components/chat-interface.tsx`
    *   **变更**: 在toggleInputMode的useCallback依赖数组中添加t翻译对象，确保语言切换时函数重新创建使用最新翻译
    *   **影响**: 解决语言切换后模式切换提示仍显示原语言的问题

*   **4. 国际化确认对话框文本 (`fix`, `i18n`)**:
    *   **文件**: `Frontend/lib/i18n/locales/zh.ts`, `Frontend/lib/i18n/locales/en.ts`
    *   **变更**: 添加chat.confirm.switchModeWithInput翻译键，替换硬编码的中文确认对话框文本；更新TypeScript类型定义
    *   **影响**: 实现确认对话框的完整国际化，提供本地化的用户交互体验

**修复效果**:
*   **输入体验**: 键盘模式下用户可以正常输入、编辑、删除文字，不再被ASR结果干扰
*   **模式隔离**: ASR语音识别和键盘输入模式完全隔离，避免相互干扰
*   **国际化一致性**: 语言切换后所有提示文本立即更新为当前选择的语言
*   **用户交互**: 确认对话框支持多语言，提供原生语言体验
*   **系统稳定性**: 修复了编译错误和变量冲突问题，提高代码质量

---

**V1.3.0 feat(i18n): 实现完整的国际化支持和键盘快捷键功能**

日期：2025-07-25

**类型**: `feat`
**范围**: `i18n`

**说明**:

实现了完整的中英文国际化支持，添加了键盘快捷键功能，并修复了翻译缺失问题。建立了可扩展的国际化架构，支持动态语言切换，显著提升了用户体验和产品的国际化水平。

**变更内容**:

*   **1. 建立完整的国际化架构 (`feat`, `i18n`)**:
    *   **文件**: `Frontend/lib/i18n/config.ts`, `Frontend/lib/i18n/context.tsx`
    *   **变更**: 创建了模块化的国际化配置和Context系统，支持语言检测、持久化和动态切换
    *   **影响**: 为应用提供了完整的多语言支持基础架构

*   **2. 实现中英文翻译系统 (`feat`, `i18n`)**:
    *   **文件**: `Frontend/lib/i18n/locales/zh.ts`, `Frontend/lib/i18n/locales/en.ts`, `Frontend/lib/i18n/locales/index.ts`
    *   **变更**: 建立了结构化的翻译文件系统，覆盖所有用户界面文本
    *   **影响**: 提供完整的中英文翻译，支持类型安全的翻译键管理

*   **3. 添加语言切换组件 (`feat`, `ui`)**:
    *   **文件**: `Frontend/components/language-switcher.tsx`
    *   **变更**: 实现了多样式的语言切换器，支持default、compact、icon-only三种模式
    *   **影响**: 提供直观的语言切换界面，支持实时语言切换

*   **4. 实现键盘快捷键功能 (`feat`, `ui`)**:
    *   **文件**: `Frontend/components/chat-interface.tsx`
    *   **变更**: 添加Ctrl+Enter发送消息，Enter换行功能，替换Input为Textarea支持多行输入
    *   **影响**: 提升输入体验，支持多行文本编辑和快捷键操作

*   **5. 集成国际化到应用架构 (`refactor`, `i18n`)**:
    *   **文件**: `Frontend/app/layout.tsx`, `Frontend/app/page.tsx`
    *   **变更**: 在应用根部集成I18nProvider，更新页面组件使用国际化
    *   **影响**: 为整个应用提供统一的国际化支持

*   **6. 实现动态页面元数据 (`feat`, `i18n`)**:
    *   **文件**: `Frontend/components/dynamic-head.tsx`
    *   **变更**: 创建动态Head组件，根据当前语言更新页面标题和meta信息
    *   **影响**: 实现页面标题和描述的多语言支持

*   **7. 更新聊天界面国际化 (`refactor`, `i18n`)**:
    *   **文件**: `Frontend/components/chat-interface.tsx`
    *   **变更**: 将所有硬编码文本替换为翻译键，包括模式切换、状态显示、错误消息等
    *   **影响**: 实现聊天界面的完整国际化，修复翻译缺失问题

*   **8. 优化错误消息国际化 (`fix`, `i18n`)**:
    *   **文件**: `Frontend/components/app-state-provider.tsx`, `Frontend/components/websocket-context.tsx`
    *   **变更**: 将系统错误消息和警告信息替换为翻译键
    *   **影响**: 提供本地化的错误提示，改善用户体验

*   **9. 完善技术文档 (`docs`)**:
    *   **文件**: `docs/internationalization_implementation.md`, `docs/frontend_localization_summary.md`, `docs/i18n_missing_translations_fix.md`, `docs/keyboard_shortcuts_implementation.md`
    *   **变更**: 新增国际化实现指南、本地化总结、翻译修复报告和键盘快捷键文档
    *   **影响**: 为开发者提供完整的国际化开发和维护指南

**改进效果**:
*   **国际化支持**: 实现完整的中英文双语支持，支持动态语言切换
*   **用户体验**: 键盘快捷键提升输入效率，多行文本支持改善编辑体验
*   **界面本地化**: 所有用户可见文本都有对应翻译，提供原生语言体验
*   **架构扩展性**: 建立了可扩展的国际化架构，易于添加新语言支持
*   **开发体验**: 类型安全的翻译系统，编译时错误检测，易于维护

---

**V1.2.0 feat(frontend): 重构前端状态管理架构，新增MCP工具调用UI功能**

日期：2025-07-25

**类型**: `feat`
**范围**: `frontend`

**说明**:

对前端进行了全面的架构重构，引入统一的应用状态管理器，解决了LLM流式响应重复消息问题，并新增了MCP工具调用的收纳/展开UI功能，显著提升了代码可维护性和用户体验。

**变更内容**:

*   **1. 引入统一应用状态管理器 (`feat`, `refactor`)**:
    *   **文件**: `Frontend/lib/app-state-manager.ts`, `Frontend/components/app-state-provider.tsx`
    *   **变更**: 创建了完整的状态管理架构，集中管理WebSocket连接、消息、UI状态等
    *   **影响**: 替代了分散的状态管理，提供类型安全的状态操作和清晰的数据流

*   **2. 修复LLM流式响应重复消息问题 (`fix`, `frontend`)**:
    *   **文件**: `Frontend/components/app-state-provider.tsx`
    *   **变更**: 解决了React闭包状态过时问题，确保LLM chunks正确更新同一个消息气泡
    *   **影响**: 消除了重复消息气泡，实现真正的流式打字效果

*   **3. 重构WebSocket通信层 (`refactor`, `frontend`)**:
    *   **文件**: `Frontend/components/websocket-context.tsx`
    *   **变更**: 基于新状态管理器重构，保持向后兼容的API接口
    *   **影响**: 改进了连接管理和错误处理，简化了消息处理逻辑

*   **4. 重构聊天界面组件 (`refactor`, `ui`)**:
    *   **文件**: `Frontend/components/chat-interface.tsx`
    *   **变更**: 完全重写聊天界面，使用新的状态管理器，简化组件逻辑
    *   **影响**: 保持UI界面不变的同时，大幅简化了状态管理复杂度

*   **5. 新增MCP工具调用UI功能 (`feat`, `ui`)**:
    *   **文件**: `Frontend/components/tool-call-display.tsx`
    *   **变更**: 实现工具调用的收纳/展开功能，状态显示，滚动支持
    *   **影响**: 提供更好的工具调用用户体验，支持长内容查看

*   **6. 更新Sphere动画组件 (`refactor`, `ui`)**:
    *   **文件**: `Frontend/components/sphere.tsx`
    *   **变更**: 迁移到新的状态管理器，改进状态监听逻辑
    *   **影响**: 更准确的动画状态反映，更好的性能表现

*   **7. 更新应用入口配置 (`refactor`, `frontend`)**:
    *   **文件**: `Frontend/app/page.tsx`
    *   **变更**: 集成新的状态管理器Provider
    *   **影响**: 为整个应用提供统一的状态管理支持

*   **8. 完善技术文档 (`docs`)**:
    *   **文件**: `docs/frontend_backend_communication_analysis.md`, `docs/app_state_manager_guide.md`, `docs/mcp_tool_call_ui_enhancement.md`, `docs/migration_completion_report.md`
    *   **变更**: 新增详细的技术分析报告、使用指南和迁移文档
    *   **影响**: 为开发者提供完整的技术文档支持

**改进效果**:
*   **架构优化**: 建立了更加健壮和可维护的前端架构
*   **问题修复**: 彻底解决了LLM流式响应和MCP工具调用的显示问题
*   **用户体验**: MCP工具调用支持收纳/展开，状态清晰，长内容可滚动查看
*   **开发体验**: 统一的状态管理，类型安全，易于调试和扩展
*   **代码质量**: 移除了冗余代码，清理了调试信息，代码更加整洁

---

**V1.1.5 fix(sync): 修复文字显示与音频播放延迟问题，实现实时同步播放**

日期：2025-07-24

**类型**: `fix`
**范围**: `sync`

**说明**:

修复了文字显示与音频播放之间存在2-5秒延迟的问题，通过在LLM流式输出的第一个文字块到达时立即启动TTS任务，实现了文字显示与音频播放的实时同步，显著提升用户体验。

**变更内容**:

*   **1. 优化TTS启动时机 (`fix`, `sync`)**:
    *   **文件**: `Backend/app/turn_manager.py`
    *   **变更**: 在LLM流式输出的第一个内容块到达时立即启动TTS任务，而不是等待LLM完全结束
    *   **影响**: 将文字到音频的延迟从2-5秒降低到0.1-0.5秒

*   **2. 实现实时文字传递 (`fix`, `sync`)**:
    *   **文件**: `Backend/app/turn_manager.py`
    *   **变更**: 将LLM的每个文字块实时放入TTS队列进行处理
    *   **影响**: 实现文字显示与音频播放的近实时同步

*   **3. 优化TTS任务管理 (`fix`, `sync`)**:
    *   **文件**: `Backend/app/turn_manager.py`
    *   **变更**: 添加TTS任务状态跟踪，避免重复启动TTS任务
    *   **影响**: 确保资源正确管理，避免冲突

*   **4. 完善流结束处理 (`fix`, `sync`)**:
    *   **文件**: `Backend/app/turn_manager.py`
    *   **变更**: 在LLM流结束时正确发送TTS结束标记
    *   **影响**: 确保音频播放完整性和资源正确释放

**改进效果**:
*   **用户体验**: 文字显示与音频播放实现近实时同步，延迟从2-5秒降低到0.1-0.5秒
*   **响应性**: 用户在看到文字的同时就能听到语音，交互体验更加自然流畅
*   **稳定性**: 优化了TTS任务管理，避免资源冲突和重复启动问题

---

**V1.1.4.1 fix(deps): 完善后端依赖文件，锁定requirements.txt当中依赖包的版本**

日期：2025-07-24

**类型**: `fix`
**范围**: `deps`

**说明**:

完善了后端依赖文件，锁定requirements.txt当中依赖包的版本，确保项目在不同环境中能够稳定运行。

**变更内容**:

*   **1. 锁定依赖包版本 (`fix`, `deps`)**:
    *   **文件**: `Backend/requirements.txt`
    *   **变更**: 锁定所有依赖包的版本，确保项目在不同环境中能够稳定运行
    *   **影响**: 确保项目在不同环境中能够稳定运行

**改进效果**:
*   **稳定性**: 确保项目在不同环境中能够稳定运行

---

**V1.1.4 fix(audio): 修复RDKX5音频输出问题并完善音频诊断工具**

日期：2025-07-21

**类型**: `fix`
**范围**: `audio`

**说明**:
修复了RDKX5开发板TTS语音无法输出的关键问题。问题根因是PulseAudio默认输出设备错误设置为USB麦克风而非USB喇叭，导致音频流被发送到错误设备。同时创建了完整的音频诊断和修复工具集，提升了音频问题的可诊断性和可维护性。

**变更内容**:

*   **1. 创建音频诊断工具集 (`fix`, `audio`)**:
    *   **文件**: `Backend/test/audio_tests/diagnose_rdkx5_audio.py`
    *   **变更**: 新增全面的音频系统诊断工具，检查PulseAudio状态、ALSA设备、SoundDevice设备识别、系统音量设置和实际播放测试
    *   **影响**: 提供一键诊断音频问题的能力，快速定位问题根因

*   **2. 创建录音功能测试工具 (`fix`, `audio`)**:
    *   **文件**: `Backend/test/audio_tests/test_recording.py`, `Backend/test/audio_tests/test_recording_simple.py`
    *   **变更**: 新增录音功能测试工具，支持简单录音测试、连续录音测试和音频信号分析
    *   **影响**: 确保ASR录音功能正常，验证UGREEN麦克风工作状态

*   **3. 创建一键音频修复脚本 (`fix`, `audio`)**:
    *   **文件**: `Backend/scripts/fix_rdkx5_audio.sh`
    *   **变更**: 新增自动化音频修复脚本，包含设备检测、配置修复、音量设置、持久化配置和功能测试
    *   **影响**: 提供快速修复RDKX5音频问题的解决方案，支持一键修复和配置持久化

*   **4. 完善音频问题修复文档 (`docs`, `audio`)**:
    *   **文件**: `docs/rdkx5_audio_fix_report.md`
    *   **变更**: 新增详细的音频问题修复报告，包含问题分析、修复过程、故障排除指南和预防措施
    *   **影响**: 提供完整的音频问题知识库，便于问题重现和修复

*   **5. 更新项目文档 (`docs`, `audio`)**:
    *   **文件**: `Backend/README.md`
    *   **变更**: 在部署指南中添加音频设备配置章节，包含RDKX5音频配置说明、快速修复指南和诊断工具使用方法
    *   **影响**: 提升项目文档完整性，为用户提供音频配置指导

**修复效果**:
*   **功能性**: RDKX5 TTS语音输出功能完全恢复，用户可正常听到AI助手回复
*   **稳定性**: USB喇叭(UACDemoV1.0)正确设置为默认输出设备，USB麦克风(UGREEN CM564)正确设置为默认输入设备
*   **可诊断性**: 提供完整的音频诊断工具链，支持快速问题定位和状态检查
*   **可维护性**: 创建自动化修复脚本和持久化配置，防止问题重现
*   **可观测性**: 录音测试显示强音频信号(最大振幅: 15532)，确认麦克风工作正常

**技术细节**:
*   问题根因: PulseAudio默认输出设备错误指向USB麦克风而非USB喇叭
*   修复方案: `pactl set-default-sink alsa_output.usb-Jieli_Technology_UACDemoV1.0_*`
*   设备状态: USB喇叭从SUSPENDED变为RUNNING，USB麦克风保持正确的输入设备角色
*   配置持久化: 创建`~/.config/pulse/default.pa`确保重启后配置保持
*   工具覆盖: 诊断工具、修复脚本、测试工具、文档指南四位一体的解决方案

---

**V1.1.3 fix(mcp): 修复MCP工具schema格式错误和连接时序问题、更新系统提示词**

日期：2025-07-21

**类型**: `fix`
**范围**: `mcp`

**说明**:
修复了MCP支持分支中的两个关键问题：1) MCP工具schema格式不符合火山引擎API规范导致的"MissingParameter"错误；2) MCP连接时序问题导致工具未正确加载到LLM中。这些修复确保了MCP工具能够被LLM正确识别和调用。同时，更新了系统提示词，使其更加符合MCP工具调用的需求。

**变更内容**:

*   **1. 修复MCP工具schema格式规范 (`fix`, `mcp`)**:
    *   **文件**: `Backend/utils/mcp/client/mcp_function_adapter.py`
    *   **变更**: 将MCP工具schema格式从简单对象修正为符合火山引擎API规范的格式，添加必需的`"type": "function"`和`"function"`包装层
    *   **影响**: 解决了LLM请求中"missing tools.function parameter"的400错误

*   **2. 优化MCP连接同步机制 (`fix`, `mcp`)**:
    *   **文件**: `Backend/utils/mcp/client/mcp_client_manager.py`
    *   **变更**: 
        - 分离连接完成事件和终止信号事件，避免事件冲突
        - 添加`connection_events`字典专门管理连接完成状态
        - 在`add_mcp_server`中等待连接完成，确保工具正确加载
    *   **影响**: 确保MCP工具在获取schema前已完全加载

*   **3. 增加MCP连接等待时间 (`fix`, `mcp`)**:
    *   **文件**: `Backend/server.py`
    *   **变更**: 在获取增强工具schema前添加10秒等待时间，并增加详细的工具统计日志
    *   **影响**: 给MCP服务器连接提供充足时间，确保所有工具都能被正确加载

*   **4. 系统提示词优化 (`fix`, `mcp`)**:
    *   **文件**: `Backend/config.yaml`
    *   **变更**: 更新系统提示词以更好地支持MCP工具调用
    *   **影响**: 提升LLM对MCP工具的识别和使用能力

**改进效果**:
*   **兼容性**: 工具schema格式完全符合火山引擎API规范，消除了400错误
*   **稳定性**: MCP连接时序问题得到解决，工具加载成功率达到100%
*   **功能性**: 验证了47个工具schema全部有效，包括18个MCP工具和29个本地工具
*   **可用性**: MCP天气工具`mcp:amap-maps:maps_weather`现在可以被LLM正确识别和调用
*   **可观测性**: 增加了详细的工具加载统计日志，便于问题诊断

**技术细节**:
*   修复前工具schema格式: `{"name": "...", "description": "...", "parameters": {...}}`
*   修复后工具schema格式: `{"type": "function", "function": {"name": "...", "description": "...", "parameters": {...}}}`
*   连接等待机制: 从异步队列模式改为同步等待模式，确保连接完成后再继续
*   工具统计: 本地工具29个，MCP工具18个，总计47个工具全部可用

---

**V1.1.2 fix(mcp): 修复MCP模块导入路径错误导致的运行时警告问题**

日期：2025-07-21

**类型**: `fix`
**范围**: `mcp`

**说明**:
修复了MCP支持分支中因导入路径错误导致的运行时警告问题。错误信息为"No module named 'utils.mcp.function_call'"，这是由于MCP模块中使用了错误的相对导入路径和不完整的模块路径导致的。

**变更内容**:

*   **1. 修复MCP客户端模块内部导入路径 (`fix`, `mcp`)**:
    *   **文件**: `Backend/utils/mcp/client/mcp_function_adapter.py`
    *   **变更**: 将相对导入路径从 `..function_call` 修正为 `...function_call`，正确引用Function Call模块
    
*   **2. 修复MCP集成模块导入路径 (`fix`, `mcp`)**:
    *   **文件**: `Backend/utils/mcp/client/mcp_integration.py`
    *   **变更**: 将相对导入路径从 `..function_call` 修正为 `...function_call`，确保正确导入FunctionRegistry

*   **3. 修复服务器主模块MCP导入路径 (`fix`, `mcp`)**:
    *   **文件**: `Backend/server.py`
    *   **变更**: 将导入路径从 `utils.mcp.mcp_function_adapter` 修正为 `utils.mcp.client.mcp_function_adapter`，包含完整的模块路径

*   **4. 修复回合管理器MCP导入路径 (`fix`, `mcp`)**:
    *   **文件**: `Backend/app/turn_manager.py`
    *   **变更**: 将导入路径从 `utils.mcp.mcp_function_adapter` 修正为 `utils.mcp.client.mcp_function_adapter`，确保正确导入兼容函数

**改进效果**:
*   **稳定性**: 消除了运行时导入警告，确保MCP功能能够正常初始化和运行
*   **兼容性**: 保持了MCP与原有Function Call系统的完全向后兼容性
*   **功能性**: 验证了29个工具schema的正常加载，MCP增强功能完全可用
*   **代码质量**: 修正了模块间的依赖关系，提高了代码的可维护性

---

**V1.1.1 fix(deps): 修复因logging和dotenv包冲突导致的依赖安装失败问题**

日期：2025-07-20

**类型**: `fix`
**范围**: `deps`

**说明**:
修复了在安装后端依赖时，因 `logging` 和 `dotenv` 包与Python内置标准库冲突而导致的 `SyntaxError` 问题。该错误是由于 `requirements.txt` 中指定的第三方 `logging` 包版本过旧，使用了与Python 3不兼容的语法，从而导致 `pip install` 进程中断。

**变更内容**:

*   **1. 移除冲突的依赖包 (`fix`, `deps`)**:
    *   **文件**: `Backend/requirements.txt`
    *   **变更**: 删除了 `logging>=*******` 和 `dotenv>=0.99` 这两行。现在项目将使用Python内置的 `logging` 和 `dotenv` 标准库，解决了安装冲突。

**改进效果**:
*   **稳定性**: 解决了后端依赖安装失败的核心问题，确保项目可以在Python 3环境中被正确、稳定地初始化。
*   **依赖清理**: 移除了一个不必要且有害的第三方包，使项目的依赖关系更加清晰和健壮。

---

**V1.1.0 refactor(mcp): 重构MCP模块为模块化架构，完善外部服务器集成和配置管理**

日期：2025-07-18

**类型**: `refactor`
**范围**: `mcp`

**说明**:
对ASR-LLM-TTS项目的MCP模块进行了全面重构，采用模块化架构设计，将客户端、服务器、配置和工具分离到独立目录。新增了外部MCP服务器集成能力，支持Context7文档检索、ModelScope图像生成、高德地图等多个第三方MCP服务器，并提供了完整的配置管理和测试工具。

**变更内容**:

*   **1. 重构MCP模块架构 (`refactor`, `structure`)**:
    *   **新增**: `Backend/utils/mcp/client/`, `Backend/utils/mcp/server/`, `Backend/utils/mcp/config/`, `Backend/utils/mcp/tools/`
    *   **变更**: 将原有单一目录结构重构为模块化架构，实现职责分离和清晰的代码组织

*   **2. 实现外部MCP服务器集成 (`feat`, `integration`)**:
    *   **新增**: `Backend/utils/mcp/client/mcp_client_manager.py`, `Backend/utils/mcp/client/mcp_integration.py`
    *   **变更**: 新增对外部MCP服务器的连接和集成能力，支持SSE、Streamable HTTP等多种传输方式

*   **3. 建立Function Calling适配器 (`feat`, `adapter`)**:
    *   **新增**: `Backend/utils/mcp/client/mcp_function_adapter.py`
    *   **变更**: 实现MCP工具与现有Function Calling系统的无缝集成适配器

*   **4. 完善配置管理系统 (`feat`, `config`)**:
    *   **新增**: `Backend/utils/mcp/config/mcp_config_manager.py`, `Backend/utils/mcp/config/mcp_servers.json`
    *   **变更**: 提供完整的MCP服务器配置管理，支持添加、删除、启用/禁用服务器

*   **5. 集成多个第三方MCP服务器 (`feat`, `servers`)**:
    *   **新增**: Context7文档检索、ModelScope图像生成、高德地图服务等4个外部MCP服务器配置
    *   **变更**: 验证并集成了18个外部工具，包括文档检索、AI图像生成、地图服务等功能

*   **6. 创建统一工具启动器 (`feat`, `launcher`)**:
    *   **新增**: `Backend/mcp_tools.py`
    *   **删除**: `Backend/start_mcp.py`
    *   **变更**: 重构启动器为功能更完整的工具管理器，支持配置管理、测试、服务器启动等操作

*   **7. 实现MCP服务器测试工具 (`feat`, `testing`)**:
    *   **新增**: `Backend/utils/mcp/tools/final_mcp_test.py`
    *   **变更**: 提供并发测试所有活跃MCP服务器的能力，包含连接验证和工具发现

*   **8. 优化主程序MCP集成 (`refactor`, `integration`)**:
    *   **修改**: `Backend/server.py`, `Backend/app/turn_manager.py`
    *   **变更**: 更新主程序以使用新的模块化MCP架构，确保向后兼容性

*   **9. 更新项目依赖 (`chore`, `dependencies`)**:
    *   **修改**: `Backend/requirements.txt`
    *   **变更**: 添加MCP相关依赖包，确保外部服务器集成功能正常运行

*   **10. 完善模块文档 (`docs`, `documentation`)**:
    *   **修改**: `Backend/utils/mcp/README.md`, `Backend/utils/mcp/__init__.py`
    *   **变更**: 更新文档以反映新的模块化架构和外部服务器集成功能

*   **11. 清理冗余文件 (`remove`, `cleanup`)**:
    *   **删除**: `Backend/utils/mcp/mcp_prototype.py`, `Backend/utils/mcp/setup_mcp_universal.py`, `Backend/utils/mcp/setup_mcp_wsl2.py`
    *   **变更**: 移除不再需要的原型和安装脚本，简化项目结构

**技术特性**:
*   **模块化架构**: 客户端、服务器、配置、工具四大模块独立管理
*   **多传输支持**: SSE、Streamable HTTP、stdio等多种MCP传输方式
*   **外部服务集成**: 支持连接和使用第三方MCP服务器的工具
*   **配置管理**: 完整的服务器配置增删改查功能
*   **并发测试**: 高效的多服务器并发连接测试能力

**集成的外部服务**:
*   **Context7**: 文档检索和代码库上下文查询 (2个工具)
*   **ModelScope图像生成**: AI图像生成服务 (2个工具)  
*   **高德地图**: 地图服务和路径规划 (12个工具)
*   **总计**: 18个外部工具成功集成

**改进效果**:
*   **架构清晰**: 模块化设计使代码结构更清晰，易于维护和扩展
*   **功能丰富**: 外部服务器集成大幅扩展了系统可用工具数量
*   **管理便捷**: 统一的配置管理和测试工具提升了运维效率
*   **标准兼容**: 完全符合MCP协议标准，具备良好的互操作性

**使用方法**:
```bash
# 查看所有MCP服务器
python mcp_tools.py config list

# 测试所有活跃服务器  
python mcp_tools.py test

# 管理服务器配置
python mcp_tools.py config enable/disable <server_name>
```

---

**V1.0.0 feat(mcp): 集成MCP协议支持，实现Function Calling到MCP的迁移基础架构**

日期：2025-07-18

**类型**: `feat`
**范围**: `mcp`

**说明**:
为ASR-LLM-TTS语音对话助手项目集成了Model Context Protocol (MCP) 支持，建立了从现有Function Calling系统向标准化MCP协议迁移的完整基础架构。此次更新引入了环境自适应、跨平台兼容的MCP服务器实现，支持WSL2开发环境和Linux板卡生产环境。

**变更内容**:

*   **1. 新增MCP模块架构 (`feat`, `mcp`)**:
    *   **文件**: `Backend/utils/mcp/` (新增目录)
    *   **变更**: 创建完整的MCP模块化架构，包含环境检测、服务器实现、配置管理等核心组件

*   **2. 实现环境自适应检测 (`feat`, `environment`)**:
    *   **文件**: `Backend/utils/mcp/mcp_environment.py`
    *   **变更**: 实现WSL2、Linux板卡、Docker等环境的自动检测和功能适配，支持音频/硬件功能的智能启用/禁用

*   **3. 创建MCP原型服务器 (`feat`, `server`)**:
    *   **文件**: `Backend/utils/mcp/mcp_prototype.py`
    *   **变更**: 实现基于FastMCP 2.10.5的原型服务器，包含基础工具、系统信息查询、异步操作演示等功能

*   **4. 实现核心工具MCP服务器 (`feat`, `tools`)**:
    *   **文件**: `Backend/utils/mcp/mcp_core_tools.py`
    *   **变更**: 将现有Function Calling核心工具迁移到MCP格式，包含计算器、系统状态、文本处理、时间日期等工具

*   **5. 建立MCP配置管理 (`feat`, `config`)**:
    *   **文件**: `Backend/utils/mcp/mcp_config.yaml`
    *   **变更**: 创建统一的YAML配置文件，支持环境特定配置、模块化功能开关、安全策略等

*   **6. 提供环境安装脚本 (`feat`, `setup`)**:
    *   **文件**: `Backend/utils/mcp/setup_mcp_universal.py`, `Backend/utils/mcp/setup_mcp_wsl2.py`
    *   **变更**: 实现通用和WSL2专用的MCP环境自动安装脚本，包含依赖检查、功能验证、环境报告等

*   **7. 创建便捷启动工具 (`feat`, `launcher`)**:
    *   **文件**: `Backend/start_mcp.py`
    *   **变更**: 提供统一的MCP服务器启动入口，支持原型服务器、核心工具服务器、环境安装等操作

*   **8. 优化音频模块兼容性 (`fix`, `audio`)**:
    *   **文件**: `Backend/utils/__init__.py`, `Backend/utils/function_call/function_tools_impl.py`
    *   **变更**: 修复WSL2环境中音频库导入问题，实现条件导入和占位符类，确保在无音频支持环境中的正常运行

*   **9. 完善MCP模块文档 (`docs`, `mcp`)**:
    *   **文件**: `Backend/utils/mcp/README.md`, `Backend/utils/mcp/__init__.py`
    *   **变更**: 提供完整的MCP模块使用文档、API说明、环境配置指南和故障排除手册

**技术特性**:
*   **跨平台兼容**: 支持WSL2开发环境和Linux板卡生产环境的无缝切换
*   **智能适配**: 自动检测环境能力，智能启用/禁用音频和硬件功能
*   **标准化协议**: 基于MCP 1.12.0标准协议，使用FastMCP 2.10.5实现
*   **优雅退出**: 实现完善的信号处理和优雅关闭机制
*   **模块化设计**: 支持工具分组、资源管理、配置热加载等高级特性

**改进效果**:
*   **标准化**: 从自定义Function Calling向标准MCP协议迁移，提高互操作性
*   **可维护性**: 模块化架构和统一配置管理，降低维护复杂度
*   **开发效率**: 环境自适应和便捷工具，提升开发和部署效率
*   **扩展性**: 为后续LLM集成和工具扩展奠定坚实基础

**下一步计划**:
*   分析现有Function Calling系统，制定详细迁移策略
*   逐步迁移音频控制、远程服务等复杂工具
*   集成LLM客户端，实现完整的MCP工作流

---

从V1.0.0开始，版本信息开始进行规范化记录，记录内容包括：
- 版本号
- 日期
- 类型
- 范围
- 说明
- 变更内容

---

# 2025-06-24
feat(run.sh): 优化前端启动脚本，实现智能构建与生产模式运行

本次修改旨在彻底解决前端页面加载缓慢的问题，并兼顾开发效率与部署性能。通过对根目录下的 `run.sh` 脚本进行智能化改造，实现了“一次构建，多次运行”的最佳实践。

### 核心改进 (`run.sh`):

-   **⚡️ 智能构建**: 脚本在启动时会自动检测 `Frontend/.next` 目录是否存在。如果构建产物已存在，将直接跳过耗时的 `npm run build` 步骤，实现秒级启动。
-   **⚙️ 强制重建**: 引入 `--rebuild` 命令行参数 (`./run.sh --rebuild`)，允许用户在需要时强制清除旧的构建产物并重新构建。
-   **🚀 生产模式运行**: 前端服务默认通过 `npm run start` 以高性能的生产模式启动，确保了流畅的页面加载和运行体验。

### 解决的问题:

-   **性能问题**: 根除了在开发模式下首次访问页面时需要长达15-20秒编译时间的问题。
-   **开发便利性**: 通过 `--rebuild` 参数和脚本内的逻辑判断，平衡了快速启动和代码更新后的重新构建需求。
-   **配置警告**: 由于不再默认使用开发模式，Next.js 15 在开发模式下的 `allowedDevOrigins` 跨域警告也一并规避。

---

# 2025-06-22
doc(docs): 删除了一些无用文档，重命名了一些文档的名称

本次提交删除了一些无用的文档，重命名了一些文档的名称。

---

# 2025-06-22
fix(audio): 修复TTS音量记录缺失导致智能音量调整不准确的问题

### 问题背景
在智能音量调整逻辑中，AudioDeviceManager需要比较TTS和音乐的音量来决定是否降低背景音乐：
- 如果音乐音量 ≤ TTS音量 → 保持音乐音量不变
- 如果音乐音量 > TTS音量 → 降低音乐音量到合适的背景音量

但是TTS播放器的音量变化没有被正确记录到AudioDeviceManager中，导致智能比较总是基于默认值(0.5)进行，而不是用户实际设置的TTS音量。

### 解决方案

#### 1. 修复AudioDeviceManager缩进问题
- **文件**: `Backend/utils/audio_device_manager.py`
- **问题**: `update_music_player_volume`方法缩进错误，不在类内部
- **修复**: 正确缩进，使其成为AudioDeviceManager类的方法

#### 2. 增强智能音量调整逻辑
- **文件**: `Backend/utils/audio_device_manager.py`
- **改进**: 
  - 在TTS开始前保存当前音乐音量，结束后精确恢复
  - 优化音量比较和调整算法：`min(0.3, tts_volume * 0.6)`
  - 改进日志输出，显示具体的音量数值和调整原因

#### 3. 确保TTS音量变化被正确记录
- **文件**: `Backend/utils/function_call/function_call_tools.py`
- **添加**: 导入AudioDeviceManager和AudioDeviceType
- **修复**: 在所有TTS音量设置函数中添加音量记录逻辑：
  - `tts_player_set_volume_func`
  - `tts_player_increase_volume_func` 
  - `tts_player_decrease_volume_func`

#### 4. AudioNewPlayer初始化时记录音量
- **文件**: `Backend/utils/audio_new_player.py`
- **添加**: 
  - 初始化时记录初始音量到AudioDeviceManager
  - set_volume方法中同步更新AudioDeviceManager的音量记录

### 技术改进
- **准确性提升**: TTS音量变化现在能被实时跟踪，智能音量调整基于真实音量而非默认值
- **用户体验优化**: 音量调整更加智能，避免不必要的音量降低
- **系统稳定性**: 修复了缩进错误，确保function-call音量调整功能正常工作

### 测试验证
- 验证了TTS和音乐播放器的音量调整功能
- 确认智能音量调整逻辑按预期工作
- 测试了各种音量设置场景下的行为一致性

---

# 2025-06-22
feat(audio): 全面重构音频系统，统一输出并增强健壮性

本次提交对后端音频系统进行了一次全面的重构和加固，核心目标是解决底层库冲突、提升系统稳定性和代码现代化。

### 1. 解决库间冲突与架构统一

问题演进与解决方案分为两个关键阶段：

**阶段一：解决 Pygame 与 SoundDevice 的直接冲突**
- **问题**: `pygame` 无法通过名称找到音频设备，在通过 `pactl` 将其设置为系统默认设备后，又导致 `sounddevice` 报 `Device unavailable` 错误。
- **解决方案**: 通过修改 `Backend/config.yaml`，将 `sounddevice` 的 `device_keyword` 设置为 `"pulse"`。这使得 `pygame` (通过系统默认) 和 `sounddevice` (通过显式指定) 都将音频流输出到 PulseAudio 服务器，成功解决了设备资源抢占的问题，实现了两者共存。

**阶段二：架构统一与代码重构**
- **动机**: 既然所有音频流都可以通过 `sounddevice` (经由 `AudioNewPlayer`) 统一管理，保留 `pygame.mixer` 不仅增加了不必要的复杂性，也使音量避让等逻辑难以统一。
- **refactor(MusicPlayer)**: 基于上述考虑，对 `MusicPlayer` 进行了彻底重构，完全移除了 `pygame.mixer` 的依赖，使其转而使用项目内共享的 `AudioNewPlayer` 实例来播放音乐。
- **结果**:
    1.  **架构简化**: 移除了一个底层音频库，降低了复杂性和潜在的维护成本。
    2.  **功能统一**: 所有音频（TTS和音乐）现在都通过唯一的 `AudioNewPlayer` 实例输出，共享同一套设备管理、音量控制和音频避让逻辑。

### 2. 提升系统健壮性

-   **fix(Graceful-Shutdown)**: 实现了优雅停机，结合 `run.sh` 等待机制和 `server.py` 中 `audio_player.close()` 的主动调用，根除了因服务快速重启导致的 `Device unavailable` 错误。
-   **feat(Volume-Control)**: 服务启动时动态查找并设置系统主音量，避免了因系统静音或控制器名称不匹配导致的无声问题。
-   **feat(AudioDevice)**: 修正了 `AudioNewPlayer` 的设备选择逻辑，确保其优先使用 `config.yaml` 中用户指定的 `device_keyword`。

### 3. 代码现代化

-   **refactor(FastAPI)**: 使用 `lifespan` 上下文管理器替代了已废弃的 `@app.on_event`。

### 4. 文档与测试

-   **docs**: 新增了详尽的 `audio_library_comparison_report.md`，记录了整个音频问题的诊断和解决全过程。
-   **test**: 添加了多个用于调试音频问题的辅助脚本。

---

# 2025-06-22
fix(backend): 解决快速重启导致音频设备不可用的问题

本次更新通过对服务启停脚本和服务端代码的双重加固，彻底解决了在快速、反复重启服务时，因音频设备未能及时释放而导致的 `sounddevice.PortAudioError: Device unavailable` 竞争条件错误。

### 1. 问题根源
- 当通过 `Ctrl+C` 停止 `run.sh` 脚本时，旧的后端进程在被 `pkill` 后，可能来不及完全释放其占用的音频硬件资源。
- 如果此时立刻重新运行脚本，新的后端进程会因为无法获取被旧进程“幽灵占用”的设备而启动失败。

### 2. 解决方案 (双重保障)

#### a. 增强 `run.sh` 脚本的清理逻辑
- **修改内容**: 重构了 `run.sh` 中的 `cleanup` 函数。在发送停止信号后，增加了一个循环等待机制。
- **机制**: 脚本现在会持续检查后端进程是否已完全消失，并设置了一个超时时间。只有在确认进程已退出后，脚本才会清理完毕，否则将进行强制清理。
- **效果**: 确保了在脚本层面给予了应用足够的“优雅关闭”时间。

#### b. 实现服务端应用的优雅关闭
- **修改内容**:
    1. 在 `Backend/utils/audio_new_player.py` 的 `AudioNewPlayer` 类中，新增了一个公开的 `close()` 方法，用于显式关闭 `sounddevice` 的底层流并释放所有相关资源。
    2. 在 `Backend/server.py` 的 `lifespan` 管理器的应用关闭逻辑部分 (`yield` 之后)，明确调用了共享播放器实例的 `audio_player.close()` 方法。
- **效果**: 使得后端服务在接收到`SIGINT`/`SIGTERM`等关闭信号时，能够主动、干净地释放其占用的音频设备，而不仅仅是依赖进程被动终止。

### 3. 最终效果
- 通过“**脚本耐心等待**”和“**应用主动释放**”的双重保障，彻底根除了因服务快速重启而引发的音频设备竞争问题，极大地提升了开发和部署过程中的稳定性和可靠性。

---

# 2025-06-22
feat(backend): 增强音频系统鲁棒性并实现代码现代化

本次更新通过一系列的修复和重构，显著提升了后端音频系统的稳定性和健壮性，并使代码库与现代 FastAPI 最佳实践保持一致。所有修改均由 NexusCore 模式协调，Code 模式执行完成。

### 1. 修复：动态设置系统主音量
- **问题**：服务启动时，因写死 `amixer` 控制器名称为 `'Master'`，在某些系统上导致设置音量失败，可能使整个系统处于静音状态。
- **解决方案**：修改 `Backend/server.py`，在应用启动时动态查找可用的主音量控制器（如 'Master', 'PCM', 'Speaker' 等），并将其音量设置为 100%。增加了完善的错误处理，即使命令失败也不会使服务崩溃。
- **效果**：确保了服务每次启动后，系统音量都处于可用状态，从根本上解决了因系统静音导致程序无声的问题。

### 2. refactor(backend): 统一音频输出通道
- **问题**：`MusicPlayer` 使用的 `pygame.mixer` 与 TTS 使用的 `sounddevice` 库形成两个独立的音频输出通道，存在底层库冲突和资源抢占的风险。
- **解决方案**：重构了 `Backend/utils/function_call/function_tools_impl.py` 中的 `MusicPlayer` 类。完全移除了 `pygame.mixer` 依赖，使其转而使用项目内共享的 `AudioNewPlayer` 实例来统一播放所有音频（包括音乐）。
- **效果**：彻底消除了底层音频库的冲突隐患，统一了音频出口，使音频管理架构更加清晰和健壮。现在所有声音（TTS和音乐）都遵循 `AudioDeviceManager` 的统一调度和音量避让逻辑。

### 3. feat(backend): 修正音频设备选择逻辑
- **问题**：`AudioNewPlayer` 在选择输出设备时，错误地优先选择 `PulseAudio` 设备，忽略了 `config.yaml` 中用户指定的 `device_keyword`。
- **解决方案**：重构了 `Backend/utils/audio_new_player.py` 中的设备选择逻辑，现在的查找顺序为：`device_keyword` -> `PulseAudio` -> 系统默认。
- **效果**：系统现在会正确地、优先地使用用户在配置文件中指定的输出设备，增强了系统的可配置性和在不同硬件环境下的适应性。

### 4. refactor(fastapi): 使用 `lifespan` 替代废弃的 `on_event`
- **问题**：FastAPI 服务启动时会因使用废弃的 `@app.on_event` 装饰器而产生 `DeprecationWarning` 警告。
- **解决方案**：在 `Backend/server.py` 中，使用官方推荐的 `lifespan` 上下文管理器来处理应用的启动和关闭事件，完全替代了旧的 `on_event` 写法。
- **效果**：消除了启动警告，使代码更加现代化，并遵循了 FastAPI 的最佳实践。

---

# 2025-06-22
feat(backend-startup): 在服务启动时自动将系统主音量设置为最大

为了确保语音助手在每次启动后都能以清晰、响亮的声音进行播报，本次修改在后端服务的启动流程中增加了自动调节系统音量的功能。

1.  **问题背景**:
    *   在某些情况下，系统或硬件的音量可能在重启后被重置为较低的水平，影响用户对语音助手的听觉体验。

2.  **解决方案**:
    *   通过 **NexusCore** 模式的协调，将编码任务委派给 **Code** 模式，并成功执行。
    *   在 `Backend/server.py` 文件中，于 `lifespan` 上下文管理器的启动逻辑部分（`yield` 之前）新增了一段代码。
    *   该代码使用 Python 的 `subprocess` 模块来执行系统命令 `amixer set Master 100%`。
    *   为了保证服务的健壮性，整个命令执行过程被包裹在 `try...except` 块中，并增加了详细的日志记录。即使在目标系统上 `amixer` 命令不可用或执行失败，后端服务也只会记录一条警告日志而不会崩溃。

**技术实现亮点**:
- ✅ **自动化**: 实现了在应用启动时自动执行系统级操作，提升了用户体验的一致性。
- ✅ **健壮性**: 通过完善的错误处理和日志记录，确保了即使音量调节失败，主服务也能正常启动和运行。

**用户体验提升**:
- ✅ 确保了每次启动服务后，系统的音量都处于最大状态，用户无需手动调节即可清晰地听到语音助手的反馈。

**问题解决**:
- ✅ 解决了因系统重启或外部因素导致音量过低，从而影响语音交互效果的问题。

---

# 2025-06-22
refactor(backend-fastapi): 使用 lifespan 替代废弃的 on_event 处理器

本次重构旨在解决 FastAPI 服务启动时产生的 `DeprecationWarning` 警告，通过采用官方推荐的 `lifespan` 上下文管理器来替代旧的 `@app.on_event` 装饰器，使后端代码与 FastAPI 的最佳实践保持一致。

1.  **问题定位**:
    *   在 `Backend/server.py` 文件中，`@app.on_event("startup")` 和 `@app.on_event("shutdown")` 装饰器已被 FastAPI 废弃。

2.  **解决方案**:
    *   通过 **NexusCore** 模式的协调，将代码重构任务委派给 **Code** 模式，并成功执行。
    *   在 `Backend/server.py` 中引入了 `asynccontextmanager`。
    *   创建了一个新的 `lifespan` 异步函数，将原有的启动和关闭逻辑分别迁移到了 `yield` 语句的前后部分。
    *   在 FastAPI 应用实例化时通过 `app = FastAPI(lifespan=lifespan)` 的方式注册了新的事件处理器。
    *   彻底移除了旧的 `startup_event` 和 `shutdown_event` 函数及其装饰器，使代码更加简洁。

**技术实现亮点**:
- ✅ **代码现代化**: 将应用生命周期事件管理更新为 FastAPI 推荐的 `lifespan` 模式，遵循了框架的演进方向。
- ✅ **结构优化**: 将分散的启动和关闭逻辑集中到单一的 `lifespan` 上下文管理器中，提高了代码的可读性和内聚性。
- ✅ **警告消除**: 成功消除了服务启动时的 `DeprecationWarning`，使得日志输出更干净。

**问题解决**:
- ✅ 修复了因使用废弃的 `@app.on_event` 装饰器而导致的 `DeprecationWarning` 警告。

---

# 2025-06-22
feat(backend-audio): 修复音频输出设备选择逻辑，使其正确遵循配置

本次修改修复了后端音频播放器在选择输出设备时的核心逻辑问题，确保系统行为与用户配置完全一致。

1.  **问题定位**:
    *   在 `Backend/utils/audio_new_player.py` 文件中，`_select_output_device` 方法的实现忽略了来自 `config.yaml` 的 `audio_player.device_keyword` 配置项。
    *   代码逻辑错误地强制优先选择 `PulseAudio` 设备，导致用户无法通过配置文件指定期望的扬声器设备。

2.  **解决方案**:
    *   通过 **NexusCore** 模式的协调，将代码修改任务委派给 **Code** 模式，并成功执行。
    *   重构了 `_select_output_device` 方法的设备查找顺序，使其更加健壮和符合预期：
        1.  **优先使用 `device_keyword`**: 首先会根据配置文件中指定的关键词（如 "UACDemoV1.0"）精确查找并选择输出设备。
        2.  **备选 PulseAudio**: 如果根据关键词找不到设备，系统会回退尝试查找并使用 `PulseAudio` 设备，以保证多应用下的兼容性。
        3.  **最终回退至默认**: 如果以上两步均失败，系统将安全地使用系统默认的输出设备。

**技术实现亮点**:
- ✅ **遵循配置**: 修复了代码逻辑与配置文件意图不符的 bug，现在系统可以精确地使用用户指定的音频输出设备。
- ✅ **分层查找**: 实现了更智能的设备查找策略（关键词 -> PulseAudio -> 默认），提升了系统的鲁棒性和环境适应性。
- ✅ **模式协作**: 成功演示了 NexusCore (协调) 与 Code (执行) 模式之间的有效协作流程。

**用户体验提升**:
- ✅ 用户现在可以完全信赖 `config.yaml` 中的 `device_keyword` 配置来指定音频输出设备，简化了在不同硬件环境下的部署和调试过程。

**问题解决**:
- ✅ 彻底解决了音频播放器不遵循 `device_keyword` 配置的问题。

---

# 2025-06-15
feat(frontend-chat): 修复LLM回复字符丢失、工具调用显示错位及WebSocket动态连接问题

本次修改集中解决前端聊天界面在与后端LLM服务交互时遇到的多个核心问题，旨在提升数据显示的准确性、完整性以及连接的鲁棒性。

1.  **LLM回复字符丢失问题修复** ([`Frontend/components/chat-interface.tsx`](Frontend/components/chat-interface.tsx:0)):
    *   **问题定位**: 前端未处理后端发送的 `LLM_CHUNK` 流式消息块，仅依赖 `LLM_FINAL_RESPONSE`。若最终响应不完整，则导致字符丢失。
    *   **解决方案**:
        *   在 `ChatInterface` 组件中增加了对 `LLM_CHUNK` 消息类型的处理逻辑。
        *   引入状态变量（如 `isReceivingChunks`, `streamingChunkMessageId`, `accumulatedChunkContent`）来跟踪和聚合这些流式文本块。
        *   实现了当 `LLM_CHUNK` 到达时，实时更新对应助理消息的 `content`，从而确保所有文本内容都能被正确显示，并实现了真正的流式打字效果。

2.  **工具调用（Function Call）信息显示错位问题修复** ([`Frontend/components/chat-interface.tsx`](Frontend/components/chat-interface.tsx:0)):
    *   **问题定位**: 工具调用信息有时会错误地附加到前一条（历史）LLM的文本回复消息框中，而非当前交互回合。
    *   **解决方案**:
        *   引入核心状态变量 `currentTurnAssistantMessageId` 来唯一标识和锚定当前整个助理交互回合所对应的消息。
        *   重构了 `handleWebSocketMessage` 函数，确保所有与当前助理回合相关的事件（包括 `LLM_CHUNK`、`LLM_FINAL_RESPONSE`、`TOOL_CALL_START`、`TOOL_CALL_RESULT`）都会正确地更新或附加到由 `currentTurnAssistantMessageId` 标识的同一个消息对象上。
        *   当新的助理回合开始时（通过首个 `LLM_CHUNK`、独立的 `LLM_FINAL_RESPONSE` 或 `TOOL_CALL_START`），会创建或关联到这个回合ID。回合结束（如收到 `TURN_ENDED`）时则清除此ID。

3.  **WebSocket 连接失败及动态IP适应性问题修复** ([`Frontend/components/websocket-context.tsx`](Frontend/components/websocket-context.tsx:72), [`Frontend/.env.local`](Frontend/.env.local:1)):
    *   **问题定位**:
        1.  当用户通过IP地址（例如 `http://************:3000`）访问前端时，若WebSocket连接地址使用 `localhost`，会导致连接失败。
        2.  用户在 `.env.local` 中将 `NEXT_PUBLIC_WEBSOCKET_URL` 错误地配置为 `ws://localhost:8000/ws/chat`，此配置优先级高于代码中的动态逻辑。
        3.  用户服务器（Linux板卡）的IP地址可能会发生变化，需要前端能够动态适应。
    *   **解决方案**:
        *   优化了 `websocket-context.tsx` 中的 `getWebSocketUrl` 函数：
            *   它会优先检查并使用 `NEXT_PUBLIC_WEBSOCKET_URL` 环境变量（如果已设置）。
            *   **关键**：如果该环境变量未设置，则会根据浏览器当前访问前端时使用的 `window.location.hostname` 来动态构建 WebSocket URL（例如，若通过 `http://板卡IP:3000` 访问，则连接 `ws://板卡IP:8000/ws/chat`；若通过 `http://localhost:3000` 访问，则连接 `ws://localhost:8000/ws/chat`）。
        *   指导用户从其 `.env.local` 文件中**移除或注释掉**不正确的 `NEXT_PUBLIC_WEBSOCKET_URL` 配置。这使得代码中的动态URL构建逻辑能够生效，从而解决了因固定 `localhost` 配置导致的局域网连接失败问题，并同时满足了服务器IP可能变化的适应性需求。
        *   用户在移除错误的环境变量并重启前端服务后，确认连接成功。

**技术实现亮点**:
- ✅ 实现了对 `LLM_CHUNK` 消息的正确聚合，保证了LLM回复内容的完整性和真正的流式显示。
- ✅ 通过引入 `currentTurnAssistantMessageId` 状态，确保了工具调用等复杂交互信息能准确地归属于当前LLM的回合。
- ✅ 优化了WebSocket URL的生成逻辑，使其在环境变量未配置时能根据当前访问方式动态构建，成功解决了 `localhost` 在局域网访问中的解析问题，并支持了服务器动态IP的场景。

**用户体验提升**:
- ✅ LLM的回复文本不再丢失，用户可以接收到完整的信息。
- ✅ 工具调用信息展示准确，不再混淆于历史消息。
- ✅ 前端应用能够更稳定地连接到WebSocket后端，无论是在服务器本机访问还是从局域网其他客户端访问，并且能够适应服务器IP的变化。

**问题解决**:
- ✅ 解决了LLM回复在前端显示不完整、部分字符丢失的问题。
- ✅ 解决了工具调用信息错误地附加到前一条LLM消息的问题。
- ✅ 解决了因WebSocket URL配置不当导致的连接失败问题，特别是修复了在局域网IP访问前端时使用 `localhost` 作为WebSocket主机名的问题，并使系统能够适应服务器动态IP地址。

---

# 2025-06-14
feat(backend-config): 增强API密钥管理和更新LLM模型

本次修改旨在提升后端配置的安全性与灵活性，主要包括：

1.  **API密钥管理增强** (`Backend/utils/config_loader.py`, `Backend/.env`, `Backend/.env.example`, `.gitignore`):
    *   **环境变量支持**: 实现从 `Backend/.env` 文件加载火山引擎的 LLM 和 TTS API 密钥，取代原先在 `Backend/config.yaml` 中的硬编码。
    *   **示例文件**: 在 `Backend/` 目录下引入 `.env.example` 文件，作为用户配置实际 `.env` 文件的模板。
    *   **安全忽略**: 确保 `Backend/.env` 文件被项目根目录的 `.gitignore` 正确忽略，防止敏感密钥泄露。
    *   **配置加载逻辑更新**: 修改 `Backend/utils/config_loader.py` 中的 `ConfigLoader` 类，使其优先从环境变量读取 API 密钥，如果环境变量中未设置，则回退（当前为空字符串）到 `config.yaml` 中的值。

2.  **LLM 模型更新** (`Backend/config.yaml`):
    *   将 `llm.model.name` 配置项的值从 `"doubao-pro-32k-functioncall-241028"` 更新为 `"doubao-1.5-pro-32k-250115"`。

3.  **文档更新** (`README.md`, `Backend/README.md`):
    *   在项目根目录的 `README.md` 和 `Backend/README.md` 中添加了关于如何设置和使用 `Backend/.env` 文件来配置 API 密钥的说明，指导用户复制 `.env.example` 并填入自己的密钥。
  
  4.  **TTS环境变量与配置项名称标准化**:
      *   将火山引擎 TTS 相关的环境变量从 `VOLCENGINE_TTS_APP_KEY` 和 `VOLCENGINE_TTS_ACCESS_KEY` 分别重命名为 `VOLCENGINE_TTS_APP_ID` 和 `VOLCENGINE_TTS_ACCESS_TOKEN`。
      *   同步更新了 `Backend/.env.example` 和用户本地的 `Backend/.env` 文件中的变量名。
      *   更新了 `Backend/config.yaml` 中 `tts.volcengine` 下的占位符字段名，从 `app_key` 改为 `app_id`，从 `access_key` 改为 `access_token`，并更新了相关注释。
      *   修改了 `Backend/utils/config_loader.py` 中的 `TTSConfig` 数据类，将字段 `app_key` 重命名为 `app_id`，将 `access_key` 重命名为 `access_token`。同时更新了 `get_tts_config()` 方法以使用新的字段名和环境变量。
      *   修改了 `Backend/tts/client.py` 中的 `TTSClient`，使其在构建请求头时使用 `config.app_id` 和 `config.access_token`。
      *   更新了根 `README.md` 和 `Backend/README.md` 中的文档示例，以反映新的 TTS 环境变量名称。

**技术实现亮点**:
- ✅ 通过环境变量管理API密钥，增强了项目的安全性，避免了敏感信息硬编码。
- ✅ 提供了 `.env.example` 模板，简化了新用户的配置流程。
- ✅ 更新了LLM模型ID，以使用新的模型版本。
- ✅ 完善了相关文档，确保用户能够正确配置和使用新的密钥管理机制。

**用户体验提升**:
- ✅ 更安全的密钥管理方式，降低了密钥意外泄露的风险。
- ✅ 清晰的配置文档和示例，方便用户快速上手。

**问题解决**:
- ✅ 解决了API密钥硬编码在配置文件中带来的安全隐患。
- ✅ 修复了 `Backend/llm/client.py` 中 `LLMClient` 初始化时未优先从环境变量加载 `VOLCENGINE_LLM_API_KEY`，导致使用 `config.yaml` 中空API密钥而引发 "Illegal header value b'Bearer '" 错误的问题。

---

# 2025-06-10
feat(audio-config-tools): 重构音频配置工具，实现统一配置和持久化解决方案

本次修改的主要目标是彻底解决Linux环境下音频设备配置复杂、重启后配置丢失等问题，通过创建统一的音频配置工具和持久化机制，为ASR-LLM-TTS项目提供一站式的音频设备管理解决方案。

主要修改内容：

1. **统一音频配置工具开发** (`Backend/utils/audio_config/unified_audio_setup.py`)
   - **交互式设备选择**: 实现完整的菜单驱动设备配置界面，支持播放和录音设备选择
   - **音量控制集成**: 内置0-200%音量设置功能，支持输入输出设备独立音量控制
   - **持久化配置机制**: 集成PulseAudio系统配置文件修改，解决重启后配置丢失问题
   - **设备解析优化**: 修复设备ID解析逻辑，正确处理`hw:X,Y`格式而非错误的`hw:X,Audio`格式
   - **权限分离处理**: 普通用户运行主程序，需要时自动请求sudo权限进行系统配置

2. **设备状态检查工具增强** (`Backend/utils/audio_config/check_audio_devices.py`)
   - **详细硬件信息显示**: 新增设备详细信息获取功能，显示设备对应的具体硬件名称
   - **完整设备列表**: 显示所有可用输入输出设备及其对应的硬件信息
   - **状态可视化**: 使用emoji标识当前默认设备，提供直观的状态显示
   - **精简输出**: 移除冗余信息，专注于核心的设备状态检查功能

3. **配置清理工具创建** (`Backend/utils/audio_config/clean_audio_config.py`)
   - **持久化配置清理**: 提供安全的系统配置清理功能，移除错误的音频配置
   - **PulseAudio重启**: 自动重启PulseAudio服务，确保配置清理后系统恢复正常
   - **交互式确认**: 添加用户确认机制，避免误操作导致的配置丢失
   - **临时脚本执行**: 使用临时脚本方式安全执行系统级配置修改

4. **持久化配置机制实现**
   - **系统配置文件修改**: 修改`/etc/pulse/default.pa`文件，禁用自动检测，使用手动设备配置
   - **自定义设备命名**: 创建`custom_output`和`custom_input`设备，确保设备名称固定
   - **默认设备设置**: 在配置文件中设置默认设备，避免PulseAudio自动选择
   - **配置备份机制**: 自动备份原始配置文件，提供配置恢复能力

5. **设备解析逻辑修复**
   - **正确的硬件ID格式**: 修复设备解析逻辑，正确提取`card`和`device`号生成`hw:X,Y`格式
   - **设备信息解析**: 改进`aplay -l`和`arecord -l`输出解析，正确提取设备信息
   - **友好名称映射**: 增强设备友好名称识别，支持UACDemoV1.0、UGREEN等设备
   - **错误处理增强**: 添加详细的解析错误处理和调试信息

6. **文件结构优化和文档完善**
   - **无用文件清理**: 删除过时的`setup_audio_devices.py`、`CONFIG_GUIDE.md`、`USAGE.md`等文件
   - **README.md重写**: 完全重写文档，提供清晰的工具说明、使用方法和常见问题解答
   - **使用示例文档**: 新增`EXAMPLES.md`，提供详细的使用场景和故障排除指南
   - **文件结构精简**: 精简到6个核心文件，职责清晰，易于维护

7. **核心技术突破**
   - **PulseAudio自动检测禁用**: 通过禁用`module-udev-detect`解决设备序号变化问题
   - **手动设备加载**: 使用`module-alsa-sink`和`module-alsa-source`手动加载指定设备
   - **设备名称固定**: 通过`sink_name`和`source_name`参数确保设备名称不变
   - **权限问题解决**: 分离权限需求，避免sudo环境下PulseAudio无法访问的问题

**技术实现亮点**:
- ✅ 一站式解决方案，集成设备配置、音量控制、持久化保存
- ✅ 智能权限处理，普通用户运行，需要时自动请求sudo权限
- ✅ 完善的错误处理和恢复机制，提供配置清理和重置功能
- ✅ 正确的设备ID解析，修复`hw:X,Audio`格式错误问题
- ✅ 系统级持久化配置，彻底解决重启后配置丢失问题

**用户体验提升**:
- ✅ 交互式配置界面，无需记忆复杂命令和参数
- ✅ 音量控制集成，支持0-200%音量设置
- ✅ 详细的设备信息显示，清楚了解硬件对应关系
- ✅ 一键配置清理，故障排除更加便捷
- ✅ 完善的文档和示例，降低使用门槛

**问题解决**:
- ✅ 彻底解决重启后音频设备配置丢失问题
- ✅ 修复设备ID格式错误导致的配置失效问题
- ✅ 解决sudo权限下PulseAudio无法访问的问题
- ✅ 修复设备解析逻辑错误，正确识别硬件设备
- ✅ 解决PulseAudio自动检测导致的设备序号变化问题

**测试验证结果**:
- ✅ 配置工具正确识别UACDemoV1.0和UGREEN设备
- ✅ 持久化配置成功，重启后设备配置保持不变
- ✅ 音量控制功能正常，支持高音量设置解决声音小问题
- ✅ 设备状态检查显示详细硬件信息，配置验证准确
- ✅ 配置清理功能正常，可安全重置错误配置

**文件结构优化**:
```
utils/audio_config/
├── 📋 README.md # 完整使用文档
├── 🎯 EXAMPLES.md # 使用示例和故障排除
├── 🌟 unified_audio_setup.py # 主配置工具 (一站式解决方案)
├── 🔍 check_audio_devices.py # 设备状态检查 (增强版)
├── 🧹 clean_audio_config.py # 配置清理工具
└── ⚙️ audio_device_config_manager.py # 底层支持模块
```

**总结**:
通过本次重构，彻底解决了Linux环境下音频设备配置的核心痛点。新的统一配置工具提供了完整的一站式解决方案，从设备选择、音量控制到持久化配置，用户只需运行一个命令即可完成所有配置。持久化机制的实现彻底解决了重启后配置丢失的问题，为ASR-LLM-TTS项目的音频设备管理提供了稳定可靠的技术基础。精简的文件结构和完善的文档使工具更易维护和使用。

---

# 2025-06-09
fix(frontend-message-display): 修复前端消息显示不完整和工具调用丢失问题

本次修改的主要目标是解决前端聊天界面中LLM回复显示不完整、工具调用信息丢失、以及消息处理重复等关键问题，通过重构消息处理机制和实现伪流式显示，提供稳定可靠的用户交互体验。

主要修改内容：

1. **消息处理架构重构** (`Frontend/components/websocket-context.tsx`)
   - **全局消息处理器**: 新增messageHandlerRef和setMessageHandler函数，支持直接消息处理
   - **消息时间戳机制**: 为每个消息添加_timestamp确保React状态更新触发
   - **类型定义扩展**: 扩展WebSocketContextType接口，添加setMessageHandler方法
   - **批量更新优化**: 避免React批量更新导致的消息丢失问题

2. **聊天界面消息处理优化** (`Frontend/components/chat-interface.tsx`)
   - **直接消息处理**: 实现handleWebSocketMessage函数，绕过React useEffect的批量更新限制
   - **关键消息优先**: LLM_FINAL_RESPONSE、TOOL_CALL_START、TOOL_CALL_RESULT通过全局处理器直接处理
   - **非关键消息保留**: ASR_FINAL_RESULT、TTS_STATUS等仍通过useEffect处理
   - **依赖项简化**: 移除复杂的useEffect依赖项，避免重复触发

3. **伪流式显示实现** (`Frontend/components/chat-interface.tsx`)
   - **打字机效果**: 实现startPseudoStreaming函数，模拟逐字符显示效果
   - **显示控制**: 添加stopPseudoStreaming函数，支持中断和快速完成显示
   - **状态管理**: 新增currentDisplayContent、targetContent等状态管理伪流式显示
   - **定时器管理**: 使用typingIntervalRef管理显示定时器，确保资源正确释放

4. **工具调用信息保护** (`Frontend/components/chat-interface.tsx`)
   - **工具调用保留**: 在所有消息更新操作中确保toolCalls属性被正确保留
   - **状态同步**: 修复伪流式显示过程中工具调用信息丢失的问题
   - **消息完整性**: 确保LLM文本回复和工具调用信息同时正确显示
   - **调试日志增强**: 添加详细的工具调用处理日志，便于问题诊断

5. **React Hooks优化** (`Frontend/components/chat-interface.tsx`)
   - **useCallback导入**: 修复缺失的useCallback导入错误
   - **依赖项管理**: 正确设置useCallback和useEffect的依赖项
   - **内存泄漏防护**: 添加定时器清理逻辑，防止组件卸载时的内存泄漏
   - **组件生命周期**: 优化消息处理器的设置和清理时机

6. **消息处理流程重设计**
   - **双重处理机制**: 关键消息通过全局处理器，非关键消息通过useEffect
   - **消息去重**: 移除复杂的消息重复处理机制，简化处理逻辑
   - **错误恢复**: 改善消息处理失败时的恢复机制
   - **调试可视化**: 增强控制台日志，便于问题定位和调试

**技术实现亮点**:
- ✅ 绕过React批量更新限制，确保所有消息都能被正确处理
- ✅ 实现真正的伪流式显示，保持后端流式架构不变
- ✅ 双重消息处理机制，关键消息和非关键消息分别处理
- ✅ 完善的工具调用信息保护，确保显示完整性
- ✅ 优化的React Hooks使用，避免常见的依赖项和内存泄漏问题

**用户体验提升**:
- ✅ LLM回复完整显示，不再出现丢字现象
- ✅ 工具调用信息稳定显示，包含参数和结果
- ✅ 流畅的打字机效果，提升视觉体验
- ✅ 消息显示一致性，避免显示不同步问题
- ✅ 响应速度提升，消息处理更加及时

**问题解决**:
- ✅ 修复LLM_FINAL_RESPONSE消息被跳过导致的回复缺失问题
- ✅ 解决TOOL_CALL_START消息处理失败导致的工具调用信息丢失
- ✅ 修复React useEffect重复触发导致的消息处理混乱
- ✅ 解决伪流式显示过程中工具调用信息被覆盖的问题
- ✅ 修复useCallback导入缺失导致的运行时错误

**测试验证结果**:
- ✅ 简单对话消息完整显示，伪流式效果正常
- ✅ 工具调用对话同时显示LLM回复和工具调用信息
- ✅ 多轮对话稳定，不再出现消息丢失现象
- ✅ 控制台日志清晰，便于问题诊断和调试
- ✅ 前端性能稳定，无内存泄漏和重复处理问题

**总结**:
通过本次重构，彻底解决了前端消息显示的核心问题。新的消息处理架构更加稳定可靠，既保持了后端的流式处理优势，又提供了流畅的前端用户体验。伪流式显示的实现既满足了用户对打字机效果的需求，又确保了消息的完整性和一致性。这次修复为语音对话助手的前端交互奠定了坚实的技术基础。

---

# 2025-06-08
feat(audio-config-tools): 优化音频设备配置工具模块，提升用户体验和配置准确性

本次修改的主要目标是优化音频配置工具模块，移除快速配置功能，修复配置信息显示问题，改善用户交互体验，为Linux环境下的音频设备配置提供更加稳定和用户友好的解决方案。

主要修改内容：

1. **主配置工具优化** (`Backend/utils/audio_config/setup_audio_devices.py`)
   - **移除快速配置选项**: 删除了容易产生混淆的快速配置功能，简化用户选择
   - **菜单结构重构**: 将菜单选项从5个精简为4个，编号从0-4，提高界面清晰度
   - **菜单显示函数**: 新增show_menu()函数，确保每次操作后正确显示菜单选项
   - **交互流程优化**: 改善配置完成后的用户提示和菜单重新显示逻辑

2. **配置管理器核心修复** (`Backend/utils/audio_config/audio_device_config_manager.py`)
   - **关键词匹配逻辑修复**: 修复interactive_setup()中设备关键词获取错误的bug
   - **索引匹配优化**: 改进设备索引号匹配逻辑，避免关键词混淆问题
   - **配置汇总显示完善**: 修复配置汇总中输入输出设备关键词显示错误的问题
   - **设备识别增强**: 完善设备友好名称识别，支持Yundea、UGREEN等常见USB设备

3. **设备检测工具功能** (`Backend/utils/audio_config/check_audio_devices.py`)
   - **状态报告生成**: 提供详细的音频设备状态检查和可视化报告
   - **PulseAudio状态检测**: 检查PulseAudio服务运行状态和设备可用性
   - **设备配置验证**: 自动验证当前设备配置是否符合项目要求
   - **友好名称显示**: 为常见设备提供emoji图标和友好名称显示

4. **文档更新和规范化**
   - **README.md更新**: 移除快速配置相关说明，更新菜单选项描述
   - **USAGE.md修订**: 更新使用场景和命令示例，移除过时的快速配置引用
   - **CONFIG_GUIDE.md完善**: 提供详细的config.yaml手动配置指南和示例

5. **核心功能特性**
   - **智能设备扫描**: 自动检测所有可用的PulseAudio输入输出设备
   - **多种匹配方式**: 支持设备索引、名称关键词、友好名称等多种匹配方式
   - **安全配置策略**: 不自动修改config.yaml，通过提示引导用户手动配置
   - **设备关键词提取**: 智能提取设备关键词，支持Yundea、UGREEN、pulse等常见设备

6. **用户交互体验优化**
   - **清晰的菜单结构**: 精简为4个核心功能选项，避免用户困惑
   - **完整的配置提示**: 显示完整的config.yaml修改指导，包含具体参数
   - **状态可视化**: 使用emoji和颜色标识，直观显示设备状态和配置结果
   - **错误处理机制**: 完善的错误提示和异常处理，提高工具稳定性

**技术实现亮点**:
- ✅ 精简架构设计，仅3个核心文件解决所有音频配置需求
- ✅ 智能设备识别和友好名称映射，提升用户体验
- ✅ 安全的配置策略，避免自动修改配置文件造成的风险
- ✅ 完善的PulseAudio集成，支持动态设备路由配置
- ✅ 模块化设计，便于维护和功能扩展

**用户体验提升**:
- ✅ 简化的菜单选项，减少用户选择困惑
- ✅ 清晰的配置指导，用户明确知道需要修改什么
- ✅ 直观的状态显示，一目了然的设备配置状态
- ✅ 完整的使用文档，覆盖各种使用场景

**问题解决**:
- ✅ 修复配置汇总中设备关键词显示错误的bug
- ✅ 解决菜单显示混乱和选项编号不一致的问题
- ✅ 移除容易产生混淆的快速配置功能
- ✅ 优化设备匹配逻辑，避免索引和关键词冲突

**测试验证结果**:
- ✅ 菜单选项显示正确，编号从0-4连续
- ✅ 设备关键词提取和显示功能正常
- ✅ 配置汇总信息准确显示输入输出设备参数
- ✅ 用户交互流程顺畅，无界面混乱问题

**总结**:
通过本次优化，音频配置工具模块变得更加简洁、稳定和用户友好。移除了容易产生混淆的快速配置功能，修复了关键的显示bug，改善了用户交互体验。新的工具架构更加注重安全性和可维护性，为Linux环境下的音频设备配置提供了可靠的解决方案，特别适合需要精确控制USB音频设备的应用场景。

---

# 2025-06-08
feat(audio-device-sharing): 实现AudioNewPlayer与MusicPlayer共享USB声卡设备功能

本次修改的主要目标是让音频播放器(AudioNewPlayer)和音乐播放器(MusicPlayer)能够共享同一个外接USB声卡设备(Yundea 1076)，实现同时播放而不产生设备冲突，满足用户仅使用外接USB音频设备的需求。

主要修改内容：

1. **MusicPlayer架构重构** (`Backend/utils/function_call/function_tools_impl.py`)
   - **移除pygame独占初始化**: 去除了MusicPlayer构造函数中的直接pygame.mixer.init()调用
   - **共享AudioNewPlayer实例**: 添加set_audio_player()方法，支持外部设置共享的AudioNewPlayer实例
   - **智能pygame配置**: 新增_init_pygame_mixer()方法，配置pygame使用ALSA dmix设备实现设备共享
   - **设备选择优化**: 移除了复杂的设备选择逻辑，统一使用dmix确保设备共享
   - **环境变量配置**: 设置SDL_AUDIODRIVER='alsa'和ALSA_PCM_DEVICE='dmix'确保pygame使用共享设备

2. **设备共享接口优化** (`Backend/utils/function_call/function_call_tools.py`)
   - **新增共享函数**: 添加set_shared_audio_player()函数，提供统一的AudioNewPlayer实例设置接口
   - **模块化设计**: 将设备共享逻辑从直接赋值改为函数调用，提高代码可维护性

3. **服务器初始化优化** (`Backend/server.py`)
   - **导入共享函数**: 导入set_shared_audio_player函数
   - **统一设备共享**: 在AudioNewPlayer初始化后，使用新的共享函数设置MusicPlayer的共享实例
   - **确保初始化顺序**: 保证AudioNewPlayer完全初始化后再进行设备共享设置

4. **音频参数统一化**
   - **采样率统一**: pygame mixer配置为48000Hz，与AudioNewPlayer保持一致
   - **音频格式统一**: 使用16位有符号整数格式(-16)，确保音频质量一致性
   - **缓冲区优化**: 设置2048字节缓冲区，平衡延迟和稳定性
   - **立体声支持**: 配置为2通道立体声输出

5. **测试验证脚本** (`Backend/test_audio_sharing.py`)
   - **设备检测功能**: 自动检测系统中可用的音频设备，特别是Yundea 1076设备
   - **共享播放测试**: 验证音乐播放和TTS音频能否同时播放
   - **音频生成器**: 创建测试用的正弦波音频流，模拟TTS播放场景
   - **状态验证**: 检查音乐播放状态，确认TTS播放后音乐是否继续

6. **配置文档完善** (`docs/audio_device_sharing.md`)
   - **硬件配置说明**: 详细说明三个音频设备的用途和配置策略
   - **软件配置指南**: ALSA dmix配置和项目配置参数说明
   - **故障排除指南**: 常见问题的诊断和解决方案
   - **性能优化建议**: 针对USB声卡的参数调优建议

**技术实现亮点**:
- ✅ 使用ALSA dmix实现真正的设备共享，避免设备独占冲突
- ✅ 统一音频参数配置，确保两个播放器的音频格式兼容
- ✅ 保持现有设备管理器的音量协调功能不变
- ✅ 通过环境变量配置确保pygame使用正确的音频后端
- ✅ 模块化的设备共享接口，便于维护和扩展

**用户体验提升**:
- ✅ 音乐播放器和TTS可以真正同时使用同一个USB声卡
- ✅ 消除"Device or resource busy"设备冲突错误
- ✅ 保持原有的智能音量管理功能
- ✅ 支持仅使用外接USB音频设备的硬件配置

**问题解决**:
- ✅ 修复pygame mixer独占音频设备导致的冲突问题
- ✅ 解决AudioNewPlayer和MusicPlayer无法同时播放的问题
- ✅ 统一音频设备选择逻辑，避免设备配置不一致
- ✅ 优化设备共享机制，确保稳定的混合播放

**测试验证结果**:
- ✅ pygame mixer成功配置为使用dmix设备
- ✅ AudioNewPlayer和MusicPlayer共享设备实例设置正常
- ✅ 音频参数统一配置(48kHz, 16bit, 2ch)生效
- ✅ 设备共享接口调用正常，无初始化错误

**总结**:
通过本次重构，成功实现了AudioNewPlayer和MusicPlayer对同一USB声卡设备的真正共享。新的架构使用ALSA dmix技术确保设备级别的共享，而不是简单的应用层协调。这种实现方式更加稳定可靠，完全满足了用户仅使用外接USB音频设备的需求，为语音对话助手提供了更好的音频体验基础。

---

# 2025-06-08
fix(audio-device-conflict): 修复音频设备冲突问题并实现TTS与音乐混合播放功能

本次修改的主要目标是解决ASR-LLM-TTS系统中音频设备资源竞争问题，实现TTS语音合成与背景音乐的同时播放功能，并优化音频缓冲区配置以减少ALSA underrun警告。

主要修改内容：

1. **音频设备管理器重构** (`Backend/utils/audio_device_manager.py`)
   - **新增文件**: 创建全新的音频设备管理器，采用单例模式
   - **混合播放支持**: 从独占模式改为混合播放模式，支持多个播放器同时使用设备
   - **智能音量协调**: TTS播放时自动降低音乐音量至30%，播放结束后恢复原始音量
   - **线程安全设计**: 使用threading.Lock确保并发访问安全
   - **活跃用户管理**: 维护当前活跃播放器列表，支持动态添加和移除

2. **音频播放器集成优化** (`Backend/utils/audio_new_player.py`)
   - **设备管理器集成**: 在播放开始前请求设备访问权限，播放结束后释放权限
   - **音量回调注册**: 注册音量控制回调，支持设备管理器动态调整音量
   - **异步兼容性**: 保持原有异步API的同时支持同步设备管理
   - **资源清理优化**: 确保播放异常时正确释放设备访问权限

3. **音乐播放器功能增强** (`Backend/utils/function_call/function_tools_impl.py`)
   - **设备访问集成**: 音乐播放前请求设备访问权限，支持与TTS共享设备
   - **音量控制回调**: 实现设备管理器调用的音量调整方法
   - **播放监控优化**: 改进音乐播放结束检测和设备权限释放机制
   - **原始音量保持**: 记录并恢复用户设置的原始音量
   - **事件循环修复**: 解决同步上下文中的异步操作问题

4. **音频缓冲区配置优化** (`Backend/config.yaml`)
   - **blocksize优化**: 从1024增加到2048，减少音频回调频率
   - **buffer_size增加**: 从100增加到150，提供更大的缓冲空间
   - **min_prebuffer提升**: 从10增加到15，确保充足的预缓冲
   - **underrun问题缓解**: 通过参数调优显著减少ALSA underrun警告

5. **测试验证脚本** (`Backend/test_device_manager.py`, `Backend/test_mixed_audio.py`)
   - **功能测试**: 创建设备管理器基础功能测试脚本
   - **混合播放测试**: 验证TTS与音乐同时播放的完整流程
   - **状态管理验证**: 测试设备访问权限的申请、释放和状态转换
   - **音量协调测试**: 验证TTS播放时的音量自动调整机制

**技术实现亮点**:
- ✅ 线程安全的设备资源管理，避免竞争条件
- ✅ 智能音量协调算法，TTS播放时降低背景音乐音量30%
- ✅ 优雅的资源生命周期管理，确保异常情况下的正确清理
- ✅ 保持API向后兼容性，无需修改现有调用代码
- ✅ 事件循环问题修复，解决同步/异步上下文混合使用问题

**用户体验提升**:
- ✅ TTS与音乐可同时播放，不再互相中断
- ✅ 智能音量管理，TTS播放时自动降低背景音乐音量
- ✅ 音频播放更稳定，减少缓冲区不足导致的音频中断
- ✅ 播放结束后自动恢复原始音量设置

**问题解决**:
- ✅ 修复"Device unavailable [PaErrorCode -9985]"设备占用错误
- ✅ 解决"There is no current event loop in thread"异步上下文错误
- ✅ 显著减少"ALSA lib pcm.c underrun occurred"警告
- ✅ 消除TTS播放导致音乐中断的问题

**测试验证结果**:
- ✅ 设备管理器状态管理正常，支持多用户并发访问
- ✅ 音乐播放器成功获得设备访问权限并正常播放
- ✅ TTS请求访问时音乐音量正确降低至30%
- ✅ TTS播放结束后音乐音量正确恢复至原始值70%
- ✅ 所有播放器停止后设备访问权限正确释放

**总结**:
通过本次重构，成功解决了音频设备资源竞争的核心问题，实现了TTS语音合成与背景音乐的和谐共存。新的混合播放架构不仅提升了用户体验，还为未来扩展更多音频功能奠定了坚实基础。智能音量协调机制确保了语音内容的清晰度，同时保持了背景音乐的连续性，真正实现了"音乐不停，语音清晰"的设计目标。

---

# 2025-06-05
refactor(folder-structure): 重命名backend文件夹为Backend并更新所有路径引用

本次修改的主要目标是统一项目文件夹命名规范，将backend文件夹重命名为Backend，与Frontend文件夹保持一致的大写命名风格，并更新所有相关的路径引用以确保项目的完整性和一致性。

主要修改内容：

1. **文件夹重命名**
   - **核心操作**: 将 `backend/` 文件夹重命名为 `Backend/`
   - **命名规范统一**: 与 `Frontend/` 文件夹保持一致的大写命名风格
   - **内容完整性**: 确保Backend文件夹内所有文件和子目录完整保留

2. **启动脚本路径更新** (`run.sh`)
   - **变量路径修改**: 将 `BACKEND_DIR="$PROJECT_ROOT/backend"` 更新为 `BACKEND_DIR="$PROJECT_ROOT/Backend"`
   - **功能验证**: 确保一键启动脚本能够正确识别和访问Backend目录
   - **兼容性保持**: 保持脚本的所有功能特性不变

3. **Git忽略规则更新** (`.gitignore`)
   - **模型文件路径**: 更新 `Backend/model/asr/*` 和 `Backend/model/vad/*` 忽略规则
   - **资源文件路径**: 更新 `Backend/resource/*.mp3` 和 `Backend/resource/*.wav` 忽略规则
   - **测试资源路径**: 更新 `Backend/test/resource/*` 相关忽略规则
   - **内存库路径**: 更新 `Backend/memory-bank/` 忽略规则

4. **项目文档路径更新** (`README.md`)
   - **项目结构图**: 更新项目目录结构中的 `├── Backend/` 路径显示
   - **架构说明章节**: 更新后端架构章节标题和文档链接引用
   - **安装指南**: 更新后端依赖安装命令中的目录路径
   - **模型配置**: 更新ASR和VAD模型文件的存放路径说明
   - **配置文件**: 更新config.yaml文件的路径引用
   - **资源目录**: 更新音乐文件存放目录的路径说明
   - **测试脚本**: 更新测试脚本目录的路径引用
   - **手动启动**: 更新手动启动命令中的目录路径
   - **文档链接**: 更新后端技术文档的链接路径

5. **后端技术文档更新** (`Backend/README.md`)
   - **目录结构图**: 更新文档中的目录结构展示，将 `backend/` 改为 `Backend/`
   - **启动命令**: 更新部署指南中的启动命令路径

6. **保持不变的内容**
   - **版本历史**: `version.md` 中的历史记录保持原样，不修改过往版本信息
   - **服务引用**: `status.sh` 中对"后端服务"的功能性引用保持不变
   - **日志文件**: 保持 `backend.log` 等日志文件名不变
   - **前端代码**: 前端源代码中无相关路径引用，无需修改
   - **依赖文件**: node_modules等第三方依赖文件保持不变

**技术实现细节**:
- ✅ 使用 `mv backend Backend` 命令完成文件夹重命名
- ✅ 通过正则表达式搜索确保所有路径引用的完整更新
- ✅ 区分路径引用和功能性引用，避免误修改
- ✅ 保持历史记录和日志文件的连续性
- ✅ 验证脚本功能和目录访问的正确性

**命名规范统一**:
- ✅ Frontend/ - 前端项目目录（已存在）
- ✅ Backend/ - 后端项目目录（新命名）
- ✅ 采用统一的首字母大写命名风格
- ✅ 提升项目结构的专业性和一致性

**总结**:
通过本次重构，成功实现了项目文件夹命名规范的统一，将backend重命名为Backend，与Frontend保持一致的大写命名风格。同时完成了所有相关文件中路径引用的更新，确保项目的完整性、一致性和可维护性。这一改动提升了项目结构的专业性，为后续的开发和维护工作奠定了良好的基础。

---

# 2025-06-05
feat(function-call-docs): 创建Function Calling专门文档并优化后端README结构

本次修改的主要目标是为ASR-LLM-TTS语音对话助手项目的Function Calling工具调用系统创建专门的技术文档，并优化后端README的文档结构，提升项目文档的专业性和可维护性。

主要修改内容：

1. **创建Function Calling专门技术文档** (`docs/function_call.md`)
   - **完整的系统架构说明**: 包含设计理念、架构图和核心组件详解
   - **详细的工具分类介绍**: 涵盖基础工具、音频控制、视觉识别、机械臂控制、摄像头云台等5大类工具
   - **工具调用流程详解**: 完整的调用流程图、Turn Manager处理逻辑和WebSocket消息协议
   - **智能提示词设计**: 核心原则、机械臂操作安全指南和错误处理规则
   - **错误处理机制**: 参数验证、网络重试、冷却机制等完善的错误处理策略
   - **配置管理说明**: 工具相关配置和系统提示词配置详解
   - **扩展开发指南**: 新工具添加步骤、最佳实践和代码示例
   - **性能优化策略**: 异步执行、连接复用、缓存机制和资源管理
   - **安全考虑**: 参数验证、权限控制和错误信息安全处理

2. **后端README结构优化** (`backend/README.md`)
   - **简化Function Calling章节**: 保留系统概述和核心特性，删除冗余的详细实现说明
   - **添加文档引用**: 通过链接引用专门的Function Calling文档，避免内容重复
   - **保持README简洁性**: 维持README作为项目概览的定位，详细技术内容通过专门文档提供
   - **改进章节结构**: 优化章节组织，提升文档的可读性和导航性

3. **文档内容特色**
   - **可视化架构图**: 使用ASCII图表清晰展示Function Calling系统架构
   - **丰富的代码示例**: 提供FunctionRegistry、Schema定义、工具实现等核心代码示例
   - **详细的参数说明**: 每个工具函数的参数类型、取值范围和使用示例
   - **实用的配置指南**: 完整的配置参数说明和最佳实践建议
   - **完善的错误处理**: 详细的错误类型、处理策略和用户友好的错误提示

4. **技术文档规范**
   - **模块化文档设计**: 将复杂功能的详细说明独立成专门文档
   - **文档交叉引用**: 通过链接建立文档间的关联，形成完整的文档体系
   - **内容层次清晰**: 从概述到详细实现，层次分明的内容组织
   - **开发者友好**: 提供完整的扩展指南和最佳实践，便于二次开发

**文档结构总结**:
- ✅ 创建639行的Function Calling专门技术文档
- ✅ 涵盖11个主要章节，从概述到扩展指南
- ✅ 包含5大类工具的详细功能说明和使用方法
- ✅ 提供完整的架构设计和组件说明
- ✅ 详细的工具调用流程和WebSocket消息协议
- ✅ 智能提示词设计和安全操作规则
- ✅ 完善的错误处理和性能优化策略
- ✅ 实用的扩展开发指南和最佳实践

**总结**:
通过本次修改，成功为ASR-LLM-TTS项目的Function Calling系统建立了完整的技术文档体系。专门的技术文档为开发者提供了详细的系统说明、使用指南和扩展开发参考，而优化后的README保持了简洁性和概览性。这种模块化的文档设计提升了项目的专业性和可维护性，为后续的功能扩展和团队协作奠定了良好的文档基础。

---

# 2025-06-05
feat(startup-scripts): 新增一键启动脚本系统并整合项目文档

本次修改的主要目标是为ASR-LLM-TTS语音对话助手项目创建完整的一键启动脚本系统，优化Linux板卡环境下的部署体验，并全面整合项目文档，提供统一的项目说明和使用指南。

主要修改内容：

1. **创建一键启动脚本系统**
   - 新增 `run.sh` 一体化启动脚本，支持前后端服务的自动启动、监控和停止
   - 新增 `status.sh` 服务状态查看脚本，提供详细的系统状态信息
   - 删除独立的 `stop.sh` 脚本，将停止功能集成到 `run.sh` 的Ctrl+C处理中

2. **启动脚本核心功能**
   - **智能IP地址检测**: 自动获取局域网IP地址，优先检测WiFi(wlan0)接口，支持多种获取方式
   - **自动依赖管理**: 检测并自动安装前端依赖(node_modules)
   - **智能等待机制**: 后端启动等待10秒(ASR模型加载)，前端启动等待15秒(编译时间)
   - **实时进程监控**: 持续监控服务状态，异常停止时自动清理并退出
   - **增强的Ctrl+C清理**: 智能检测运行中服务，优雅停止+强制清理残留进程，提供详细反馈

3. **局域网访问支持优化**
   - 修复IP地址检测错误，正确识别活跃网络接口(如wlan0: ***********)
   - 启动完成后同时显示本地访问地址和局域网访问地址
   - 明确提示局域网内其他设备可通过板卡IP访问服务
   - 提供完整的WebSocket连接地址信息

4. **进程检测机制优化**
   - 修复进程检测不准确问题，使用更精确的正则表达式匹配
   - 改进 `pgrep` 命令使用方式，避免误报运行状态
   - 统一前后端进程检测逻辑，确保状态显示准确性

5. **项目文档全面整合**
   - **根目录README.md全面重写**: 
     * 新增系统架构可视化图表
     * 整合启动脚本使用说明，删除独立的脚本说明文档
     * 通过链接引用前后端详细技术文档，避免重复
     * 新增Function Calling工具生态完整介绍
     * 添加WebSocket通信机制说明
     * 完善配置指南、常见问题解答等章节
   - **文档结构优化**: 采用emoji图标和清晰的章节划分，提升可读性
   - **技术架构说明**: 详细介绍前后端技术栈、核心组件和模块职责

6. **适配Linux板卡环境**
   - 针对Ubuntu 22.04板卡环境优化，直接使用系统Python环境
   - 支持用户习惯的启动方式: `npm run dev` 和 `python server.py`
   - 兼容不同的网络工具(netstat/ss)和IP获取命令
   - 提供详细的彩色输出和状态反馈

7. **用户体验提升**
   - **一键操作**: 单个 `./run.sh` 命令完成所有启动工作
   - **智能反馈**: 详细的启动进度、状态信息和错误提示
   - **日志管理**: 自动生成 `frontend.log` 和 `backend.log` 日志文件
   - **状态监控**: 每30秒显示运行状态，实时监控服务健康

**脚本功能特点总结**:
- ✅ 自动获取并显示局域网IP地址(支持WiFi优先检测)
- ✅ 自动检查和安装前端依赖
- ✅ 智能等待服务启动(考虑ASR模型加载和前端编译时间)
- ✅ 实时进程监控和异常处理
- ✅ 增强的Ctrl+C清理功能(智能检测+优雅停止+强制清理)
- ✅ 详细的服务状态显示和访问地址提示
- ✅ 完整的错误处理和用户友好的反馈信息

**总结**:
通过本次修改，成功为ASR-LLM-TTS项目构建了完整的一键启动脚本系统，极大简化了Linux板卡环境下的部署和使用流程。用户只需运行 `./run.sh` 即可启动完整的语音对话助手系统，并自动获得本地和局域网访问地址。同时通过文档整合，提供了统一、完整、易读的项目说明，显著提升了项目的可用性和用户体验。

---

# 2025-06-04
fix(audio-player): 修复AudioNewPlayer设备关键词参数传递缺失问题

本次修改的主要目标是修复语音助手系统中AudioNewPlayer初始化时缺失device_keyword参数传递的bug，确保配置文件中的音频设备关键词设置能够正常生效。

主要修改内容：

1. **修复AudioNewPlayer初始化参数缺失**
   - 在 `backend/server.py` 第157-170行的AudioNewPlayer初始化代码中添加缺失的 `device_keyword=player_config_obj.device_keyword` 参数传递
   - 更新相应的日志输出信息，显示device_keyword配置值以便调试

2. **问题背景分析**
   - AudioNewPlayer类已完整实现device_keyword功能，支持通过关键词智能查找音频输出设备
   - config.yaml中已正确配置 `device_keyword: "Y1076"` 
   - config_loader.py已正确读取device_keyword配置
   - 但server.py在初始化AudioNewPlayer时未传递此参数，导致设备关键词配置完全失效

3. **修复效果**
   - 修复前：程序忽略device_keyword配置，只使用系统默认音频设备
   - 修复后：程序正确使用device_keyword查找包含指定关键词的音频设备，找不到时回退到默认设备
   - 解决了USB扬声器等特定音频设备无法被正确识别和使用的问题

4. **设备查找机制说明**
   - 程序通过sounddevice.query_devices()直接查询音频子系统，不依赖lsusb等USB设备枚举
   - 播放器设备发现的层级结构
   ```
    应用层: AudioNewPlayer
    |    ↓
    音频库: sounddevice (Python)
    |    ↓
    底层库: PortAudio (C/C++)
    |    ↓
    音频子系统: ALSA/PulseAudio
    |    ↓
    内核驱动: snd-usb-audio等
    |    ↓
    硬件: USB扬声器
   ```
   - 支持发现所有被音频驱动正确识别的设备，包括USB音频设备、内置声卡等
   - 通过设备名称关键词模糊匹配，提高设备识别的灵活性和可靠性

**总结**:
通过本次bug修复，成功解决了音频设备关键词配置不生效的问题，确保语音助手能够按照用户配置正确选择和使用指定的音频输出设备，提升了系统在不同硬件环境下的兼容性和稳定性。

---

# 2025-05-19
feat(config.yaml): 调整远程function-call请求超时时间

本次修改的主要目标是调整远程function-call请求超时时间，由5秒调整为10秒，确保大模型能够更稳定地调用远程服务。

主要修改内容：

1. **调整远程function-call请求超时时间**  
   - 在 `backend/config.yaml` 中将 `remote_yolo` 的 `timeout` 由5秒调整为10秒。
   - 在 `backend/config.yaml` 中将 `vision_arm_control` 的 `timeout` 由5秒调整为15秒。

**总结**:
通过本次修改，成功调整了远程function-call请求超时时间，确保大模型能够更稳定地调用远程服务。

---

# 2025-05-19
feat(vision-arm-control、function-call): 新增机械臂方向控制function-call及系统提示完善

本次修改的主要目标是为机械臂控制系统新增前后左右上下六个方向的精细移动function-call，并系统性完善function-call功能说明和LLM系统提示，确保大模型能够准确理解和调用所有能力。

主要修改内容：

1. **新增机械臂方向控制function-call**  
   - 在 `vision_arm_control.py` 中新增六个异步接口：
     * `call_robot_arm_move_forward(distance)`
     * `call_robot_arm_move_back(distance)`
     * `call_robot_arm_move_left(distance)`
     * `call_robot_arm_move_right(distance)`
     * `call_robot_arm_move_up(distance)`
     * `call_robot_arm_move_down(distance)`
   - 每个接口均校验参数为正数，单位为厘米，并通过POST请求转发到后端API。

2. **function-call注册与schema完善**  
   - 在 `function_call_tools.py` 中注册上述六个新函数，提供详细描述，明确参数要求。
   - 在 `function_schema.py` 中为每个新函数添加schema，详细说明参数类型、单位、推荐范围及调用场景。

3. **系统提示(system_prompt)全面升级**  
   - 在 `config.yaml` 的 `system_prompt` 中，系统性梳理所有function-call能力，分门别类详细列出：
     * 物体识别与场景理解（如shape_get_all_objects等）
     * 机械臂基础控制（抓取、放置、归位、堆叠等）
     * 机械臂方向控制（前后左右上下六个方向的精细移动，参数要求、推荐范围等）
     * 云台摄像头控制
     * 音频控制（音乐、TTS音量等）
     * 其他工具（如计算器）
   - 明确每个function-call的触发语境、参数要求和典型用法，特别强调机械臂操作前必须先识别物体，方向控制函数适用于微调等场景。
   - 丰富了物体识别的触发词，避免LLM因表达不同而漏掉function-call调用。
   - 强调禁止使用固定/随机坐标，所有机械臂操作参数必须来源于物体检测结果或用户明确指定。

4. **文档与版本记录同步**  
   - 版本记录中详细说明了本次功能扩展的背景、实现细节和对系统可靠性的提升。

**总结**  
通过本次修改，系统实现了机械臂的全方位精细控制，所有function-call能力和参数要求在系统提示中得到系统性梳理和明确，大模型能够更智能、准确地理解和调用各项功能，极大提升了语音助手与智能机械臂系统的协作能力和用户体验。

---

# 2025-05-19
fix(vision-arm-control): 修正机械臂控制参数传递问题

本次修改的主要目标是解决大模型在调用机械臂控制函数时参数传递错误的问题。大模型之前使用硬编码的坐标值(如300,300)而不是从物体检测结果中获取的距离参数。

主要修改内容:

1. **参数名称统一化**:
   * 将机械臂控制相关函数的参数从`image_x`和`image_y`改为`distance_x`和`distance_y`
   * 使参数名称与shape_get_all_objects返回的字段名完全匹配，减少大模型混淆
   * 在API调用中保持向后兼容，将distance_x/distance_y映射到API需要的image_x/image_y

2. **参数验证增强**:
   * 添加`_validate_distances`函数，检查传入的坐标参数是否在合理范围内
   * 识别常见的固定值模式(如300,300)，拒绝大模型使用硬编码值
   * 为验证失败情况添加明确的错误信息，指导大模型正确使用参数

3. **函数描述与文档改进**:
   * 更新function_call_tools.py中的函数注册描述，强调参数必须直接使用shape_get_all_objects结果
   * 修改function_schema.py中的参数描述，提供详细的使用步骤和警告
   * 添加示例流程，说明正确的参数获取和使用方法

4. **系统提示优化**:
   * 在config.yaml的LLM系统提示中更新机械臂控制说明
   * 添加明确的步骤指导，确保大模型遵循正确的调用流程
   * 强调参数名称与数据源的一致性，禁止使用固定值

总结:
通过本次修改，解决了大模型在调用机械臂控制函数时使用错误参数的问题。参数名称的统一化使大模型能更直观地理解应使用shape_get_all_objects返回的distance_x和distance_y值，增强的参数验证机制为错误使用提供明确反馈，系统提示的优化确保大模型遵循正确的调用步骤。这些改进显著提高了机械臂控制的准确性和可靠性。

---

# 2025-05-18
fix(remote-yolo、vision-arm-control): 修复远程人脸追踪、机械臂控制服务地址配置错误

本次修改的主要目标是修复远程人脸追踪、机械臂控制服务地址配置错误，
将人脸追踪服务地址由 "http://***********:5001" 改为 "http://***********:5000"。
将机械臂控制服务地址由 "http://***********:5000" 改为 "http://***********:5002"。

主要修改内容:

1. **远程人脸追踪服务地址修改**:
   * 在 `backend/utils/function_call/remote_yolo_control.py` 中将 `base_url` 由 "http://***********:5001" 改为 "http://***********:5000"

2. **远程机械臂控制服务地址修改**:
   * 在 `backend/utils/function_call/vision_arm_control.py` 中将 `base_url` 由 "http://***********:5000" 改为 "http://***********:5002"

**总结**:
通过本次修改，成功解决了远程人脸追踪、机械臂控制服务地址配置错误的问题。

---

# 2025-05-18
fix(audio-player): 实现通过关键词选择输出音频设备

本次修改的主要目标是解决TTS音频播放器在特定系统环境中设备不可用的问题。通过实现类似ASR服务的设备关键词选择机制，系统可以更智能地选择正确的音频输出设备。

主要修改内容:

1. **音频播放器设备选择机制扩展**:
   * 在 `backend/utils/audio_new_player.py` 中添加了 `_select_output_device()` 方法
   * 修改 `__init__` 方法，添加 `device_keyword` 参数支持
   * 实现了类似ASR服务的设备关键词匹配功能，自动查找包含特定关键词的输出设备

2. **配置类扩展与加载**:
   * 在 `backend/utils/config_loader.py` 的 `AudioPlayerConfig` 类中添加 `device_keyword` 字段
   * 修改 `get_audio_player_config()` 方法，支持从配置文件加载 `device_keyword` 参数

3. **配置文件更新**:
   * 在 `backend/config.yaml` 中的 `audio_player` 配置部分添加 `device_keyword: "Y1076"` 配置项
   * 将 `device` 参数值设置为 `null`，优先使用关键词匹配机制选择设备

4. **设备选择流程优化**:
   * 添加详细的设备查找日志，方便调试
   * 实现了多设备情况下的智能选择策略
   * 添加了优雅的降级机制，当找不到匹配设备时回退到默认设备

**总结**:
通过本次修改，音频播放器现在能够像ASR服务一样，通过关键词智能选择合适的输出设备，有效解决了"设备不可用"（Device unavailable [PaErrorCode -9985]）的错误。该机制提高了系统在复杂音频设备环境下的适应性和稳定性，用户可以在配置文件中通过简单的关键词指定想要使用的音频输出设备，无需了解具体的设备索引，大大提升了系统的易用性和可靠性。

---

# 2025-05-18
fix(music-player): 修复音乐播放器在config.yaml当中配置的音乐资源的路径

本次修改主要修改了音乐资源文件夹的路径，由"./backend/resource"改为"resource"

1. **音乐播放器资源文件夹路径修改**:
   * 在 `backend/config.yaml` 中将 `music_player` 配置部分中的 `resource_dir` 路径由 "./backend/resource" 改为 "resource"

**总结**:
通过本次修改，成功解决了音乐播放器在config.yaml当中配置的音乐资源的路径问题。

---

# 2025-05-18
fix(music-player): 修复音乐播放器function-call功能问题

本次修改的主要目标是解决语音助手系统中音乐播放器的function-call功能问题，确保当用户通过语音命令请求播放音乐时，系统不仅返回成功消息，还能实际播放指定的音乐文件。
新增asr模型的int8量化模型（.int8.ort）

主要修改内容:

1. **音乐播放器配置优化**:
   * 在 `backend/config.yaml` 中新增 `music_player` 配置部分
   * 添加 `resource_dir` 配置项，指定音乐资源目录路径为 "./backend/resource"
   * 添加 `volume` 配置项，将默认音量设置为0.7
   * 添加 `supported_formats` 配置项，指定支持的音乐文件格式为 [".mp3", ".wav", ".flac"]

2. **配置加载器扩展**:
   * 在 `backend/utils/config_loader.py` 中添加 `MusicPlayerConfig` 数据类
   * 实现 `get_music_player_config()` 方法，用于加载音乐播放器配置
   * 添加合理的默认值和错误处理机制

3. **音乐播放器实现优化**:
   * 修改 `backend/utils/function_call/function_tools_impl.py` 中的 `MusicPlayer` 类
   * 使用配置文件中的资源目录路径，替代硬编码的路径
   * 使用配置文件中的音量设置，提高默认音量
   * 使用配置文件中的支持格式列表，增强灵活性

4. **错误处理与日志增强**:
   * 添加更详细的日志记录，包括音乐目录加载、音乐文件查找和播放过程
   * 优化错误处理逻辑，提供更友好的错误提示
   * 在关键操作点添加日志，便于问题定位和调试

5. **音频播放优化**:
   * 将TTS播放器的默认音量从0.1调整为0.5，确保语音输出清晰可听
   * 将音乐播放器的默认音量设置为0.7，确保音乐播放效果良好
   * 优化音乐文件查找逻辑，提高匹配成功率

6. **ASR模型优化**:
   * 新增asr模型的int8量化模型（.int8.ort）
   * 在 `backend/config.yaml` 中将asr模型的模型文件设置为 "model.int8.ort"

**总结**:
通过本次修改，成功解决了音乐播放器的function-call功能问题。修复的核心是将硬编码的音乐资源路径改为从配置文件中读取，并提高了默认音量设置。这些改进不仅解决了当前问题，还提高了系统的可配置性和可维护性，使音乐播放功能更加稳定可靠。用户现在可以通过语音命令成功播放指定的音乐文件，增强了语音助手系统的实用性和用户体验。
解决了asr模型int8量化模型的问题，提高了ASR模型的推理速度。

---

# 2025-05-18
feat(function-call): 集成机械臂动作组函数到语音助手系统

本次修改的主要目标是将IntelligentVisionArmControl项目中的机械臂动作组函数集成到语音对话助手系统中，使其可通过LLM的function-call功能调用。

主要修改内容:

1. **后端包装函数实现**:
   * 在 `backend/utils/function_call/vision_arm_control.py` 中添加了新的包装函数：
     - `call_robot_arm_init_action()`: 用于调用机械臂初始化动作
     - `call_robot_arm_normal_put_object()`: 用于调用机械臂普通放置动作
     - `call_robot_arm_stack_put_object()`: 用于调用机械臂堆叠放置动作
   * 每个函数都实现了完整的错误处理和日志记录

2. **函数Schema定义**:
   * 在 `backend/utils/function_call/function_schema.py` 中为新增函数定义了JSON Schema
   * 为每个函数提供了详细的中文描述和参数说明
   * 添加了推荐参数值（如堆叠放置推荐高度为140毫米）

3. **函数注册与导入**:
   * 在 `backend/utils/function_call/function_call_tools.py` 中导入并注册了新函数
   * 使用 `@FunctionRegistry.register_function` 装饰器注册函数
   * 为每个函数提供了详细的中文描述

4. **API端点实现**:
   * 在 `IntelligentVisionArmControl/src/web/app.py` 中添加了新的API端点：
     - `/api/arm/init_action`: 用于执行机械臂初始化动作
     - `/api/arm/normal_put_object`: 用于执行机械臂普通放置动作
     - `/api/arm/stack_put_object`: 用于执行机械臂堆叠放置动作
   * 每个端点都实现了参数验证、错误处理和日志记录

**总结**:
通过本次修改，成功将IntelligentVisionArmControl项目中的机械臂动作组函数集成到了语音对话助手系统中。用户现在可以通过语音命令控制机械臂执行初始化、抓取、普通放置和堆叠放置等动作，大大增强了系统的交互能力和实用性。这些新增功能使语音助手系统的机械臂控制能力更加丰富和灵活，能够满足更多复杂场景的需求。

---

# 2025-05-18
feat(frontend): 实现前后端WebSocket连接并优化UI交互

本次会话的主要目标是实现前端与后端之间的真实WebSocket连接，并优化前端界面的交互体验，特别是语音对话模式和3D可视化组件。

主要修改内容:

1. **前后端WebSocket连接实现**:
   * 修改 `Frontend/components/websocket-context.tsx`，将模拟WebSocket连接替换为真实的WebSocket连接
   * 添加环境变量配置，通过 `.env.local` 文件设置WebSocket服务器地址
   * 实现连接错误处理、重连机制和连接状态管理
   * 确保前端能正确处理后端发送的所有消息类型（SYSTEM_STATUS, LLM_CHUNK, TTS_STATUS等）

2. **语音对话模式优化**:
   * 实现语音对话模式和键盘对话模式的切换功能
   * 在语音对话模式下，ASR识别结果自动发送给LLM处理，无需手动点击发送按钮
   * 修复麦克风按钮交互问题，确保点击麦克风按钮本身不会触发文字输入
   * 优化模式切换提示，使用toast通知代替聊天消息

3. **UI界面优化**:
   * 优化顶部导航栏布局，将"Stop Generating"按钮移至单独的一行
   * 在键盘输入模式下完全隐藏麦克风按钮，而不是仅将其设置为禁用状态
   * 修复"Stop Generating"按钮的显示问题，确保在生成完成后按钮会自动消失
   * 优化工具调用的显示样式，使其更加清晰

4. **3D可视化组件优化**:
   * 修改"Idle"状态动画效果，使用黑灰色调和微弱脉动效果
   * 修改"Speaking"状态动画效果，使用蓝紫色系和明显的抖动形变
   * 实现状态之间的平滑过渡动画，过渡时间控制在300-500毫秒之间
   * 修复状态切换问题，确保在TTS播放语音时球体能正确切换到"Speaking"状态

5. **错误处理与状态管理**:
   * 添加WebSocket连接状态指示器，显示连接状态（已连接/未连接）
   * 实现连接错误提示，使用toast通知显示错误信息
   * 优化Function-call工具调用显示问题，确保所有工具调用都在对话界面中可见
   * 修复LLM回复完整性问题，确保在工具调用时文本内容不会丢失

**总结**:
通过本次修改，前端与后端之间实现了真实的WebSocket连接，并优化了前端界面的交互体验，特别是语音对话模式和3D可视化组件。这些改进使得用户体验更加流畅自然，界面更加美观直观。

---

# 2025-05-17
feat(docs): 编写前端项目说明文档；上传前端项目到github

本次会话的主要目标是编写前端项目说明文档，包括项目结构说明、项目启动说明、项目运行说明、项目测试说明、项目部署说明等；上传前端项目到github。

主要修改内容:

1.  **项目结构说明**:
    *   新增 `frontend/README.md` 文件，详细描述了前端项目的目录结构、项目启动说明、项目运行说明、项目测试说明、项目部署说明等。
    *   更新 `README.md` 文件，增加了对 `frontend/README.md` 的引用。

2.  **项目启动说明**:
    *   新增 `frontend/README.md` 文件，详细描述了前端项目的目录结构、项目启动说明、项目运行说明、项目测试说明、项目部署说明等。

3. **上传前端项目到github**:
    *   上传前端项目到github。

**总结**:
通过本次修改，前端项目说明文档已编写完成，并上传到github。

---

# 2025-05-16
feat(docs): 编写后端项目说明文档

本次会话的主要目标是编写后端项目说明文档，包括项目结构说明、项目启动说明、项目运行说明、项目测试说明、项目部署说明等。

主要修改内容:

1.  **项目结构说明**:
    *   新增 `backend/README.md` 文件，详细描述了后端项目的目录结构、项目启动说明、项目运行说明、项目测试说明、项目部署说明等。
    *   更新 `README.md` 文件，增加了对 `backend/README.md` 的引用。

2.  **项目启动说明**:
    *   新增 `backend/README.md` 文件，详细描述了后端项目的目录结构、项目启动说明、项目运行说明、项目测试说明、项目部署说明等。

---

# 2025-05-15
feat(project): 调整项目结构为前后端分离，并修复server.py启动错误

本次会话的核心目标是为项目向前后端分离架构演进奠定基础，并解决 `server.py` 在此过程中出现的多个启动时错误。主要包括讨论并确定了新的项目目录结构，以及对 `server.py` 和 `app/shared_state.py` 的多次迭代修改。

主要修改内容:

1.  **项目结构调整 (讨论与规划)**:
    *   **目标**: 将现有项目重构为更清晰的前后端分离结构。
    *   **建议结构**:
        *   `backend/`: 包含所有现有Python后端代码 (`server.py`, `app/`, `llm/`, `tts/`, `asr/`, `utils/`, `model/`, `config.yaml`, `requirements.txt` 等)。
        *   `frontend/`: 新建目录，用于存放未来的前端代码 (如React, Vue, Svelte等)。
        *   `docs/`: 保留，存放项目文档，如 `websocket_api.md`。
        *   顶层文件: `README.md`, `.gitignore`, `version.md` 等。
    *   **理由**: 提升关注点分离、独立开发/部署能力、技术栈灵活性及可维护性。
    *   **后续**: 已开始将代码移至 `backend/` 目录结构下。

2.  **`asr-llm-tts/backend/server.py` 启动错误修复**:
    *   **`IndentationError` (多处)**:
        *   修复了 `startup_event` 中 `if tts_config_obj:` 和 `if player_config_obj:` 后代码块的缩进问题。
        *   修复了 `websocket_endpoint` 中 `shared_state.current_turn_task = asyncio.create_task(turn_wrapper())` 附近的缩进，确保其位于正确的 `if user_text is not None:` 块内。
    *   **`SyntaxError` (invalid syntax on else)**:
        *   修复了 `websocket_endpoint` 中处理 `MSG_TYPE_INTERRUPT_TURN` 消息时，其内部 `else` 块的缩进问题，确保了外层 `if/elif/else` 消息类型判断链的正确性。

3.  **`asr-llm-tts/backend/app/shared_state.py` 清理与错误修复**:
    *   **`NameError: name 'queue' is not defined`**:
        *   由于 `user_input_queue` (依赖 `queue` 模块) 实际上是旧输入模式的残留，且 `import queue` 已被移除。
        *   最终解决方案是彻底从 `shared_state.py` 中移除了 `user_input_queue`、`new_input_event`、`main_loop_is_running`、`main_loop` 等与旧 `main.py` 循环和输入处理相关的未使用变量及其注释。
        *   确保了 `shared_state.py` 只包含当前 WebSocket 架构下实际需要的共享状态变量 (`current_turn_stop_event`, `current_turn_task`)。

4.  **WebSocket API 文档 (`asr-llm-tts/docs/websocket_api.md`) 完善**:
    *   **格式修正**: 将文档中所有JSON示例代码块的格式从 `"'json ... “”` 调整为标准的Markdown三反引号格式 (```json ... ```)。
    *   **内容补充**:
        *   根据 `turn_manager` 的设计，在S2C消息中补充了 `TOOL_CALL_START` 和 `TOOL_CALL_RESULT` 消息类型的详细定义，包括用途、载荷结构和示例。
        *   对部分消息的载荷字段描述进行了细化。

**总结**:
经过本次一系列的调整和修复，`server.py` 应该能够成功启动。项目结构向前后端分离迈出了重要的一步，并且核心的WebSocket通信协议也得到了进一步的完善和规范化。这些改动为后续的前端开发和后端功能的稳定运行打下了良好基础。

---

# 2025-05-15
feat(refactor): 完成阶段六部分任务，包括废弃模块移除与WebSocket API文档创建

本次会话主要集中在执行 `step.md` 中定义的阶段六“收尾与前端对接准备”任务。我们成功移除了项目中不再需要的旧模块，并为前后端通过 WebSocket 通信创建了详细的 API 文档。

主要修改内容:

1.  **任务 6.1: 移除废弃模块，整理 `shared_state` (部分完成)**:
    *   **`asr-llm-tts/app/main.py`**:
        *   文件已成功删除。该模块的功能已完全迁移到 `server.py`。
    *   **`asr-llm-tts/app/input_handler.py`**:
        *   文件已成功删除。键盘输入等功能将由未来的 Web UI 处理。
    *   **`asr-llm-tts/app/shared_state.py`**:
        *   计划中的清理工作（移除与旧主循环和输入处理相关的未使用变量）标记为 `[PENDING]`，因用户反馈之前的修改尝试被拒绝。
    *   **`step.md`**:
        *   更新了任务 6.1 的状态为 `[IN PROGRESS]`，并详细列出了已完成（模块移除）和待处理（`shared_state.py` 清理）的子项。

2.  **任务 6.2: 后端API文档与前端对接准备 (部分完成)**:
    *   **`asr-llm-tts/docs/websocket_api.md`**:
        *   成功创建并填充了该文件。
        *   文档详细定义了客户端 (C2S) 和服务器端 (S2C) 的所有 WebSocket 消息类型，包括：
            *   C2S: `USER_TEXT_INPUT`, `INTERRUPT_TURN`
            *   S2C: `SYSTEM_STATUS`, `LLM_CHUNK`, `LLM_FINAL_RESPONSE`, `TTS_STATUS`, `ASR_FINAL_RESULT`, `ERROR_MESSAGE`, `TURN_ENDED`, `TURN_INTERRUPTED`
        *   每个消息类型都包含了用途说明、载荷结构 (payload) 和 JSON 示例，为前端开发提供了清晰的接口规范。
    *   **`step.md`**:
        *   更新了任务 6.2 的状态为 `[IN PROGRESS]`，并指明 API 文档已完成。

3.  **`step.md` 计划书同步**:
    *   根据用户最新的修改，`step.md` 中 **任务 4.3 (前端开关控制ASR结果是否用于触发LLM对话)** 的状态已由用户恢复为未完成。我的更新仅针对任务 6.1 和 6.2 的状态描述。

**总结**:
通过本次修改，项目结构得到了进一步清理，核心的 WebSocket 通信协议也已文档化，为后续的前端开发和对接工作奠定了坚实的基础。`shared_state.py` 的清理仍是待办事项。

---

# 2025-05-15
feat(server): 实现WebSocket中断逻辑与初步的统一状态消息推送

本次提交完成了项目后端化改造的阶段五，核心目标是增强服务器的交互鲁棒性和状态透明度，主要包括实现通过WebSocket中断当前对话回合，以及在关键处理节点推送状态/错误信息。

主要修改内容 (`server.py`, `app/turn_manager.py`):

1.  **实现 WebSocket 中断逻辑 (任务 5.1)**:
    *   **`server.py` - 处理 `MSG_TYPE_INTERRUPT_TURN`**:
        *   在 `websocket_endpoint` 中添加了对 `MSG_TYPE_INTERRUPT_TURN` 消息的处理逻辑。
        *   当收到此消息时，服务器会：
            *   检查是否存在活动的 `shared_state.current_turn_task`。
            *   如果存在，则设置与该任务关联的 `shared_state.current_turn_stop_event`，以通知 `turn_manager` 和相关的子任务（如TTS播放）停止。
            *   立即调用 `audio_player.stop()` 来停止任何正在播放的音频。
            *   向发起中断的客户端发送 `MSG_TYPE_TURN_INTERRUPTED` 消息作为确认。
            *   记录相关的日志信息。
        *   如果无活动任务可中断，则发送 `MSG_TYPE_SYSTEM_STATUS` 告知客户端。
    *   **`server.py` - `send_to_client_callback` 调整**:
        *   为了让 `INTERRUPT_TURN` 处理器能直接使用回调向特定客户端发送消息，`send_to_client_callback` 函数的签名被修改为显式接收 `ws: WebSocket` 作为其第一个参数。
        *   所有在 `turn_wrapper` 中对 `send_to_client_callback` 的调用都已更新，以适配新的签名（通过lambda传递当前 `websocket` 对象）。

2.  **统一的状态/错误消息推送 (任务 5.2 - 初步实现)**:
    *   **`server.py` - 回合处理开始状态**:
        *   在 `turn_wrapper` 函数开始执行时（即收到用户输入，准备调用核心对话逻辑前），会向客户端发送一条 `MSG_TYPE_SYSTEM_STATUS` 消息，内容如 `{"module": "turn_handler", "status": "processing_started", "user_input": "..."}`，告知客户端该用户输入已开始处理。
    *   **`app/turn_manager.py` - 对话逻辑开始状态**:
        *   在 `handle_turn` 函数开始执行其核心逻辑时，会通过 `websocket_callback` 向客户端发送一条 `MSG_TYPE_SYSTEM_STATUS` 消息，内容如 `{"module": "turn_manager", "status": "logic_started", "current_input": "..."}`。
    *   **现有错误消息**: 各模块（`turn_manager`, `tts_player`, `server.py`的`turn_wrapper`）中已有的错误处理逻辑会通过回调发送 `ERROR_MESSAGE` 或特定类型的错误状态（如`TTS_STATUS`中的error）。
    *   **未来可增强**: 计划中提及的更细致的ASR服务启动失败或ASR监听任务异常时的状态/错误通知，可以作为后续的增强点。

通过这些修改，系统现在允许客户端主动中断正在进行的处理流程，提高了交互的灵活性。同时，通过在处理的关键起始点发送系统状态消息，增强了前端对后端处理进度的感知能力。

---

# 2025-05-15
feat(server/asr): 集成ASR服务生命周期管理与结果推送

本次提交完成了项目后端化改造的阶段四（核心部分），目标是使服务器能够管理ASR（自动语音识别）服务的生命周期，并在ASR服务产生识别结果时，通过WebSocket将这些结果推送给客户端。

主要修改内容 (`server.py`):

1.  **ASR 服务生命周期管理 (任务 4.1)**:
    *   **启动时 (`startup_event`)**:
        *   读取配置文件中的 `input_mode`。
        *   如果 `input_mode` 设置为 `"asr"`:
            *   加载ASR相关的配置 (`ASRConfig`)。
            *   创建一个 `multiprocessing.Queue` (`asr_result_queue`)，用于从ASR服务进程接收识别结果。
            *   启动 `asr.asr_service.run_asr_service` 函数作为一个新的守护（daemon）进程 (`asr_service_process`)。该进程负责实际的语音捕获和识别。
            *   必要的模块（如 `multiprocessing`, `ASRConfig`, `run_asr_service`）已被导入。
            *   相应的全局变量 (`asr_service_process`, `asr_result_queue`, `input_mode`) 被添加和管理。
    *   **关闭时 (`shutdown_event`)**:
        *   新增了 FastAPI 的 `shutdown` 事件处理器。
        *   在该处理器中，会先尝试优雅地终止 `asr_service_process` (发送 SIGTERM 并等待其结束)。
        *   如果超时后进程仍未结束，则会强制终止 (发送 SIGKILL)。
        *   同时，TTS客户端的关闭逻辑也移至此统一的 `shutdown_event` 中。

2.  **ASR 结果监听与推送 (任务 4.2)**:
    *   **创建 `asr_listener_task`**:
        *   在 `startup_event` 中，如果ASR服务成功启动，会创建一个名为 `listen_and_forward_asr_results` 的 `asyncio.Task` (`asr_listener_task`)。
    *   **`listen_and_forward_asr_results` 函数**:
        *   此异步函数在一个循环中运行。
        *   它通过 `await asyncio.to_thread(asr_result_queue.get, timeout=0.5)` 从 `asr_result_queue` 中非阻塞地获取ASR识别出的文本。
        *   获取到文本后，它会遍历所有当前活跃的WebSocket连接 (`active_connections`)。
        *   对每个连接的客户端，它将识别的文本封装成 `MSG_TYPE_ASR_FINAL_RESULT` 类型的JSON消息 (`{"type": "ASR_FINAL_RESULT", "payload": {"text": "识别的文本"}}`) 并通过WebSocket发送。
    *   **关闭 `asr_listener_task`**:
        *   在 `shutdown_event` 中，会先尝试取消 `asr_listener_task` 并等待其完成。

3.  **关于任务 4.3 (前端控制ASR触发LLM)**:
    *   根据讨论，此任务的核心逻辑调整为在前端实现。
    *   后端的核心职责是：如果ASR服务被配置并启动，则持续通过 `MSG_TYPE_ASR_FINAL_RESULT` 消息将所有识别结果推送给所有连接的客户端。
    *   前端将负责提供UI开关，并根据此开关的状态决定是否将接收到的 `ASR_FINAL_RESULT` 中的文本内容重新包装成 `USER_TEXT_INPUT` 消息发送回服务器，从而触发LLM对话流程。当前后端代码已支持此模式，无需进一步修改后端以实现此前端开关。

通过这些修改，服务器现在具备了在ASR模式下独立运行语音识别服务、收集识别结果，并将这些结果实时推送给所有WebSocket客户端的能力。这为前端实现语音输入功能，并决定何时将语音输入转化为与LLM的交互提供了后端支持。

---

# 2025-05-15
feat(tts_player/turn_manager): 集成TTS播放状态回调

本次提交完成了项目后端化改造的阶段三，核心目标是在TTS播放过程中，通过WebSocket向客户端反馈播放状态。

主要修改内容:

1.  **`app/tts_player.py` (任务 3.1: `stream_tts_and_play` 改造以支持状态回调)**:
    *   **新增 `websocket_callback` 参数**:
        *   `stream_tts_and_play` 函数签名中增加了 `websocket_callback: Optional[Callable[[str, Dict[str, Any]], Any]]` 参数。
    *   **定义内部辅助函数 `_send_tts_status`**:
        *   此异步函数用于统一处理向客户端发送 `TTS_STATUS` 消息的逻辑。它会检查 `websocket_callback` 是否存在，如果存在，则调用它发送包含状态 (`status`) 和可选消息 (`message`) 的载荷。
    *   **发送 `TTS_STATUS` 消息**:
        *   **播放开始时**: 在实际调用 `player.play_stream` 之前，调用 `await _send_tts_status("speaking")`，通知客户端TTS已开始播放。
        *   **播放正常结束时**: 在 `player.play_stream` 正常完成后，调用 `await _send_tts_status("idle")`，通知客户端TTS播放已结束，状态为空闲。
        *   **播放中断时**: 在捕获到 `AudioPlayerError`指示播放被停止或取消（通常由外部中断引起）时，调用 `await _send_tts_status("idle", message="Playback interrupted: ...")`。
        *   **播放出错时**:
            *   当捕获到明确的 `AudioPlayerError`（非中断类型）时，调用 `await _send_tts_status("error", message="Playback error: ...")`。
            *   当捕获到其他意外异常时，调用 `await _send_tts_status("error", message="Unexpected TTS/Playback error: ...")`。
    *   **日志保留**: 保留了原有的 `logger` 调用。

2.  **`app/turn_manager.py` (任务 3.2: 向 `stream_tts_and_play` 传递回调)**:
    *   在 `handle_turn` 函数内部，所有对 `app.tts_player.stream_tts_and_play` 的调用处，都将从 `handle_turn` 自身接收到的 `websocket_callback` 参数透传了过去。
    *   这意味着当 `handle_turn` 启动TTS播放任务时，`stream_tts_and_play` 函数现在拥有了将状态直接回调给最初的WebSocket的能力。

通过这些修改，当TTS进行播放时，客户端能够实时收到关于播放状态的更新（如"正在讲话"、"空闲"、"错误"），这为前端UI展示TTS活动状态提供了必要的信息。

---

# 2025-05-15
feat(server/turn_manager): 集成对话核心逻辑 (Turn Manager) 并实现回调输出

本次提交完成了项目后端化改造的阶段二，核心目标是将对话管理逻辑 (`turn_manager`) 集成到 FastAPI 服务器中，并通过 WebSocket 实现双向通信。

主要修改内容:

1.  **`app/turn_manager.py` (任务 2.1: `handle_turn` 改造)**:
    *   **引入 `websocket_callback`**:
        *   `handle_turn` 函数新增 `websocket_callback: Callable[[str, Dict], Any]` 参数，用于将事件和数据异步发送回调用方 (即 `server.py` 中的 WebSocket 处理器)。
        *   如果未提供回调，则使用一个临时的打印回调，以保持局部测试能力。
    *   **结构化消息输出**:
        *   移除了原先直接 `print` 到终端的逻辑。
        *   在对话处理的各个关键节点，通过 `await websocket_callback(MSG_TYPE_XXX, payload_dict)` 发送定义好的消息类型和数据：
            *   `MSG_TYPE_LLM_CHUNK`: 发送 LLM 流式输出的文本块。
            *   `MSG_TYPE_LLM_FINAL_RESPONSE`: 发送 LLM 完整的最终回复文本。
            *   `MSG_TYPE_TOOL_CALL_START`: 通知客户端工具调用开始，包含工具名称、ID和参数。
            *   `MSG_TYPE_TOOL_CALL_RESULT`: 发送工具执行的结果（成功或失败）及相关数据。
            *   `MSG_TYPE_ERROR_MESSAGE`: 发送在 `handle_turn` 内部发生的各种错误信息（如 LLM 错误、工具执行错误等）。
    *   **日志保留**: 保留了原有的 `logger.info/debug/error` 调用，确保详细的后端处理日志依然存在。

2.  **`server.py` (任务 2.2: 调用改造后的 `handle_turn` 并集成到 WebSocket)**:
    *   **依赖导入**: 导入了 `asyncio`, `queue`, `app.shared_state`, 和 `app.turn_manager`。
    *   **组件初始化**: 确保了核心组件如 `llm_client`, `tts_client`, `audio_player`, `tools_schema`, `tts_config_obj`, `system_prompt` 在 FastAPI `startup` 事件中正确加载并设为全局变量。
    *   **WebSocket 消息处理 (`/ws/chat` 端点)**:
        *   当收到 `MSG_TYPE_USER_TEXT_INPUT` 类型的消息时：
            *   **中断处理 (初步)**: 增加了对上一个正在运行的 `shared_state.current_turn_task` 的中断请求逻辑（设置其 `stop_event` 并尝试停止播放器）。
            *   **回合上下文创建**: 为当前新的对话回合创建了独立的 `shared_state.current_turn_stop_event` (类型为 `asyncio.Event`) 和 `text_queue_for_tts_this_turn` (类型为 `asyncio.Queue`)。
            *   **定义 `send_to_client_callback`**:
                *   创建了一个异步回调函数 `send_to_client_callback(event_type: str, data_payload: dict)`。
                *   此回调函数负责将从 `handle_turn` 接收到的事件类型和数据载荷封装成 JSON，通过当前 WebSocket 连接发送给客户端。
                *   包含日志记录，用于调试发送给客户端的内容。
            *   **定义 `turn_wrapper`**:
                *   创建了一个异步任务包装器 `turn_wrapper()`。
                *   该包装器负责调用 `turn_manager.handle_turn`，并向其传递所有必要的参数，包括用户输入、LLM/TTS/Player 实例、新创建的停止事件和文本队列、工具列表、TTS配置、系统提示词以及 `send_to_client_callback`。
                *   `turn_wrapper` 捕获 `handle_turn` 可能发生的异常（包括 `asyncio.CancelledError`），并在回合正常结束、被取消或出错时，通过 `send_to_client_callback` 向客户端发送 `MSG_TYPE_TURN_ENDED` 消息，其中包含回合的状态 (`completed`, `cancelled`, `error`)。
            *   **异步任务执行**: 使用 `asyncio.create_task(turn_wrapper())` 启动对话回合处理，并将返回的 `Task` 对象存储在 `shared_state.current_turn_task` 中，以便后续管理（如中断）。
    *   **消息常量对齐**: 确保了 `server.py` 中定义的 WebSocket 消息类型常量与 `step.md` 计划中的定义保持一致。

这些修改使得后端能够通过 WebSocket 接收用户文本输入，完整地执行一个对话回合（包括潜在的 LLM 调用和工具使用），并将所有中间和最终结果结构化地、流式地反馈给客户端。为后续的 TTS 播放集成、ASR 输入集成以及更完善的中断处理奠定了坚实的基础。

---

# 2025-05-15
feat(server): 搭建 FastAPI 后端基础与 WebSocket 通信

本次提交完成了将项目改造为 FastAPI 后端的第一阶段，主要包括：

1.  **FastAPI 应用初始化 (`server.py`)**:
    *   创建了 `server.py` 作为新的后端主入口。
    *   引入 FastAPI 并配置了基本的应用实例和 Uvicorn 运行参数。
    *   安装了必要的依赖：`fastapi`, `uvicorn`, `websockets`, `python-multipart`。

2.  **核心组件加载与管理 (`server.py`)**:
    *   将原 `app/main.py` 中的配置加载逻辑 (`ConfigLoader`) 和核心服务客户端 (`LLMClient`, `TTSClient`, `AudioNewPlayer`) 的初始化迁移到 FastAPI 的 `startup` 事件中。
    *   全局管理这些核心组件的实例。
    *   确保 `music_player_instance` 能正确获取到 `AudioNewPlayer` 实例。
    *   加载了函数调用工具 schema 和系统提示词。

3.  **WebSocket 端点与消息协议 (`server.py`)**:
    *   定义了客户端与服务端之间 WebSocket 通信的消息类型常量 (如 `MSG_TYPE_USER_TEXT_INPUT`, `MSG_TYPE_ERROR_MESSAGE` 等)。
    *   创建了 WebSocket 端点 `/ws/chat`，用于接受客户端连接。
    *   实现了基本的用户文本输入处理：服务器能接收符合 `{"type": "USER_TEXT_INPUT", "payload": {"text": "..."}}` 格式的 JSON 消息，并在终端打印接收到的文本。
    *   对无效或未知类型的 WebSocket 消息，服务器会向客户端回复错误信息。

4.  **测试 WebSocket 客户端 (`test/test_websocket_client.py`)**:
    *   新增了一个简单的异步 Python WebSocket 客户端 (`test_websocket_client.py`)。
    *   该客户端可以连接到服务器，允许用户输入文本，将文本封装后发送给服务器，并打印服务器的响应。
    *   用于方便地测试 WebSocket 连接和基本的消息收发功能。

此阶段为后续集成完整的对话逻辑、ASR语音输入和TTS播放等功能奠定了基础架构。

---

# 2025-05-15
feat(ASR): 实现麦克风动态选择与输入音频重采样

主要解决问题：
- 修复了ASR服务因硬编码麦克风设备名 (如 "hw:0,0") 导致的在更换设备或设备采样率不匹配时的兼容性问题。
- 解决了特定麦克风（如 UGREEN CM564）原生采样率（48kHz）与ASR模型期望采样率（16kHz）不符，且系统无法自动转换导致的错误。

主要修改内容：

1.  **配置更新 (`config.yaml` & `utils/config_loader.py`)**:
    *   在 `config.yaml` 的 `asr` 配置中，移除了旧的 `alsa_device_name`。
    *   新增 `target_microphone_keyword` 配置项，允许用户通过设备名称中的关键词（如 "Yundea", "UGREEN"）来指定所用麦克风。若留空，则尝试使用系统默认输入设备。
    *   相应更新了 `utils/config_loader.py` 中的 `ASRConfig` dataclass 和 `get_asr_config` 方法以支持新配置项。

2.  **ASR服务增强 (`asr/asr_service.py`)**:
    *   **动态设备选择 (`_select_input_device`)**:
        *   该方法现在会根据 `target_microphone_keyword` 查找并选择匹配的输入设备。
        *   如果关键词为空或未找到匹配设备，则回退到使用系统默认输入设备。
        *   关键变更：该方法现在会尝试以ASR模型期望的采样率（16kHz）打开设备。如果失败，则会查询设备的默认/原生采样率，并尝试以该采样率打开。
        *   返回选定的设备标识符、设备实际将被打开的采样率以及用于日志的设备名。
    *   **输入音频重采样**:
        *   在 `_main_loop` 中，`sd.InputStream` 现在使用设备实际支持的采样率打开。
        *   如果打开流的采样率与ASR模型期望的16kHz不同，则在读取到音频数据后，会调用新增的 `resample_audio` 辅助函数（使用Numpy线性插值）将音频实时重采样到16kHz，然后再送入VAD和ASR识别流程。
    *   **日志与错误处理改进**: 增强了设备选择和音频流打开过程中的日志记录和错误提示，使其更清晰。

3.  **音频播放器配置加载修复 (`app/main.py`)**:
    *   修正了 `AudioNewPlayer` 初始化逻辑，确保其从 `config.yaml` 中正确加载并使用包括 `device`, `blocksize`, `buffer_size`, `min_prebuffer` 在内的完整 `AudioPlayerConfig`，而不仅仅是 `volume`。这解决了之前TTS播放可能因未使用正确输出设备配置而无声的问题。

通过这些修改，ASR模块现在能够更灵活地适应不同的麦克风设备，并能处理由于设备原生采样率与模型要求不一致所导致的问题，提升了系统的鲁棒性和易用性。同时，确保了音频播放器正确加载其设备配置。

---

# 2025-05-11
- **功能(视觉与机械臂辅助): 增强视觉信息获取能力并优化机械臂调用引导**
    - **视觉信息函数增强 (`shape_get_all_objects` @ [`utils/function_call/vision_arm_control.py`](asr-llm-tts/utils/function_call/vision_arm_control.py:1))**:
        - 新增返回物体相对于图像中心的**像素偏移距离 (`distance_x`, `distance_y`)**，以补充原有的绝对像素坐标 (`image_x`, `image_y`)、`id`、`class_name` 及 `confidence`。
        - 同步更新了函数内部的物体信息完整性校验逻辑，确保包含新增的距离字段。
        - (历史修复：已修正 `class_name` 提取的bug)。
    - **Function Call Schema 更新 (`@ [`utils/function_call/function_call_tools.py`](asr-llm-tts/utils/function_call/function_call_tools.py:1))**:
        - **`shape_get_all_objects` Schema 描述**:
            - 更新了其注册描述，准确加入了新增的 `distance_x` 和 `distance_y` 字段及其清晰含义，以便LLM理解和使用更全面的视觉输入。
        - **机械臂操作函数 (`robot_arm_catch_object`, `robot_arm_put_object`) Schema 描述**:
            - 为这两个函数的 `height_z` (高度) 参数描述补充了用户先前提供的默认值建议（抓取任务默认50mm，放置任务默认60mm），旨在引导LLM进行更合理的参数填充。
    - **目标**: 显著提升系统获取和理解视觉信息的能力，为LLM更精确地分析场景、定位物体并指导机械臂操作奠定坚实的基础，并尝试引导LLM使用更合适的参数（如偏移坐标和默认高度）。
    - **结果**：效果不是很好，llm无法理解将distance_x和distance_y填充到机械臂的函数当中，还需要修改。

---

# 2025-05-11
- **功能（机械臂控制）：实现机械臂控制的函数调用（抓取与放置）**
    - **模式定义**：在[`utils/function_call/function_schema.py`](asr-llm-tts/utils/function_call/function_schema.py:1)中为`robot_arm_catch_object`和`robot_arm_put_object`添加了JSON模式。
    - **逻辑实现**：在[`utils/function_call/vision_arm_control.py`](asr-llm-tts/utils/function_call/vision_arm_control.py:1)中实现了核心逻辑，以调用`IntelligentVisionArmControl` API（`/api/arm/catch_object`，`/api/arm/put_object`），包括参数处理、基于`httpx`的异步请求、超时/重试以及详细的错误/成功响应处理。
    - **工具注册**：在[`utils/function_call/function_call_tools.py`](asr-llm-tts/utils/function_call/function_call_tools.py:1)中注册了新的机械臂控制函数，以便大语言模型（LLM）能够访问。
- **杂项（大语言模型）：优化系统提示以改进机械臂控制错误报告**
    - 更新了[`config.yaml`](asr-llm-tts/config.yaml:1)中的`llm_config.system_prompt`，指示大语言模型在`robot_arm_catch_object`或`robot_arm_put_object`调用失败时，利用工具错误响应中的`message`字段，向用户提供更具体的反馈，并建议常见原因（例如，无法到达的坐标、计算问题）。（用户应用了最终的提示更改）。 

---

# 2025-05-11
- **重构形状检测相关模块为视觉机械臂控制**:
    - **模块重命名**:
        - 将 Python 模块文件 `asr-llm-tts/utils/function_call/shape_detection_control.py` 重命名为 `asr-llm-tts/utils/function_call/vision_arm_control.py`。
    - **导入语句更新**:
        - 更新了 `asr-llm-tts/utils/function_call/function_call_tools.py` 和 `asr-llm-tts/test/test_shape_detection.py` 中的导入语句，将 `shape_detection_control` 更改为 `vision_arm_control`。
    - **配置文件修改**:
        - 在 `asr-llm-tts/config.yaml` 文件中，将配置节名从 `shape_detection:` 修改为 `intelligent_vision_arm_control:`。
    - **代码适配**:
        - 修改了 `asr-llm-tts/utils/function_call/vision_arm_control.py`（原 `shape_detection_control.py`）中的代码，使其从新的配置节 `intelligent_vision_arm_control` 读取配置。
        - 确认 `asr-llm-tts/utils/config_loader.py` 无需修改，其为通用加载器。
    - **目标**: 统一命名，更好地反映模块功能，提高代码可读性和可维护性。

---
# 2025-05-10
- **增强LLM交互逻辑，支持多轮函数调用及优化总结行为**:
    - **`app/turn_manager.py`**:
        - 重构 `handle_turn` 函数以支持多轮函数调用。LLM现在可以在单次用户指令中连续进行多次工具调用，直到满足用户需求或达到最大调用次数。
        - 实现了更精细的对话历史管理 (`messages_history_for_this_turn`)，确保LLM在多轮调用中拥有正确的上下文。
        - 优化了错误处理和中断逻辑，以适应多轮调用的复杂性。
    - **`config.yaml` (`llm.system_prompt`)**:
        - **目标**: 改善LLM在执行包含多次工具调用的任务后，其最终自然语言回复的全面性。
        - **变更**: 在 `system_prompt` 的规则中新增了第6条规则：“6. 当你完成完成一件包含好几个步骤的事情（比如先唱歌再调音量）之后，需要将每一步都做得怎么样，清清楚楚地向主人汇报。”
        - **预期效果**: LLM的总结性回复将更清晰地概括所有执行步骤及其主要结果，而不仅仅是最后一步。
    - **状态**: 用户已确认 `config.yaml` 修改和 `turn_manager.py` 的多轮调用逻辑符合预期。

---

# 2025-05-09
- **新增yolo形状检测远程function-call文件：shape_detection_control.py**:
    - 适配了shape_detection_control的function-call功能
    - 更新 `config_loader.py` 以加载新的 `remote_yolo` 配置段。 
- 修改 `shape_detection_control.py` 以使用加载的配置，不再硬编码 URL 和超时。

---

# 2025-04-30
- **配置化远程YOLO控制并修复YAML解析错误**:
    - 将远程YOLO控制功能的配置（base_url, timeout）从 `remote_yolo_control.py` 移至 `config.yaml`，提高可配置性。
    - 更新 `config_loader.py` 以加载新的 `remote_yolo` 配置段。
- 修改 `remote_yolo_control.py` 以使用加载的配置，不再硬编码 URL 和超时。
- 将 `config.yaml` 中的 `system_prompt` 改为使用字面量块标量 (`|`) 格式，解决了因特殊字符和多行文本导致的 YAML 解析错误。
- 在 `system_prompt` 中添加规则，尝试引导 LLM 更准确地生成函数调用（特别是针对布尔参数）。
- 更新 `requirements.txt`，添加 `sherpa-onnx`, `soundfile`, `pygame` 依赖。

---

# 2025-04-29
- **调整requirements.txt、README.md**:
    - 调整requirements.txt，添加了httpx库
    - 调整README.md，添加了预训练模型介绍路径、ASR模型下载路径、VAD模型下载路径

---

# 2025-04-28
- **新增远程 Function Call 功能，用于控制 YOLO Demo 云台**:
    - **目标**: 让对话助手能够通过自然语言指令调用 `rdk_yolo_v8_demo` 项目提供的 API，实现对外部摄像头云台的远程控制。
    - **`utils/function_call/remote_yolo_control.py`**:
        - 新建此文件，用于封装与远程 YOLO Demo API 的交互逻辑。
        - 使用 `httpx` 异步库发送 HTTP GET/POST 请求到远程 API。
        - 实现 `_make_api_request` 辅助函数，统一处理请求发送、响应解析、超时及各种网络/HTTP 错误。
        - 添加了 `yolo_move_servo_relative`, `yolo_reset_servo`, `yolo_toggle_tracking`, `yolo_get_face_count` 等异步函数，分别对应远程 API 的相应端点。
        - **重要**: 将远程服务的 URL (`YOLO_DEMO_BASE_URL`) 从硬编码改为从 `config.yaml` 文件中的 `remote_control.yolo_demo_url` 字段读取，提高了配置灵活性。
    - **`utils/function_call/function_call_tools.py`**:
        - 导入 `remote_yolo_control.py` 中定义的异步函数。
        - 为每个远程控制功能创建了对应的包装函数（例如 `yolo_move_servo_relative_func`）。
        - 使用 `@FunctionRegistry.register_function` 注册这些包装函数，使其能够被 Function Call 机制发现和调用。
        - 为注册的函数添加了清晰的描述（例如“控制远程摄像头云台相对转动指定的角度”）。
    - **`utils/function_call/function_schema.py`**:
        - 新增 `get_remote_yolo_schemas()` 函数，为新注册的远程控制函数定义了符合 OpenAI Function Calling 规范的 JSON Schema，包括函数名、描述、参数类型和必需项。
        - 在 `get_all_schemas()` 函数中整合了这些新的 Schema，确保 LLM 能够理解和使用这些新的远程工具。
- **注意事项**:
    - **配置 URL**: 必须在 `asr-llm-tts/config.yaml` 文件的 `remote_control` 部分正确配置 `yolo_demo_url`，指向 `rdk_yolo_v8_demo` 服务实际运行的 IP 地址和端口。
    - **固定 IP 与端口**: 为了保证连接稳定，建议为运行 `rdk_yolo_v8_demo` 的设备配置静态 IP 地址。同时，确保其 Flask 服务使用的端口（默认为 5000，但可能自动选择其他端口如 5001）是固定的，或者在 `config.yaml` 中始终更新为正确的端口号。
    - **网络**: 确保运行 `asr-llm-tts` 和 `rdk_yolo_v8_demo` 的设备在同一局域网内，且网络通畅，没有防火墙阻止两者之间的 HTTP 通信。
    - **依赖**: `remote_yolo_control.py` 依赖 `httpx` 库，确保已将其添加到 `requirements.txt` 并安装。
- **结果**:
    - 对话助手现在具备了通过 Function Call 远程控制 YOLO Demo 云台的能力，可以响应如“摄像头向左转”、“打开人脸追踪”、“画面里有人吗”等指令。
- **后续有待优化的内容**:
    - 将远程控制功能的参数写入配置文件，便于管理。
    - 后续将板卡的IP都进行固定，方便配置

---

# 2025-04-28
- **新增项目README文档**:
    - 新增 README.md 文件，详细介绍了项目结构、功能、使用方法、配置说明等。

---

# 2025-04-28
- **新增 TTS 音量控制功能并通过 Function Call 调用**:
    - **`utils/function_call/function_call_tools.py`**:
        - 添加了 `tts_player_set_volume_func`, `tts_player_increase_volume_func`, `tts_player_decrease_volume_func` 函数，用于控制共享的 `AudioNewPlayer` 实例（主要负责 TTS 播放）的音量。
        - 使用 `@FunctionRegistry.register_function` 注册了这些新函数。
        - 修改了原有音乐音量控制函数的描述和返回消息，明确区分是“背景音乐”音量。
        - 为新函数添加了清晰的描述，明确是控制“语音助手（TTS）”音量。
    - **`utils/function_call/function_schema.py`**:
        - 新增 `get_tts_player_schemas()` 函数，为上述新函数生成对应的 JSON Schema。
        - 在 `get_all_schemas()` 中整合了这些新的 Schema。
    - **目标**: 允许用户通过自然语言指令分别控制背景音乐（Pygame）和语音助手（AudioNewPlayer）的音量。
- **结果**:
    - 成功添加了控制 TTS 音量的 Function Call 定义。
- **新发现的问题**:
    - 添加新功能后，调用 LLM API 时出现 `HTTP 400 Bad Request` 错误，错误码为 `InvalidParameter`。初步排查 `config.yaml` 无明显问题。
- **原因与修复**:
    - 经检查，错误源于 `utils/function_call/function_schema.py` 中为 `tts_player_set_volume` 函数生成的 Schema 里，`value` 参数的类型被错误地设置为 `"type": "any"`。
    - 火山引擎 LLM API 不支持 `"any"` 类型。将该参数类型修正为 `"type": "string"`（因为实际处理函数 `parse_volume_value` 可以解析字符串输入）后，`InvalidParameter` 错误解决。
- **最终结果**:
    - 控制 TTS 音量的 Function Call 功能可正常使用，LLM API 不再报错。用户现在可以通过明确的指令（如“把语音声音调大”）来控制 TTS 音量。

---

# 2025-04-27
- **修复了音频播放器音量调节问题**:
    - **`utils/audio_new_player.py`**:
        - **问题**: 先前的音量调节实现无法有效静音（音量设为0时仍有声音），并且在调整音量时通过清除部分缓冲 (`_partial_bytes_from_queue`) 来尝试快速生效，但这会导致音频内容跳过。
        - **修复**: 引入 `_volume_changed` 标志位。当 `set_volume` 检测到显著音量变化时，设置此标志，而不是清除缓冲。`_callback` 函数在每次执行时检查此标志，如果设置了，则使用新的音量值统一处理回调中的所有音频数据（包括剩余缓冲和新获取的数据块），然后重置标志。
        - **效果**: 确保新音量能快速、一致地应用于所有待播放数据，解决了静音无效和音频跳跃的问题，音量调节变得平滑且有效。
- **结果**:
    - 音量调节功能（包括静音）现在可以正常工作，且不会导致播放内容跳过。

---

# 2025-04-27
- **修复了之前asr输入的时候，播放卡顿的问题**:
    - **`asr/asr_service.py`**:
        - 新建了 `asr_service.py` ，将asr作为独立的一个进程与整体代码区分开，避免了由于引入asr导致线程变得过于复杂，cpu来不及处理导致的播放卡顿问题
- **结果**:
    - 解决了之前实验asr作为输入的时候，音频播放卡顿的问题。目前这版的效果是非常好的。
- **新发现的问题**:
    - 音频播放器的音量调节功能无法使用。
    - asr还存在一些误检的情况。
- **原因**：
    - 暂时还未找到

---

# 2025-04-22
- **添加shepa-ncnn作为llm输入的新方式**:
    - **`app/main.py`**:
        - 修改了 `main` 函数，使其能够根据命令行参数选择使用 Shepa-ncnn 或其他输入方式。
    - **`config.yaml`**:
        - 添加了 `shepa_ncnn` 配置项。
- **结果**:
    - asr测试正常，测试脚本位于`/test/test_asr_module.py`
- **新发现的问题**:
    - 在使用asr作为输入的时候，tts的音频播放非常的卡顿。
    - 日志中大量的 output underflow 警告是导致 TTS 播放卡顿的直接原因
- **原因**：
    - output underflow: 这个警告来自底层的 sounddevice 库，意味着当你使用 AudioNewPlayer 播放 TTS 音频流时，程序未能及时向声卡提供足够的音频数据。声卡缓冲区空了，播放自然就会断断续续。

---

# 2025-04-22
- **实现并发处理以达成真正的双向流式**:
    - **`app/turn_manager.py`**:
        - 修改了 `handle_turn` 函数的核心逻辑。
        - 引入了 `playback_task_started` 标志位。
        - 将 `stream_tts_and_play` 任务的启动时机提前到 `async for chunk in llm.stream_chat(...)` 循环内部，当收到第一个有效 `content` 块时立即并发启动。
        - 调整了 Function Call 处理逻辑，确保在执行工具前取消初始的并发播放任务，并在生成总结后为总结内容启动新的并发播放任务。
        - 优化了中断和错误处理逻辑，确保能正确取消并发启动的播放任务。
    - **目标**: 实现官方 TTS API 文档描述的"边发边收"模式，让 LLM 输出、TTS 合成、音频播放能够真正并行进行，解决之前 LLM 输出完成后 TTS 才开始的问题，并缓解 `text_queue` 溢出。
- **结果**:
    - 成功实现了 LLM 处理和 TTS/播放的并发执行。
    - "Text queue full" 问题得到解决，在本轮测试中未再出现。
    - Function Call 和中断流程在并发模型下工作正常。
- **新发现的问题**:
    - 在处理较长的音频流（如"介绍一下北京"的回复）播放到后期时，TTS 客户端出现 WebSocket 连接异常 (`tts.protocol - ERROR - 接收响应失败: 'NoneType' object has no attribute 'resume_reading'`)。初步判断可能与服务器端长连接超时或客户端在连接结束/异常时的状态管理有关，需要进一步检查 `tts/client.py` 和 `tts/protocol.py` 的相关逻辑。

---

# 2025-04-20
- **移除历史对话管理**:
    - **`app/turn_manager.py`**:
        - 移除了 `handle_turn` 函数的 `shared_history` 参数。
        - 修改了发送给 LLM 的消息构建逻辑 (`current_turn_messages` 和 `messages_for_summary`)，使其不再包含历史对话，每次交互都是独立的。
        - 删除了所有向 `shared_history` 列表追加用户输入和助手回复的代码。
        - 删除了修剪 `shared_history` 列表长度的代码。
    - **`app/main.py`**:
        - 在调用 `turn_manager.handle_turn` 创建任务时，移除了传递 `shared_history` 的参数。
    - **`app/shared_state.py`**:
        - 注释掉了不再使用的全局 `shared_history` 列表定义。
- **目标**: 实现无状态的对话交互，每次用户输入都视为一个新的、独立的对话开始，不保留之前的上下文信息。

---

# 2025-04-20
- **代码重构**:
    - 将原有的 `test/test_llm_tts.py` 脚本重构为模块化的应用程序结构，拆分到 `app/` 目录下，包含 `main.py`, `input_handler.py`, `shared_state.py`, `turn_manager.py`, `tts_player.py` 等模块。
    - 目标是提高代码的可维护性和可扩展性，将不同的功能逻辑分离到独立的模块中。
- **功能迁移与修复**:
    - 将 `test_llm_tts.py` 中的核心逻辑（如初始化、事件循环、输入处理、任务管理、TTS播放、清理等）迁移到新的 `app/` 模块中。
    - 修复了模块拆分后出现的各种问题，包括：
        - 恢复了被注释掉的核心功能代码。
        - 修正了模块间的依赖关系和函数调用方式。
        - 解决了导入路径错误 (`TTSConfig` 的导入问题)。
        - 修复了参数传递错误（`tts_config` 未正确传递给 `handle_turn`）。
        - 修正了 `main.py` 中的语法错误（调用 `handle_turn` 时缺少逗号）。
- **目标**: 确保重构后的应用程序 (`app/main.py`) 能够成功运行，并且功能（特别是 TTS 合成与播放）与原始的 `test_llm_tts.py` 脚本保持一致。

---

# 2025-04-20
- 修改了很多内容，已经不好溯源了。不过目前这版是较为完成的一版，实现了llm、tts、音频播放器、function-call功能
    - llm：实现了流式输出、错误码定位
    - tts：实现了流式音频输出、块状音频输出、双向流式输出
    - 音频播放器：实现了多种音频格式的支持、流式播放支持、块状播放支持
    - function call：实现了计算器、音乐播放器等功能
- 添加了md文档，介绍各个模块的功能
- 还存在的问题：
    - 明确暴露的 Text queue full 和 output underflow 问题，核心是 LLM 生成文本的速度超过了 TTS 合成及音频播放的速度，导致中间的 text_queue 成为瓶颈。
    
---    

# 2025-04-18
- 将音乐播放器的参数放入config.yaml中，便于管理。
    - 播放器的音量参数是可以被代码覆盖的，所以需要优先使用代码配置，其次才是config.yaml中的配置。

---

# 2025-04-17
- 修复audio_player.py中，音量调整会导致音频播放卡顿的问题。
    - 主要就是需要加快推送速率，否则会导致音频播放卡顿，推送速度要大于声卡消耗速度，否则会导致音频播放卡顿。
- 修复tts模块中，流式播放时出现播放不完整的问题。
- 编写了audio_player和tts模块的文档
- 新增test_audio_player.py和test_stream_tts.py，用于测试音频播放和TTS流式合成。

---

# 2025-04-16
- 新增 LLM 大语言模型模块
    - 新增 llm/client.py，实现 LLMClient，支持与火山引擎大模型 API 的流式/非流式对话，支持自定义推理参数、模型选择等。
    - 支持异步流式输出，便于语音助手等实时场景集成。
    - 配置项集中管理，兼容多模型和多参数灵活切换。
- 新增 function-call 工具注册与分发机制
    - 新增 utils/function_call/function_call_tools.py，实现 FunctionRegistry，支持本地工具函数的自动注册、查找、异步调用。
    - 支持注册计算器、音乐播放器等多种本地工具，便于 LLM function-call 场景下自动调用。
    - 工具注册与底层实现解耦，结构清晰，易于扩展。
- 新增 function-call 工具实现
    - 新增 utils/function_call/function_tools_impl.py，实现 Calculator、MusicPlayer 等底层功能类。
    - MusicPlayer 支持自动解码 mp3、wav、flac 等格式为 PCM，兼容多种音频文件，提升用户体验。
    - 统一音乐文件目录，支持动态扫描和播放。
- 新增 LLM function-call 测试工具
    - 新增 test/test_llm.py，支持多轮对话、流式输出、自动 function-call 工具调用与结果总结。
    - 支持自动构建 tools schema，便于 LLM 识别和调用本地工具。
    - 测试工具支持交互式输入，便于开发调试和功能验证。

---

# 2025-04-15

- 第一次版本提交
    - 实现了音频播放模块和tts模块
    
        - 音频播放模块支持应用层混音（多音频PCM流合成播放），并集成混音测试用例
        - AudioPlayer 新增 mix_pcm_audio 静态方法，支持不同长度音频的混合
        - 优化并发播放测试，完善异常处理和日志
        - 规范了 AudioPlayer 默认设备参数，提升兼容性
        -  **AudioPlayer 假定输入是原始 PCM int16 数据，并假定采样率/通道数一致，无法直接播放压缩音频**
        - 代码结构优化，注释与日志更清晰
        - tts模块、协议常量、协议处理等无实质性变更，仅做兼容性核查

    - 编写了音频模块、tts模块的测试脚本

        - test_audio_player.py：覆盖音频播放的直接播放、AudioPlayer播放、并发播放、应用层混音等多种场景，自动检测采样率/通道数一致性，日志详细，便于定位问题。
        - test_stream_tts.py：覆盖TTS流式合成、连续合成、音色切换、长文本、实时分块、异常处理、压力测试、流式播放打断等多种TTS场景，支持边合成边播放和保存，适合端到端功能验证。
