"use client"

// 扩展Window接口以支持去重机制
declare global {
  interface Window {
    processedLLMResponses?: Set<string>
  }
}

import React, { createContext, useContext, useReducer, useCallback, useEffect, useRef, type ReactNode } from 'react'
import { 
  AppState, 
  AppAction, 
  WebSocketMessage,
  Message,
  ToolCall,
  initialAppState, 
  appStateReducer,
  InputMode,
  SystemStatus,
  ErrorInfo
} from '@/lib/app-state-manager'

// ============================================================================
// Context定义
// ============================================================================

interface AppStateContextType {
  // 状态
  state: AppState
  
  // 基础操作
  dispatch: React.Dispatch<AppAction>
  
  // 连接管理
  connectWebSocket: (url?: string) => void
  disconnectWebSocket: () => void
  sendMessage: (message: WebSocketMessage) => boolean
  
  // 消息管理
  addMessage: (message: Omit<Message, 'id' | 'timestamp'>) => string
  updateMessage: (id: string, updates: Partial<Message>) => void
  clearMessages: () => void
  
  // 输入管理
  setInput: (text: string) => void
  clearInput: () => void
  sendUserInput: (text?: string) => void
  
  // UI状态管理
  setInputMode: (mode: InputMode) => void
  setListening: (isListening: boolean) => void
  setAsrResult: (result: string) => void
  
  // 系统状态管理
  updateSystemStatus: (status: SystemStatus) => void
  addError: (error: Omit<ErrorInfo, 'id' | 'timestamp'>) => void
  clearError: (id: string) => void
  clearAllErrors: () => void
  
  // 流式响应管理
  startStream: (turnId?: string) => void
  addStreamChunk: (content: string, isFirst?: boolean) => void
  endStream: (finalContent?: string) => void
  
  // 工具调用管理
  startToolCall: (toolCall: ToolCall) => void
  updateToolCall: (id: string, updates: Partial<ToolCall>) => void
  endToolCall: (id: string, result: string) => void
  errorToolCall: (id: string, error: string) => void
  
  // 轮次管理
  startTurn: (turnId?: string) => string
  endTurn: () => void
  interruptTurn: () => void
  
  // 助手状态管理
  setAssistantTyping: (isTyping: boolean) => void
}

const AppStateContext = createContext<AppStateContextType | null>(null)

// ============================================================================
// Provider组件
// ============================================================================

interface AppStateProviderProps {
  children: ReactNode
  websocketUrl?: string
  autoConnect?: boolean
}

export function AppStateProvider({ 
  children, 
  websocketUrl,
  autoConnect = true 
}: AppStateProviderProps) {
  const [state, dispatch] = useReducer(appStateReducer, initialAppState)
  
  // WebSocket引用
  const socketRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const stateRef = useRef(state)
  const maxReconnectAttempts = 5
  const baseReconnectDelay = 1000

  // 保持stateRef同步
  useEffect(() => {
    stateRef.current = state
  }, [state])

  // ============================================================================
  // WebSocket连接管理
  // ============================================================================

  const getWebSocketUrl = useCallback(() => {
    // 1. 优先使用传入的URL
    if (websocketUrl) return websocketUrl
    
    // 2. 使用环境变量
    const envWsUrl = process.env.NEXT_PUBLIC_WEBSOCKET_URL
    if (envWsUrl) return envWsUrl
    
    // 3. 动态构建URL
    if (typeof window !== "undefined") {
      const protocol = window.location.protocol === "https:" ? "wss:" : "ws:"
      return `${protocol}//${window.location.hostname}:8000/ws/chat`
    }
    
    return "ws://localhost:8000/ws/chat"
  }, [websocketUrl])

  const connectWebSocket = useCallback((url?: string) => {
    const wsUrl = url || getWebSocketUrl()
    
    // 如果已经连接，先断开
    if (socketRef.current) {
      socketRef.current.close()
    }
    
    dispatch({ type: 'CONNECTION_CONNECTING', payload: { url: wsUrl } })
    
    try {
      const newSocket = new WebSocket(wsUrl)
      socketRef.current = newSocket
      
      newSocket.onopen = () => {
        dispatch({ type: 'CONNECTION_CONNECTED', payload: { socket: newSocket } })
        dispatch({ type: 'CONNECTION_RESET_ATTEMPTS' })
      }
      
      newSocket.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data)
          dispatch({ type: 'SYSTEM_MESSAGE_RECEIVED', payload: { message } })
          handleWebSocketMessage(message)
        } catch (error) {
          console.error("Failed to parse WebSocket message:", error)
          dispatch({
            type: 'SYSTEM_ERROR_ADD',
            payload: {
              message: "解析WebSocket消息失败",
              source: "websocket",
              severity: 'error'
            }
          })
        }
      }
      
      newSocket.onerror = (error) => {
        console.error("WebSocket error:", error)
        dispatch({
          type: 'CONNECTION_ERROR',
          payload: { error: "WebSocket连接错误" }
        })
      }
      
      newSocket.onclose = (event) => {
        socketRef.current = null
        
        if (event.code !== 1000) {
          // 非正常关闭，尝试重连
          scheduleReconnect()
        } else {
          dispatch({ type: 'CONNECTION_DISCONNECTED', payload: {} })
        }
      }
      
    } catch (error) {
      console.error("Failed to create WebSocket connection:", error)
      dispatch({
        type: 'CONNECTION_ERROR',
        payload: { error: "创建WebSocket连接失败" }
      })
    }
  }, [getWebSocketUrl])

  const disconnectWebSocket = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }
    
    if (socketRef.current) {
      socketRef.current.close(1000, "User disconnected")
      socketRef.current = null
    }
    
    dispatch({ type: 'CONNECTION_DISCONNECTED', payload: {} })
  }, [])

  const scheduleReconnect = useCallback(() => {
    if (state.connection.reconnectAttempts >= maxReconnectAttempts) {
      dispatch({
        type: 'CONNECTION_ERROR',
        payload: { error: "已达到最大重连次数" }
      })
      return
    }
    
    const attempts = state.connection.reconnectAttempts + 1
    const delay = baseReconnectDelay * Math.pow(2, attempts - 1)
    
    dispatch({ type: 'CONNECTION_RECONNECTING', payload: { attempts } })
    
    reconnectTimeoutRef.current = setTimeout(() => {
      connectWebSocket()
    }, delay)
  }, [state.connection.reconnectAttempts, connectWebSocket])

  const sendMessage = useCallback((message: WebSocketMessage): boolean => {
    if (!socketRef.current || state.connection.state !== 'connected') {
      dispatch({ 
        type: 'SYSTEM_ERROR_ADD', 
        payload: {
          message: "WebSocket未连接",
          source: "websocket",
          severity: 'warning'
        }
      })
      return false
    }
    
    try {
      socketRef.current.send(JSON.stringify(message))
      return true
    } catch (error) {
      console.error("Failed to send WebSocket message:", error)
      dispatch({ 
        type: 'SYSTEM_ERROR_ADD', 
        payload: {
          message: "发送消息失败",
          source: "websocket",
          severity: 'error'
        }
      })
      return false
    }
  }, [state.connection.state])

  // ============================================================================
  // WebSocket消息处理
  // ============================================================================

  const handleWebSocketMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'SYSTEM_STATUS':
        dispatch({ type: 'SYSTEM_STATUS_UPDATE', payload: message.payload })

        // 特殊处理ASR状态
        if (message.payload.module === 'asr_service') {
          dispatch({
            type: 'UI_SET_LISTENING',
            payload: { isListening: message.payload.status === 'listening' }
          })
        }
        break

      case 'LLM_CHUNK':
        const { content_delta, is_final } = message.payload

        // 使用最新的state
        const currentState = stateRef.current

        // 确保有当前轮次的助手消息
        let currentAssistantMessageId = currentState.conversation.currentTurnId

        // 如果没有当前轮次或者当前轮次不是助手消息，创建新的助手消息
        if (!currentAssistantMessageId) {
          currentAssistantMessageId = Date.now().toString() + '_assistant'
          dispatch({ type: 'TURN_START', payload: { turnId: currentAssistantMessageId } })

          // 创建助手消息，保留可能已存在的工具调用
          const existingToolCalls = currentState.tools.activeCalls ? Object.values(currentState.tools.activeCalls) : []
          const assistantMessage: Message = {
            id: currentAssistantMessageId,
            role: 'assistant',
            content: content_delta || '',
            timestamp: Date.now(),
            toolCalls: existingToolCalls
          }
          dispatch({ type: 'MESSAGE_ADD', payload: { message: assistantMessage } })
        } else {
          // 🛡️ 保护机制：智能更新现有助手消息的内容
          const currentMessage = currentState.conversation.messages.find(m => m.id === currentAssistantMessageId)
          if (currentMessage) {
            const existingContent = currentMessage.content || ''
            const contentDelta = content_delta || ''

            // 🔒 静默模式：在有工具调用的turn中，LLM_CHUNK不更新内容，等待LLM_FINAL_RESPONSE
            const hasToolCallsInTurn = currentState.conversation.hasToolCallsInCurrentTurn

            if (hasToolCallsInTurn) {
              return // 静默模式：直接返回，不更新消息内容
            }

            // 正常的流式追加
            const newContent = existingContent + contentDelta

            dispatch({
              type: 'MESSAGE_UPDATE',
              payload: {
                id: currentAssistantMessageId,
                updates: { content: newContent }
              }
            })
          }
        }

        // 更新流式状态
        if (!currentState.conversation.isReceivingStream) {
          dispatch({ type: 'STREAM_START', payload: { turnId: currentAssistantMessageId } })
        }

        dispatch({
          type: 'STREAM_CHUNK',
          payload: {
            content: content_delta || '',
            isFirst: !currentState.conversation.isReceivingStream
          }
        })

        if (is_final) {
          dispatch({ type: 'STREAM_END', payload: {} })
        }
        break

      // 思维链处理
      case 'REASONING_START':
        const currentState2 = stateRef.current
        let currentAssistantMessageId2 = currentState2.conversation.currentTurnId

        // 如果没有当前轮次，创建新的助手消息
        if (!currentAssistantMessageId2) {
          currentAssistantMessageId2 = Date.now().toString() + '_assistant'
          dispatch({ type: 'TURN_START', payload: { turnId: currentAssistantMessageId2 } })

          const assistantMessage: Message = {
            id: currentAssistantMessageId2,
            role: 'assistant',
            content: '',
            timestamp: Date.now(),
            reasoningContent: '',
            isReasoningStreaming: true
          }
          dispatch({ type: 'MESSAGE_ADD', payload: { message: assistantMessage } })
        }

        dispatch({
          type: 'REASONING_START',
          payload: { messageId: currentAssistantMessageId2 }
        })
        break

      case 'REASONING_CHUNK':
        const currentState3 = stateRef.current
        const currentAssistantMessageId3 = currentState3.conversation.currentTurnId

        if (currentAssistantMessageId3) {
          dispatch({
            type: 'REASONING_CHUNK',
            payload: {
              messageId: currentAssistantMessageId3,
              content: message.payload.reasoning_delta || ''
            }
          })
        }
        break

      case 'REASONING_END':
        const currentState4 = stateRef.current
        const currentAssistantMessageId4 = currentState4.conversation.currentTurnId

        if (currentAssistantMessageId4) {
          dispatch({
            type: 'REASONING_END',
            payload: { messageId: currentAssistantMessageId4 }
          })
        }
        break

      case 'LLM_FINAL_RESPONSE':
        // 🛡️ 事件去重：防止重复处理相同的LLM_FINAL_RESPONSE
        const responseContent = message.payload.content || ''
        const currentTurnId = stateRef.current.conversation.currentTurnId
        const responseKey = `${currentTurnId}_${responseContent.length}_${responseContent.slice(-20)}`

        // 使用更精确的去重机制
        if (!window.processedLLMResponses) {
          window.processedLLMResponses = new Set()
        }

        if (window.processedLLMResponses.has(responseKey)) {
          // 跳过重复的LLM_FINAL_RESPONSE事件
          break
        }

        window.processedLLMResponses.add(responseKey)


        // 使用最新的state
        const finalCurrentState = stateRef.current

        // 确保有当前轮次的助手消息
        let finalAssistantMessageId = finalCurrentState.conversation.currentTurnId

        if (!finalAssistantMessageId) {
          // 如果没有当前轮次，创建新的助手消息（这种情况很少见）
          finalAssistantMessageId = Date.now().toString() + '_final_assistant'
          dispatch({ type: 'TURN_START', payload: { turnId: finalAssistantMessageId } })

          // 创建助手消息，包含可能的工具调用
          const existingToolCalls = finalCurrentState.tools.activeCalls ? Object.values(finalCurrentState.tools.activeCalls) : []
          const assistantMessage: Message = {
            id: finalAssistantMessageId,
            role: 'assistant',
            content: message.payload.content || '',
            timestamp: Date.now(),
            toolCalls: existingToolCalls
          }



          dispatch({ type: 'MESSAGE_ADD', payload: { message: assistantMessage } })
        } else {
          // 🛡️ 智能保护机制：防止重复累积
          const currentMessage = finalCurrentState.conversation.messages.find(m => m.id === finalAssistantMessageId)
          const existingContent = currentMessage?.content || ''
          const newContent = message.payload.content || ''
          const hasToolCallsInTurn = finalCurrentState.conversation.hasToolCallsInCurrentTurn

          let finalContent = newContent

          // 🔒 智能保护：如果当前turn有工具调用，智能累积
          if (hasToolCallsInTurn && existingContent && newContent && newContent.trim() !== '') {
            // 检查新内容是否已经在现有内容中（防止重复累积）
            if (existingContent.includes(newContent)) {
              // 新内容已存在，保持现有内容不变
              finalContent = existingContent
            } else {
              // 新内容不存在，进行累积
              finalContent = `${existingContent}\n\n${newContent}`
            }
          } else if (hasToolCallsInTurn && existingContent && (!newContent || newContent.trim() === '')) {
            // 如果新内容为空，保持现有内容
            finalContent = existingContent
          }



          dispatch({
            type: 'MESSAGE_UPDATE',
            payload: {
              id: finalAssistantMessageId,
              updates: { content: finalContent }
            }
          })
        }

        // 结束流式响应
        dispatch({
          type: 'STREAM_END',
          payload: { finalContent: message.payload.content }
        })
        break

      case 'TTS_STATUS':
        dispatch({
          type: 'SYSTEM_STATUS_UPDATE',
          payload: {
            module: 'tts_player',
            status: message.payload.status
          }
        })
        break

      case 'ASR_FINAL_RESULT':
        // 只有在语音模式下才处理ASR结果，避免在键盘模式下干扰用户输入
        const asrCurrentState = stateRef.current
        if (asrCurrentState.ui.inputMode === 'voice') {
          dispatch({
            type: 'UI_SET_ASR_RESULT',
            payload: { result: message.payload.text }
          })
        }
        // 键盘模式下忽略ASR结果
        break

      case 'TOOL_CALL_START':
        let parsedArgs = {}
        try {
          parsedArgs = message.payload.args_str ? JSON.parse(message.payload.args_str) : {}
        } catch (e) {
          console.error("Failed to parse tool call args:", message.payload.args_str, e)
          parsedArgs = { error: "解析参数失败" }
        }

        const toolCall: ToolCall = {
          name: message.payload.name,
          id: message.payload.id,
          args: parsedArgs,
          status: 'pending',
        }

        dispatch({ type: 'TOOL_CALL_START', payload: { toolCall } })
        dispatch({ type: 'ASSISTANT_TYPING_SET', payload: { isTyping: true } })

        // 使用最新的state
        const toolCurrentState = stateRef.current

        // 确保有当前轮次的助手消息来显示工具调用
        let toolAssistantMessageId = toolCurrentState.conversation.currentTurnId

        if (!toolAssistantMessageId) {
          // 创建新的助手消息轮次
          toolAssistantMessageId = Date.now().toString() + '_assistant_with_tools'
          dispatch({ type: 'TURN_START', payload: { turnId: toolAssistantMessageId } })

          const assistantMessage: Message = {
            id: toolAssistantMessageId,
            role: 'assistant',
            content: '', // 内容将在后续的LLM响应中填充
            timestamp: Date.now(),
            toolCalls: [toolCall]
          }
          dispatch({ type: 'MESSAGE_ADD', payload: { message: assistantMessage } })
        } else {
          // 更新现有助手消息的工具调用
          const currentMessage = toolCurrentState.conversation.messages.find(m => m.id === toolAssistantMessageId)
          const existingToolCalls = currentMessage?.toolCalls || []
          dispatch({
            type: 'MESSAGE_UPDATE',
            payload: {
              id: toolAssistantMessageId,
              updates: {
                toolCalls: [...existingToolCalls, toolCall]
              }
            }
          })
        }
        break

      case 'TOOL_CALL_RESULT':
        // 根据status字段处理成功或失败
        if (message.payload.status === 'error') {
          // 处理工具调用失败
          dispatch({
            type: 'TOOL_CALL_ERROR',
            payload: {
              id: message.payload.id,
              error: message.payload.error || '工具调用失败'
            }
          })

          // 使用最新的state
          const errorCurrentState = stateRef.current

          // 更新当前助手消息中的工具调用错误
          if (errorCurrentState.conversation.currentTurnId) {
            const currentMessage = errorCurrentState.conversation.messages.find(m => m.id === errorCurrentState.conversation.currentTurnId)
            if (currentMessage?.toolCalls) {
              const updatedToolCalls = currentMessage.toolCalls.map(tool =>
                tool.id === message.payload.id ? {
                  ...tool,
                  error: message.payload.error || '工具调用失败',
                  status: 'error' as const
                } : tool
              )
              dispatch({
                type: 'MESSAGE_UPDATE',
                payload: {
                  id: errorCurrentState.conversation.currentTurnId,
                  updates: { toolCalls: updatedToolCalls }
                }
              })
            }
          }
        } else {
          // 处理工具调用成功
          dispatch({
            type: 'TOOL_CALL_END',
            payload: {
              id: message.payload.id,
              result: message.payload.result || '执行成功'
            }
          })

          // 使用最新的state
          const resultCurrentState = stateRef.current

          // 更新当前助手消息中的工具调用结果
          if (resultCurrentState.conversation.currentTurnId) {
            const currentMessage = resultCurrentState.conversation.messages.find(m => m.id === resultCurrentState.conversation.currentTurnId)
            if (currentMessage?.toolCalls) {
              const updatedToolCalls = currentMessage.toolCalls.map(tool =>
                tool.id === message.payload.id ? {
                  ...tool,
                  result: message.payload.result || '执行成功',
                  status: 'success' as const
                } : tool
              )
              dispatch({
                type: 'MESSAGE_UPDATE',
                payload: {
                  id: resultCurrentState.conversation.currentTurnId,
                  updates: { toolCalls: updatedToolCalls }
                }
              })
            }
          }
        }
        break

      case 'TURN_ENDED':
        dispatch({ type: 'TURN_END' })
        break

      case 'TURN_INTERRUPTED':
        dispatch({ type: 'TURN_INTERRUPT' })
        break

      case 'ERROR_MESSAGE':
        dispatch({
          type: 'SYSTEM_ERROR_ADD',
          payload: {
            message: message.payload.message,
            source: message.payload.source || 'server',
            severity: 'error'
          }
        })
        break

      default:
        // 忽略未处理的消息类型
        break
    }
  }, [])

  // ============================================================================
  // 自动连接
  // ============================================================================

  useEffect(() => {
    if (autoConnect && state.connection.state === 'disconnected') {
      connectWebSocket()
    }
    
    return () => {
      disconnectWebSocket()
    }
  }, [autoConnect, connectWebSocket, disconnectWebSocket])

  // ============================================================================
  // 消息管理函数
  // ============================================================================

  const addMessage = useCallback((message: Omit<Message, 'id' | 'timestamp'>): string => {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9)
    const fullMessage: Message = {
      ...message,
      id,
      timestamp: Date.now(),
    }
    dispatch({ type: 'MESSAGE_ADD', payload: { message: fullMessage } })
    return id
  }, [])

  const updateMessage = useCallback((id: string, updates: Partial<Message>) => {
    dispatch({ type: 'MESSAGE_UPDATE', payload: { id, updates } })
  }, [])

  const clearMessages = useCallback(() => {
    dispatch({ type: 'MESSAGE_CLEAR_ALL' })
  }, [])

  // ============================================================================
  // 输入管理函数
  // ============================================================================

  const setInput = useCallback((text: string) => {
    dispatch({ type: 'INPUT_SET', payload: { text } })
  }, [])

  const clearInput = useCallback(() => {
    dispatch({ type: 'INPUT_CLEAR' })
  }, [])

  const sendUserInput = useCallback((text?: string) => {
    const inputText = text || state.conversation.currentInput
    if (!inputText.trim()) return

    // 添加用户消息
    const messageId = Date.now().toString() + Math.random().toString(36).substring(2, 11)
    const userMessage: Message = {
      id: messageId,
      role: 'user',
      content: inputText,
      timestamp: Date.now(),
    }
    dispatch({ type: 'MESSAGE_ADD', payload: { message: userMessage } })

    // 发送到服务器
    const success = sendMessage({
      type: 'USER_TEXT_INPUT',
      payload: { text: inputText },
    })

    if (success) {
      dispatch({ type: 'INPUT_CLEAR' })
      // 清除当前轮次，等待助手响应时创建新轮次
      dispatch({ type: 'TURN_END' })
    }
  }, [state.conversation.currentInput, sendMessage])

  // ============================================================================
  // UI状态管理函数
  // ============================================================================

  const setInputMode = useCallback((mode: InputMode) => {
    dispatch({ type: 'UI_SET_INPUT_MODE', payload: { mode } })

    // 切换到键盘模式时清除ASR结果，避免干扰用户输入
    if (mode === 'keyboard') {
      dispatch({ type: 'UI_SET_ASR_RESULT', payload: { result: '' } })
    }
  }, [])

  const setListening = useCallback((isListening: boolean) => {
    dispatch({ type: 'UI_SET_LISTENING', payload: { isListening } })

    // 发送ASR监听切换消息
    sendMessage({
      type: 'TOGGLE_ASR_LISTENING',
      payload: { enabled: isListening },
    })
  }, [sendMessage])

  const setAsrResult = useCallback((result: string) => {
    dispatch({ type: 'UI_SET_ASR_RESULT', payload: { result } })
  }, [])

  // ============================================================================
  // 系统状态管理函数
  // ============================================================================

  const updateSystemStatus = useCallback((status: SystemStatus) => {
    dispatch({ type: 'SYSTEM_STATUS_UPDATE', payload: status })
  }, [])

  const addError = useCallback((error: Omit<ErrorInfo, 'id' | 'timestamp'>) => {
    dispatch({ type: 'SYSTEM_ERROR_ADD', payload: error })
  }, [])

  const clearError = useCallback((id: string) => {
    dispatch({ type: 'SYSTEM_ERROR_CLEAR', payload: { id } })
  }, [])

  const clearAllErrors = useCallback(() => {
    dispatch({ type: 'SYSTEM_ERROR_CLEAR_ALL' })
  }, [])

  // ============================================================================
  // 流式响应管理函数
  // ============================================================================

  const startStream = useCallback((turnId?: string) => {
    const id = turnId || Date.now().toString()
    dispatch({ type: 'STREAM_START', payload: { turnId: id } })
    return id
  }, [])

  const addStreamChunk = useCallback((content: string, isFirst?: boolean) => {
    dispatch({ type: 'STREAM_CHUNK', payload: { content, isFirst } })
  }, [])

  const endStream = useCallback((finalContent?: string) => {
    dispatch({ type: 'STREAM_END', payload: { finalContent } })
  }, [])

  // ============================================================================
  // 工具调用管理函数
  // ============================================================================

  const startToolCall = useCallback((toolCall: ToolCall) => {
    dispatch({ type: 'TOOL_CALL_START', payload: { toolCall } })
  }, [])

  const updateToolCall = useCallback((id: string, updates: Partial<ToolCall>) => {
    dispatch({ type: 'TOOL_CALL_UPDATE', payload: { id, updates } })
  }, [])

  const endToolCall = useCallback((id: string, result: string) => {
    dispatch({ type: 'TOOL_CALL_END', payload: { id, result } })
  }, [])

  const errorToolCall = useCallback((id: string, error: string) => {
    dispatch({ type: 'TOOL_CALL_ERROR', payload: { id, error } })
  }, [])

  // ============================================================================
  // 轮次管理函数
  // ============================================================================

  const startTurn = useCallback((turnId?: string): string => {
    const id = turnId || Date.now().toString()
    dispatch({ type: 'TURN_START', payload: { turnId: id } })
    return id
  }, [])

  const endTurn = useCallback(() => {
    dispatch({ type: 'TURN_END' })
  }, [])

  const interruptTurn = useCallback(() => {
    dispatch({ type: 'TURN_INTERRUPT' })

    // 发送中断消息到服务器
    sendMessage({
      type: 'INTERRUPT_TURN',
      payload: {},
    })
  }, [sendMessage])

  // ============================================================================
  // 助手状态管理函数
  // ============================================================================

  const setAssistantTyping = useCallback((isTyping: boolean) => {
    dispatch({ type: 'ASSISTANT_TYPING_SET', payload: { isTyping } })
  }, [])

  // ============================================================================
  // Context值
  // ============================================================================

  const contextValue: AppStateContextType = {
    // 状态
    state,

    // 基础操作
    dispatch,

    // 连接管理
    connectWebSocket,
    disconnectWebSocket,
    sendMessage,

    // 消息管理
    addMessage,
    updateMessage,
    clearMessages,

    // 输入管理
    setInput,
    clearInput,
    sendUserInput,

    // UI状态管理
    setInputMode,
    setListening,
    setAsrResult,

    // 系统状态管理
    updateSystemStatus,
    addError,
    clearError,
    clearAllErrors,

    // 流式响应管理
    startStream,
    addStreamChunk,
    endStream,

    // 工具调用管理
    startToolCall,
    updateToolCall,
    endToolCall,
    errorToolCall,

    // 轮次管理
    startTurn,
    endTurn,
    interruptTurn,

    // 助手状态管理
    setAssistantTyping,
  }

  // ============================================================================
  // 清理函数
  // ============================================================================

  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
      if (socketRef.current) {
        socketRef.current.close()
      }
    }
  }, [])

  return (
    <AppStateContext.Provider value={contextValue}>
      {children}
    </AppStateContext.Provider>
  )
}

// ============================================================================
// Hook
// ============================================================================

export function useAppState(): AppStateContextType {
  const context = useContext(AppStateContext)
  if (!context) {
    throw new Error('useAppState必须在AppStateProvider内部使用')
  }
  return context
}
