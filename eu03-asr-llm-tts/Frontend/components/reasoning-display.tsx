'use client'

import React, { useState, useEffect, useRef } from 'react'
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline'

interface ReasoningDisplayProps {
  reasoningContent: string
  isStreaming: boolean
  className?: string
}

export default function ReasoningDisplay({
  reasoningContent,
  isStreaming,
  className = ''
}: ReasoningDisplayProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [displayContent, setDisplayContent] = useState('')
  const contentRef = useRef<HTMLDivElement>(null)
  const previewRef = useRef<HTMLDivElement>(null)

  // 流式更新显示内容
  useEffect(() => {
    setDisplayContent(reasoningContent)

    // 如果正在流式输出，自动滚动到最新内容
    if (isStreaming) {
      setTimeout(() => {
        if (isExpanded && contentRef.current) {
          contentRef.current.scrollTop = contentRef.current.scrollHeight
        } else if (!isExpanded && previewRef.current) {
          previewRef.current.scrollLeft = previewRef.current.scrollWidth
        }
      }, 10)
    }
  }, [reasoningContent, isStreaming, isExpanded])

  // 如果没有思维链内容，不显示组件
  if (!reasoningContent.trim()) {
    return null
  }

  // 预览内容逻辑：显示最新的内容而不是开头
  const getPreviewContent = () => {
    const maxPreviewLength = 100 // 增加预览长度到100字符

    if (displayContent.length <= maxPreviewLength) {
      return displayContent
    }

    // 如果正在流式输出，显示最后的内容
    if (isStreaming) {
      return '...' + displayContent.substring(displayContent.length - maxPreviewLength)
    }

    // 如果已完成，显示开头和结尾
    const startPart = displayContent.substring(0, 40)
    const endPart = displayContent.substring(displayContent.length - 40)
    return startPart + ' ... ' + endPart
  }

  const previewContent = getPreviewContent()

  // 切换展开/收起状态
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded)
  }

  return (
    <div className={`reasoning-container ${className}`}>
      <div className="reasoning-header">
        <div className="reasoning-icon">
          🤔
        </div>
        <div className="reasoning-title">
          思维链
          {isStreaming && (
            <span className="reasoning-streaming-indicator">
              <span className="dot"></span>
              <span className="dot"></span>
              <span className="dot"></span>
            </span>
          )}
        </div>
        <button
          onClick={toggleExpanded}
          className="reasoning-toggle-btn"
          aria-label={isExpanded ? "收起思维链" : "展开思维链"}
        >
          {isExpanded ? (
            <ChevronUpIcon className="w-4 h-4" />
          ) : (
            <ChevronDownIcon className="w-4 h-4" />
          )}
        </button>
      </div>
      
      <div className="reasoning-content">
        {isExpanded ? (
          <div
            ref={contentRef}
            className="reasoning-full-content"
          >
            {displayContent}
            {isStreaming && <span className="reasoning-cursor">|</span>}
          </div>
        ) : (
          <div
            ref={previewRef}
            className="reasoning-preview-content"
          >
            {previewContent}
            {isStreaming && <span className="reasoning-cursor">|</span>}
          </div>
        )}
      </div>

      <style jsx>{`
        .reasoning-container {
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
          border: 1px solid #e2e8f0;
          border-radius: 12px;
          margin: 8px 0;
          overflow: hidden;
          transition: all 0.3s ease;
        }

        .reasoning-container:hover {
          border-color: #cbd5e1;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .reasoning-header {
          display: flex;
          align-items: center;
          padding: 12px 16px;
          background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
          border-bottom: 1px solid #e2e8f0;
          cursor: pointer;
          user-select: none;
        }

        .reasoning-header:hover {
          background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
        }

        .reasoning-icon {
          font-size: 16px;
          margin-right: 8px;
        }

        .reasoning-title {
          flex: 1;
          font-size: 14px;
          font-weight: 600;
          color: #475569;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .reasoning-streaming-indicator {
          display: inline-flex;
          gap: 2px;
        }

        .dot {
          width: 4px;
          height: 4px;
          background-color: #3b82f6;
          border-radius: 50%;
          animation: pulse 1.4s infinite ease-in-out;
        }

        .dot:nth-child(1) { animation-delay: -0.32s; }
        .dot:nth-child(2) { animation-delay: -0.16s; }
        .dot:nth-child(3) { animation-delay: 0s; }

        @keyframes pulse {
          0%, 80%, 100% {
            transform: scale(0);
            opacity: 0.5;
          }
          40% {
            transform: scale(1);
            opacity: 1;
          }
        }

        .reasoning-toggle-btn {
          background: none;
          border: none;
          color: #64748b;
          cursor: pointer;
          padding: 4px;
          border-radius: 4px;
          transition: all 0.2s ease;
        }

        .reasoning-toggle-btn:hover {
          background-color: #e2e8f0;
          color: #475569;
        }

        .reasoning-content {
          padding: 16px;
          font-size: 14px;
          line-height: 1.6;
          color: #64748b;
          background: #ffffff;
        }

        .reasoning-full-content {
          white-space: pre-wrap;
          word-wrap: break-word;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
          max-height: 300px;
          overflow-y: auto;
          scrollbar-width: thin;
          scrollbar-color: #cbd5e1 #f1f5f9;
        }

        .reasoning-full-content::-webkit-scrollbar {
          width: 6px;
        }

        .reasoning-full-content::-webkit-scrollbar-track {
          background: #f1f5f9;
          border-radius: 3px;
        }

        .reasoning-full-content::-webkit-scrollbar-thumb {
          background: #cbd5e1;
          border-radius: 3px;
        }

        .reasoning-full-content::-webkit-scrollbar-thumb:hover {
          background: #94a3b8;
        }

        .reasoning-preview-content {
          white-space: nowrap;
          overflow-x: auto;
          word-wrap: break-word;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
          opacity: 0.8;
          scrollbar-width: none;
          -ms-overflow-style: none;
        }

        .reasoning-preview-content::-webkit-scrollbar {
          display: none;
        }

        .reasoning-cursor {
          color: #3b82f6;
          animation: blink 1s infinite;
          font-weight: bold;
        }

        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }

        /* 响应式设计 */
        @media (max-width: 640px) {
          .reasoning-header {
            padding: 10px 12px;
          }
          
          .reasoning-content {
            padding: 12px;
            font-size: 13px;
          }
          
          .reasoning-title {
            font-size: 13px;
          }
        }
      `}</style>
    </div>
  )
}
