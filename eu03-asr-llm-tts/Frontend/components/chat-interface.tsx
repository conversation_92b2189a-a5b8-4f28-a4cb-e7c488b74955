"use client"

/**
 * 重构后的聊天界面组件
 * 使用新的状态管理器，简化状态管理逻辑，保持UI界面不变
 */

import { useEffect, useRef, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Mic, MicOff, Send, X, Keyboard, MessageSquare } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useToast } from "@/hooks/use-toast"
import { useAppState } from "@/components/app-state-provider"
import { ToolCallList } from "@/components/tool-call-display"
import ReasoningDisplay from "@/components/reasoning-display"
import { useI18n } from "@/lib/i18n/context"

export function ChatInterface() {
  const { toast } = useToast()
  const { t } = useI18n()
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  
  // 使用统一的状态管理器
  const {
    state,
    setInput,
    sendUserInput,
    setInputMode,
    setListening,
    interruptTurn,
    setAssistantTyping,
  } = useAppState()

  // 从状态中解构需要的值
  const {
    connection: { state: connectionState, error: connectionError },
    conversation: { messages, currentInput, isAssistantTyping },
    ui: { inputMode, isListening, asrResult, isInputDisabled },
  } = state

  const isConnected = connectionState === 'connected'

  // ============================================================================
  // 副作用处理
  // ============================================================================

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  // 监听ASR结果，仅在语音模式下处理
  useEffect(() => {
    // 只有在语音模式且正在监听时才处理ASR结果
    if (inputMode === 'voice' && isListening && asrResult.trim()) {
      setInput(asrResult)
      sendUserInput(asrResult)
    }
    // 键盘模式下完全忽略ASR结果，避免干扰用户输入
  }, [asrResult, inputMode, isListening, setInput, sendUserInput])

  // 组件初始化提示
  useEffect(() => {
    toast({
      title: "欢迎使用语音对话助手",
      description: "当前为键盘对话模式，可点击右上角按钮切换到语音对话模式。",
      duration: 5000,
    })
  }, [toast])

  // ============================================================================
  // 自动调整textarea高度
  // ============================================================================

  const adjustTextareaHeight = useCallback(() => {
    if (textareaRef.current) {
      const textarea = textareaRef.current
      textarea.style.height = 'auto'
      const scrollHeight = textarea.scrollHeight
      const maxHeight = 128 // max-h-32 = 8rem = 128px
      textarea.style.height = `${Math.min(scrollHeight, maxHeight)}px`
    }
  }, [])

  // 当输入内容变化时自动调整高度
  useEffect(() => {
    adjustTextareaHeight()
  }, [currentInput, adjustTextareaHeight])

  // ============================================================================
  // 事件处理函数
  // ============================================================================

  const handleSendMessage = useCallback(() => {
    if (!currentInput.trim()) return
    
    if (!isConnected) {
      toast({
        title: "连接错误",
        description: "未连接到服务器，请检查网络连接后重试。",
        variant: "destructive",
        duration: 3000,
      })
      return
    }

    sendUserInput()
  }, [currentInput, isConnected, sendUserInput, toast])

  const toggleInputMode = useCallback(() => {
    // 检查是否有未发送的输入内容
    if (currentInput.trim()) {
      const shouldSend = window.confirm(t.chat.confirm.switchModeWithInput)
      if (shouldSend) {
        handleSendMessage()
      } else {
        setInput("")
      }
    }

    // 如果当前正在监听，先停止监听
    if (isListening) {
      setListening(false)
    }

    // 切换模式
    const newMode = inputMode === 'voice' ? 'keyboard' : 'voice'
    setInputMode(newMode)

    // 显示切换提示
    toast({
      title: newMode === 'voice' ? t.chat.toast.switchedToVoice : t.chat.toast.switchedToKeyboard,
      description: newMode === 'voice'
        ? t.chat.toast.voiceModeDescription
        : t.chat.toast.keyboardModeDescription,
      duration: 3000,
    })
  }, [currentInput, isListening, inputMode, handleSendMessage, setInput, setListening, setInputMode, toast, t])

  const toggleListening = useCallback(() => {
    if (!isConnected) {
      toast({
        title: "连接错误",
        description: "未连接到服务器，无法启动语音识别。",
        variant: "destructive",
        duration: 3000,
      })
      return
    }

    if (isListening) {
      setListening(false)
      setInput("")
    } else {
      setListening(true)
      setInput("")
      
      toast({
        title: "语音识别已启动",
        description: "正在监听，请说话...",
        duration: 2000,
      })
    }
  }, [isConnected, isListening, setListening, setInput, toast])

  const handleInterrupt = useCallback(() => {
    if (!isConnected) return
    
    // 停止助手输入状态
    setAssistantTyping(false)
    
    // 发送中断信号
    interruptTurn()
  }, [isConnected, setAssistantTyping, interruptTurn])

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    // Ctrl+Enter: 发送消息（在任何模式下都可用）
    if (e.key === "Enter" && e.ctrlKey && isConnected) {
      e.preventDefault()
      handleSendMessage()
      return
    }

    // Enter: 换行（不发送消息）
    // 默认行为就是换行，所以不需要特殊处理
  }, [isConnected, handleSendMessage])

  // ============================================================================
  // 渲染函数
  // ============================================================================

  return (
    <div className="flex flex-col h-[600px] w-full border rounded-lg overflow-hidden bg-white dark:bg-gray-950 shadow-lg">
      {/* 头部 */}
      <div className="p-4 border-b">
        <div className="flex justify-between items-center mb-2">
          <div className="flex items-center flex-shrink-0">
            <h2 className="text-lg font-semibold">{t.chat.title}</h2>
            <div 
              className={`ml-3 w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}
              title={isConnected ? t.chat.connectionStatus.connected : t.chat.connectionStatus.disconnected}
            />
            <div className="ml-3 px-2 py-1 text-xs rounded-full bg-gray-200 dark:bg-gray-700 hidden sm:block">
              {inputMode === 'voice' ? t.chat.inputMode.voice : t.chat.inputMode.keyboard}
            </div>
          </div>

          <div className="flex items-center gap-2">
            {connectionError && (
              <span className="text-red-500 text-sm max-w-[150px] truncate hidden sm:inline-block" title={connectionError}>
                {connectionError}
              </span>
            )}

            <Button
              variant="ghost"
              size="sm"
              onClick={toggleInputMode}
              disabled={!isConnected}
              title={inputMode === 'voice' ? t.chat.inputMode.switchToKeyboard : t.chat.inputMode.switchToVoice}
              className="whitespace-nowrap"
            >
              {inputMode === 'voice' ? <Keyboard className="h-4 w-4 mr-1" /> : <MessageSquare className="h-4 w-4 mr-1" />}
              {inputMode === 'voice' ? t.chat.inputMode.keyboardButton : t.chat.inputMode.voiceButton}
            </Button>
          </div>
        </div>

        {/* Stop按钮 */}
        <div className={`flex justify-end transition-all duration-300 ease-in-out ${isAssistantTyping ? 'opacity-100 max-h-10 mt-2' : 'opacity-0 max-h-0 overflow-hidden'}`}>
          <Button
            variant="outline"
            size="sm"
            onClick={handleInterrupt}
            className="text-red-500 whitespace-nowrap"
          >
            <X className="h-4 w-4 mr-1" />
            {t.chat.stopGenerating}
          </Button>
        </div>
      </div>

      {/* 消息区域 */}
      <ScrollArea className="flex-1 p-4 overflow-x-hidden">
        <div className="space-y-4 max-w-full">
          {messages.length === 0 ? (
            <div className="text-center text-gray-500 my-8">
              {t.chat.emptyMessage}
            </div>
          ) : (
            messages.map((message) => (
              <div key={message.id} className={`grid ${message.role === "user" ? "grid-cols-[minmax(0,1fr)_auto] justify-items-end" : "grid-cols-[auto_minmax(0,1fr)] justify-items-start"} gap-2 items-start w-full max-w-full`}>
                {message.role === "assistant" && (
                  <div className="flex-shrink-0 mt-1">
                    <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                      <img
                        src="/user-default-avatar.png"
                        alt="AI Assistant"
                        className="w-8 h-8 rounded-full object-cover"
                      />
                    </div>
                  </div>
                )}
                <div
                  className={`min-w-0 p-3 rounded-lg ${
                    message.role === "user"
                      ? "bg-black text-white rounded-br-none justify-self-end max-w-[calc(100%-3rem)] w-auto inline-block"
                      : "bg-white text-gray-800 border border-gray-200 rounded-bl-none dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700 justify-self-start w-full max-w-[calc(100%-3rem)]"
                  }`}
                >
                  {/* 思维链显示（仅助手消息） */}
                  {message.role === "assistant" && message.reasoningContent && (
                    <div className="mb-3">
                      <ReasoningDisplay
                        reasoningContent={message.reasoningContent}
                        isStreaming={message.isReasoningStreaming || false}
                      />
                    </div>
                  )}

                  <div className="whitespace-pre-wrap break-words overflow-wrap-anywhere">{message.content}</div>

                  {message.toolCalls && message.toolCalls.length > 0 && (
                    <div className="mt-3 min-w-0">
                      <ToolCallList toolCalls={message.toolCalls} />
                    </div>
                  )}
                </div>
                {message.role === "user" && (
                  <div className="flex-shrink-0 mt-1">
                    <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
                      <span className="text-white text-xs font-semibold">我</span>
                    </div>
                  </div>
                )}
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* 输入区域 */}
      <div className="p-4 border-t">
        <div className="flex flex-col space-y-2">
          <div className={`text-xs text-center ${inputMode === 'voice' ? 'text-green-600 dark:text-green-400' : 'text-blue-600 dark:text-blue-400'}`}>
            {inputMode === 'voice'
              ? (isListening
                  ? t.chat.voiceModeHint.listening
                  : t.chat.voiceModeHint.idle)
              : t.chat.keyboardModeHint}
          </div>

          <div className="flex space-x-2">
            {inputMode === 'voice' && (
              <Button
                variant={isListening ? "destructive" : "default"}
                size="icon"
                onClick={toggleListening}
                disabled={!isConnected}
                className="flex-shrink-0"
                title={!isConnected
                  ? "未连接到服务器"
                  : isListening
                    ? "停止监听"
                    : "开始语音输入"}
              >
                {isListening ? <MicOff className="h-5 w-5" /> : <Mic className="h-5 w-5" />}
              </Button>
            )}

            <Textarea
              ref={textareaRef}
              value={currentInput}
              onChange={(e) => setInput(e.target.value)}
              placeholder={!isConnected
                ? t.chat.connectionStatus.disconnected
                : inputMode === 'voice'
                  ? (isListening ? t.chat.voiceModeHint.listening : t.chat.voiceModeHint.idle)
                  : t.chat.inputPlaceholder}
              disabled={!isConnected || isInputDisabled || (inputMode === 'voice' && isListening)}
              onKeyDown={handleKeyDown}
              className={`flex-1 min-h-[40px] max-h-32 resize-none overflow-y-auto ${inputMode === 'voice' ? (isListening ? 'bg-gray-100 dark:bg-gray-800' : '') : ''}`}
              rows={1}
            />

            <Button
              onClick={handleSendMessage}
              disabled={!currentInput.trim() || !isConnected || isInputDisabled || (inputMode === 'voice' && isListening)}
              size="icon"
              className="flex-shrink-0"
              title={!isConnected
                ? t.chat.sendButton.disconnected
                : inputMode === 'voice' && isListening
                  ? t.chat.sendButton.voiceListening
                  : t.chat.sendButton.send}
            >
              <Send className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
