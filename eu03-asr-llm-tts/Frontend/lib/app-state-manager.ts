/**
 * 应用状态管理器
 * 
 * 统一管理前端应用的所有状态，包括：
 * - WebSocket连接状态
 * - 消息和对话状态
 * - UI交互状态
 * - 错误和系统状态
 * 
 * 设计原则：
 * 1. 类型安全：完整的TypeScript类型支持
 * 2. 不可变性：使用不可变状态更新
 * 3. 可预测性：清晰的状态转换逻辑
 * 4. 性能优化：减少不必要的重渲染
 */

// 这个文件只定义类型和reducer，不需要React导入

// ============================================================================
// 类型定义
// ============================================================================

export type WebSocketMessage = {
  type: string
  payload: any
}

export type ToolCallStatus = 'pending' | 'success' | 'error'

export type ToolCall = {
  name: string
  id: string
  args: any
  result?: string
  error?: string
  status: ToolCallStatus
}

export type Message = {
  id: string
  role: "user" | "assistant" | "system"
  content: string
  toolCalls?: ToolCall[]
  timestamp: number
  // 新增思维链相关字段
  reasoningContent?: string
  isReasoningStreaming?: boolean
}

export type ConnectionState = 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'error'

export type InputMode = 'keyboard' | 'voice'

export type SystemStatus = {
  module: string
  status: string
  message?: string
  data?: any
}

export type ErrorInfo = {
  id: string
  message: string
  source?: string
  timestamp: number
  severity: 'info' | 'warning' | 'error'
}

// ============================================================================
// 应用状态接口
// ============================================================================

export interface AppState {
  // WebSocket连接状态
  connection: {
    state: ConnectionState
    socket: WebSocket | null
    url: string | null
    error: string | null
    reconnectAttempts: number
    lastConnectedAt: number | null
  }

  // 消息和对话状态
  conversation: {
    messages: Message[]
    currentInput: string
    isAssistantTyping: boolean
    currentTurnId: string | null
    streamingContent: string
    isReceivingStream: boolean
    hasToolCallsInCurrentTurn: boolean     // 当前turn是否有工具调用
  }

  // UI交互状态
  ui: {
    inputMode: InputMode
    isListening: boolean
    asrResult: string
    showStopButton: boolean
    isInputDisabled: boolean
  }

  // 系统状态
  system: {
    statuses: Record<string, SystemStatus>
    errors: ErrorInfo[]
    lastMessage: WebSocketMessage | null
    messageHistory: WebSocketMessage[]
  }

  // 工具调用状态
  tools: {
    activeCalls: Record<string, ToolCall>
    callHistory: ToolCall[]
  }
}

// ============================================================================
// 状态操作类型
// ============================================================================

export type AppAction =
  // 连接相关操作
  | { type: 'CONNECTION_CONNECTING'; payload: { url: string } }
  | { type: 'CONNECTION_CONNECTED'; payload: { socket: WebSocket } }
  | { type: 'CONNECTION_DISCONNECTED'; payload: { error?: string } }
  | { type: 'CONNECTION_ERROR'; payload: { error: string } }
  | { type: 'CONNECTION_RECONNECTING'; payload: { attempts: number } }
  | { type: 'CONNECTION_RESET_ATTEMPTS' }

  // 消息相关操作
  | { type: 'MESSAGE_ADD'; payload: { message: Message } }
  | { type: 'MESSAGE_UPDATE'; payload: { id: string; updates: Partial<Message> } }
  | { type: 'MESSAGE_CLEAR_ALL' }
  | { type: 'INPUT_SET'; payload: { text: string } }
  | { type: 'INPUT_CLEAR' }

  // 流式响应操作
  | { type: 'STREAM_START'; payload: { turnId: string } }
  | { type: 'STREAM_CHUNK'; payload: { content: string; isFirst?: boolean } }
  | { type: 'STREAM_END'; payload: { finalContent?: string } }
  | { type: 'ASSISTANT_TYPING_SET'; payload: { isTyping: boolean } }

  // UI状态操作
  | { type: 'UI_SET_INPUT_MODE'; payload: { mode: InputMode } }
  | { type: 'UI_SET_LISTENING'; payload: { isListening: boolean } }
  | { type: 'UI_SET_ASR_RESULT'; payload: { result: string } }
  | { type: 'UI_SET_INPUT_DISABLED'; payload: { disabled: boolean } }

  // 系统状态操作
  | { type: 'SYSTEM_STATUS_UPDATE'; payload: SystemStatus }
  | { type: 'SYSTEM_ERROR_ADD'; payload: Omit<ErrorInfo, 'id' | 'timestamp'> }
  | { type: 'SYSTEM_ERROR_CLEAR'; payload: { id: string } }
  | { type: 'SYSTEM_ERROR_CLEAR_ALL' }
  | { type: 'SYSTEM_MESSAGE_RECEIVED'; payload: { message: WebSocketMessage } }

  // 工具调用操作
  | { type: 'TOOL_CALL_START'; payload: { toolCall: ToolCall } }
  | { type: 'TOOL_CALL_UPDATE'; payload: { id: string; updates: Partial<ToolCall> } }
  | { type: 'TOOL_CALL_END'; payload: { id: string; result: string } }
  | { type: 'TOOL_CALL_ERROR'; payload: { id: string; error: string } }

  // 思维链操作
  | { type: 'REASONING_START'; payload: { messageId: string } }
  | { type: 'REASONING_CHUNK'; payload: { messageId: string; content: string } }
  | { type: 'REASONING_END'; payload: { messageId: string } }

  // 轮次管理操作
  | { type: 'TURN_START'; payload: { turnId: string } }
  | { type: 'TURN_END' }
  | { type: 'TURN_INTERRUPT' }

// ============================================================================
// 初始状态
// ============================================================================

export const initialAppState: AppState = {
  connection: {
    state: 'disconnected',
    socket: null,
    url: null,
    error: null,
    reconnectAttempts: 0,
    lastConnectedAt: null,
  },

  conversation: {
    messages: [],
    currentInput: '',
    isAssistantTyping: false,
    currentTurnId: null,
    streamingContent: '',
    isReceivingStream: false,
    hasToolCallsInCurrentTurn: false,
  },

  ui: {
    inputMode: 'keyboard',
    isListening: false,
    asrResult: '',
    showStopButton: false,
    isInputDisabled: false,
  },

  system: {
    statuses: {},
    errors: [],
    lastMessage: null,
    messageHistory: [],
  },

  tools: {
    activeCalls: {},
    callHistory: [],
  },
}

// ============================================================================
// 状态缩减器
// ============================================================================

export function appStateReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    // 连接状态管理
    case 'CONNECTION_CONNECTING':
      return {
        ...state,
        connection: {
          ...state.connection,
          state: 'connecting',
          url: action.payload.url,
          error: null,
        },
      }

    case 'CONNECTION_CONNECTED':
      return {
        ...state,
        connection: {
          ...state.connection,
          state: 'connected',
          socket: action.payload.socket,
          error: null,
          reconnectAttempts: 0,
          lastConnectedAt: Date.now(),
        },
      }

    case 'CONNECTION_DISCONNECTED':
      return {
        ...state,
        connection: {
          ...state.connection,
          state: 'disconnected',
          socket: null,
          error: action.payload.error || null,
        },
      }

    case 'CONNECTION_ERROR':
      return {
        ...state,
        connection: {
          ...state.connection,
          state: 'error',
          error: action.payload.error,
        },
      }

    case 'CONNECTION_RECONNECTING':
      return {
        ...state,
        connection: {
          ...state.connection,
          state: 'reconnecting',
          reconnectAttempts: action.payload.attempts,
        },
      }

    case 'CONNECTION_RESET_ATTEMPTS':
      return {
        ...state,
        connection: {
          ...state.connection,
          reconnectAttempts: 0,
        },
      }

    // 消息管理
    case 'MESSAGE_ADD':
      return {
        ...state,
        conversation: {
          ...state.conversation,
          messages: [...state.conversation.messages, action.payload.message],
        },
      }

    case 'MESSAGE_UPDATE':
      return {
        ...state,
        conversation: {
          ...state.conversation,
          messages: state.conversation.messages.map(msg =>
            msg.id === action.payload.id
              ? { ...msg, ...action.payload.updates }
              : msg
          ),
        },
      }

    case 'MESSAGE_CLEAR_ALL':
      return {
        ...state,
        conversation: {
          ...state.conversation,
          messages: [],
        },
      }

    case 'INPUT_SET':
      return {
        ...state,
        conversation: {
          ...state.conversation,
          currentInput: action.payload.text,
        },
      }

    case 'INPUT_CLEAR':
      return {
        ...state,
        conversation: {
          ...state.conversation,
          currentInput: '',
        },
      }

    // 流式响应管理
    case 'STREAM_START':
      return {
        ...state,
        conversation: {
          ...state.conversation,
          currentTurnId: action.payload.turnId,
          isReceivingStream: true,
          streamingContent: '',
          isAssistantTyping: true,
        },
      }

    case 'STREAM_CHUNK':
      return {
        ...state,
        conversation: {
          ...state.conversation,
          streamingContent: action.payload.isFirst 
            ? action.payload.content
            : state.conversation.streamingContent + action.payload.content,
        },
      }

    case 'STREAM_END':
      return {
        ...state,
        conversation: {
          ...state.conversation,
          isReceivingStream: false,
          streamingContent: action.payload.finalContent || state.conversation.streamingContent,
        },
      }

    case 'ASSISTANT_TYPING_SET':
      return {
        ...state,
        conversation: {
          ...state.conversation,
          isAssistantTyping: action.payload.isTyping,
        },
        ui: {
          ...state.ui,
          showStopButton: action.payload.isTyping,
        },
      }



    // UI状态管理
    case 'UI_SET_INPUT_MODE':
      return {
        ...state,
        ui: {
          ...state.ui,
          inputMode: action.payload.mode,
        },
      }

    case 'UI_SET_LISTENING':
      return {
        ...state,
        ui: {
          ...state.ui,
          isListening: action.payload.isListening,
        },
      }

    case 'UI_SET_ASR_RESULT':
      return {
        ...state,
        ui: {
          ...state.ui,
          asrResult: action.payload.result,
        },
      }

    case 'UI_SET_INPUT_DISABLED':
      return {
        ...state,
        ui: {
          ...state.ui,
          isInputDisabled: action.payload.disabled,
        },
      }

    // 系统状态管理
    case 'SYSTEM_STATUS_UPDATE':
      return {
        ...state,
        system: {
          ...state.system,
          statuses: {
            ...state.system.statuses,
            [action.payload.module]: action.payload,
          },
        },
      }

    case 'SYSTEM_ERROR_ADD':
      const newError: ErrorInfo = {
        ...action.payload,
        id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
        timestamp: Date.now(),
      }
      return {
        ...state,
        system: {
          ...state.system,
          errors: [...state.system.errors, newError],
        },
      }

    case 'SYSTEM_ERROR_CLEAR':
      return {
        ...state,
        system: {
          ...state.system,
          errors: state.system.errors.filter(error => error.id !== action.payload.id),
        },
      }

    case 'SYSTEM_ERROR_CLEAR_ALL':
      return {
        ...state,
        system: {
          ...state.system,
          errors: [],
        },
      }

    case 'SYSTEM_MESSAGE_RECEIVED':
      return {
        ...state,
        system: {
          ...state.system,
          lastMessage: action.payload.message,
          messageHistory: [
            ...state.system.messageHistory.slice(-99), // 保留最近100条消息
            action.payload.message,
          ],
        },
      }

    // 工具调用管理
    case 'TOOL_CALL_START':
      return {
        ...state,
        conversation: {
          ...state.conversation,
          hasToolCallsInCurrentTurn: true,  // 标记当前turn有工具调用
        },
        tools: {
          ...state.tools,
          activeCalls: {
            ...state.tools.activeCalls,
            [action.payload.toolCall.id]: action.payload.toolCall,
          },
        },
      }

    case 'TOOL_CALL_UPDATE':
      const existingCall = state.tools.activeCalls[action.payload.id]
      if (!existingCall) return state

      return {
        ...state,
        tools: {
          ...state.tools,
          activeCalls: {
            ...state.tools.activeCalls,
            [action.payload.id]: {
              ...existingCall,
              ...action.payload.updates,
            },
          },
        },
      }

    case 'TOOL_CALL_END':
      const completedCall = state.tools.activeCalls[action.payload.id]
      if (!completedCall) return state

      const finalCall = {
        ...completedCall,
        result: action.payload.result,
        status: 'success' as ToolCallStatus,
      }

      const { [action.payload.id]: removed, ...remainingCalls } = state.tools.activeCalls

      return {
        ...state,
        tools: {
          activeCalls: remainingCalls,
          callHistory: [...state.tools.callHistory, finalCall],
        },
      }

    case 'TOOL_CALL_ERROR':
      const failedCall = state.tools.activeCalls[action.payload.id]
      if (!failedCall) return state

      const errorCall = {
        ...failedCall,
        error: action.payload.error,
        status: 'error' as ToolCallStatus,
      }

      const { [action.payload.id]: removedError, ...remainingErrorCalls } = state.tools.activeCalls

      return {
        ...state,
        tools: {
          activeCalls: remainingErrorCalls,
          callHistory: [...state.tools.callHistory, errorCall],
        },
      }

    // 思维链管理
    case 'REASONING_START':
      return {
        ...state,
        conversation: {
          ...state.conversation,
          messages: state.conversation.messages.map(msg =>
            msg.id === action.payload.messageId
              ? { ...msg, isReasoningStreaming: true, reasoningContent: '' }
              : msg
          ),
        },
      }

    case 'REASONING_CHUNK':
      return {
        ...state,
        conversation: {
          ...state.conversation,
          messages: state.conversation.messages.map(msg =>
            msg.id === action.payload.messageId
              ? {
                  ...msg,
                  reasoningContent: (msg.reasoningContent || '') + action.payload.content,
                  isReasoningStreaming: true
                }
              : msg
          ),
        },
      }

    case 'REASONING_END':
      return {
        ...state,
        conversation: {
          ...state.conversation,
          messages: state.conversation.messages.map(msg =>
            msg.id === action.payload.messageId
              ? { ...msg, isReasoningStreaming: false }
              : msg
          ),
        },
      }

    // 轮次管理
    case 'TURN_START':
      return {
        ...state,
        conversation: {
          ...state.conversation,
          currentTurnId: action.payload.turnId,
          isAssistantTyping: false,
          hasToolCallsInCurrentTurn: false,   // 重置工具调用标志
        },
        tools: {
          ...state.tools,
          activeCalls: {}, // 清空活跃的工具调用
        },
      }

    case 'TURN_END':
      return {
        ...state,
        conversation: {
          ...state.conversation,
          currentTurnId: null,
          isAssistantTyping: false,
          isReceivingStream: false,
          streamingContent: '',
          hasToolCallsInCurrentTurn: false,   // 重置工具调用标志
        },
        ui: {
          ...state.ui,
          showStopButton: false,
        },
        tools: {
          ...state.tools,
          activeCalls: {},
        },
      }

    case 'TURN_INTERRUPT':
      return {
        ...state,
        conversation: {
          ...state.conversation,
          currentTurnId: null,
          isAssistantTyping: false,
          isReceivingStream: false,
          streamingContent: '',
        },
        ui: {
          ...state.ui,
          showStopButton: false,
        },
        tools: {
          ...state.tools,
          activeCalls: {},
        },
      }

    default:
      return state
  }
}
