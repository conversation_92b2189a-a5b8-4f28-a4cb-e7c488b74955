<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.0.16 Chrome/132.0.6834.196 Electron/34.2.0 Safari/537.36" version="26.0.16">
  <diagram name="第 1 页" id="5iLjqwyCwsKXc04yD3Kl">
    <mxGraphModel dx="1524" dy="880" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-1" value="" style="whiteSpace=wrap;strokeWidth=2;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="20" y="1746" width="839" height="208" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-2" value="PID控制逻辑" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="309" y="1280" width="290" height="312" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-3" value="目标选择逻辑" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="140" y="503" width="330" height="440" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-4" value="摄像头获取图像" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="628" y="28" width="172" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-60" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-5" target="Ww2Gc6Sinkn6AS2U0GB0-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-61" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="Ww2Gc6Sinkn6AS2U0GB0-60">
          <mxGeometry x="0.0425" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-62" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=1;entryDx=0;entryDy=0;curved=1;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-5" target="Ww2Gc6Sinkn6AS2U0GB0-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-63" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="Ww2Gc6Sinkn6AS2U0GB0-62">
          <mxGeometry x="-0.1682" y="23" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-5" value="YOLO模型检测人脸" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="207.5" y="203" width="195" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-6" value="检测到人脸?" style="rhombus;strokeWidth=2;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="235" y="388" width="140" height="78" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-7" value="目标选择与优先级计算" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="195" y="528" width="220" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-8" value="计算各目标优先级得分" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="195" y="632" width="220" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-9" value="基于距离/面积/稳定性/置信度" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="175" y="736" width="260" height="78" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-10" value="选择最优跟踪目标" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="211" y="864" width="188" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-57" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;curved=1;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-11" target="Ww2Gc6Sinkn6AS2U0GB0-12">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-11" value="计算目标与画面中心偏移量" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="179" y="993" width="252" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-58" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;curved=1;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-12" target="Ww2Gc6Sinkn6AS2U0GB0-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-59" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="Ww2Gc6Sinkn6AS2U0GB0-58">
          <mxGeometry x="0.2134" y="12" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-12" value="偏移量超过阈值?" style="rhombus;strokeWidth=2;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="368" y="1116" width="172" height="91" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-13" value="PID控制器计算控制量" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="349" y="1305" width="211" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-14" value="基于距离的自适应增益" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="344" y="1409" width="220" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-15" value="生成平滑控制输出" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="360" y="1513" width="188" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-16" value="计算舵机目标角度" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="360" y="1642" width="188" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-47" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=1;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-17" target="Ww2Gc6Sinkn6AS2U0GB0-20">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-48" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-17" target="Ww2Gc6Sinkn6AS2U0GB0-18">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-17" value="根据距离选择舵机控制策略" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="328" y="1771" width="252" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-46" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;curved=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-18" target="Ww2Gc6Sinkn6AS2U0GB0-21">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-18" value="远距离:同时控制两个舵机" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="58" y="1875" width="242" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-44" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-19" target="Ww2Gc6Sinkn6AS2U0GB0-21">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-19" value="中距离:快速顺序控制" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="349" y="1875" width="210" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-45" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;curved=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-20" target="Ww2Gc6Sinkn6AS2U0GB0-21">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-20" value="近距离:平滑顺序控制" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="609" y="1875" width="210" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-55" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;curved=1;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-21" target="Ww2Gc6Sinkn6AS2U0GB0-4">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="454" y="2147" />
              <mxPoint x="1333" y="2147" />
              <mxPoint x="1333" y="55" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-21" value="通过串口发送控制命令" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="344" y="2008" width="220" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-22" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0;exitY=0.73;entryX=0.5;entryY=0;rounded=0;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-4" target="Ww2Gc6Sinkn6AS2U0GB0-5">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="301" y="99" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-25" value="是" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-6" target="Ww2Gc6Sinkn6AS2U0GB0-7">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-26" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-7" target="Ww2Gc6Sinkn6AS2U0GB0-8">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-27" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-8" target="Ww2Gc6Sinkn6AS2U0GB0-9">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-28" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-9" target="Ww2Gc6Sinkn6AS2U0GB0-10">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-29" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-10" target="Ww2Gc6Sinkn6AS2U0GB0-11">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-32" value="是" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-12" target="Ww2Gc6Sinkn6AS2U0GB0-13">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-33" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-13" target="Ww2Gc6Sinkn6AS2U0GB0-14">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-34" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-14" target="Ww2Gc6Sinkn6AS2U0GB0-15">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-35" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-15" target="Ww2Gc6Sinkn6AS2U0GB0-16">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-36" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-16" target="Ww2Gc6Sinkn6AS2U0GB0-17">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-38" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" edge="1" parent="1" source="Ww2Gc6Sinkn6AS2U0GB0-17" target="Ww2Gc6Sinkn6AS2U0GB0-19">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-52" value="&lt;b style=&quot;forced-color-adjust: none; color: rgb(0, 0, 0); font-family: Helvetica; font-size: 20px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: right; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&quot;&gt;舵机控制策略&lt;/b&gt;" style="text;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="17.000344827586503" y="1710.9972413793103" width="148" height="46" as="geometry" />
        </mxCell>
        <mxCell id="Ww2Gc6Sinkn6AS2U0GB0-53" value="&lt;b style=&quot;forced-color-adjust: none; color: rgb(0, 0, 0); font-family: Helvetica; font-size: 20px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: right; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&quot;&gt;PID控制&lt;/b&gt;" style="text;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="1246" width="82" height="46" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
