# RDK YOLO V8 人脸检测与舵机控制系统 - 项目结构说明

## 项目概述

本项目是基于RDK（瑞芯微开发套件）平台的YOLO V8目标检测系统，主要实现了人脸检测和舵机控制功能。系统通过摄像头实时捕获视频流，使用YOLO V8模型进行人脸检测，并可以通过舵机控制云台实现对检测目标的自动跟踪。

## 目录结构

```
├── docs/                       # 文档目录
│   ├── 人脸追踪逻辑流程.drawio    # 人脸追踪逻辑流程图
│   └── 项目结构说明.md           # 本文档
│
├── model/                      # 模型目录
│   └── yolo11n_face_detect_bayese_320x320_nv12.bin  # YOLO V8人脸检测模型
│
├── pic/                        # 图片资源目录
│   └── 周杰伦.jpg               # 测试图片
│
├── src/                        # 源代码目录
│   ├── config/                 # 配置文件目录
│   │   └── settings.py         # 系统配置参数
│   │
│   ├── detection_core/         # 检测核心模块
│   │   ├── base_model.py       # 模型基类
│   │   ├── README.md           # 模块说明
│   │   └── yolo_detect.py      # YOLO检测实现
│   │
│   ├── utils/                  # 工具函数目录
│   │   ├── Algorithm_README.md # 算法说明
│   │   ├── coordinate.py       # 坐标计算
│   │   ├── kalman_filter.py    # 卡尔曼滤波器
│   │   ├── pid_controller.py   # PID控制器
│   │   ├── README.md           # 工具模块说明
│   │   ├── serial_protocol.py  # 串口通信协议
│   │   ├── servo_controller.py # 舵机控制器
│   │   ├── servo_protocol.py   # 舵机通信协议
│   │   └── servo_tracking.py   # 舵机追踪
│   │
│   ├── web/                    # Web应用模块
│   │   ├── app.py              # Flask应用
│   │   ├── README.md           # Web模块说明
│   │   ├── static/             # 静态资源
│   │   │   ├── css/            # CSS样式
│   │   │   ├── js/             # JavaScript脚本
│   │   │   └── models/         # 3D模型文件
│   │   └── templates/          # HTML模板
│   │       ├── disabled.html   # 禁用舵机时的页面
│   │       └── index.html      # 主页面
│   │
│   ├── __init__.py             # 包初始化文件
│   └── main.py                 # 主程序入口
│
├── README.md                   # 项目说明
├── requirements.txt            # 依赖列表
├── start.sh                    # 启动脚本
└── version.md                  # 版本历史
```

## 核心模块说明

### 1. 检测核心模块 (detection_core)

- **base_model.py**: 定义了模型的基类，包含模型加载、预处理等通用功能
- **yolo_detect.py**: 实现了YOLO V8模型的推理和后处理逻辑

### 2. 工具模块 (utils)

- **coordinate.py**: 实现坐标计算，用于计算检测目标与画面中心的偏移
- **kalman_filter.py**: 实现卡尔曼滤波器，用于平滑目标运动轨迹
- **pid_controller.py**: 实现PID控制器，用于舵机控制
- **serial_protocol.py**: 实现串口通信协议
- **servo_controller.py**: 实现舵机控制器，负责与Arduino通信
- **servo_protocol.py**: 实现舵机通信协议
- **servo_tracking.py**: 实现舵机追踪逻辑

### 3. Web应用模块 (web)

- **app.py**: 实现Flask Web应用，提供用户界面和API
- **static/**: 包含静态资源，如CSS、JavaScript和3D模型
- **templates/**: 包含HTML模板

### 4. 配置模块 (config)

- **settings.py**: 包含系统各模块的配置参数

## 主要功能流程

1. **初始化**:
   - 加载YOLO V8模型
   - 初始化摄像头
   - 初始化舵机控制器
   - 启动Web服务器

2. **检测流程**:
   - 从摄像头获取图像
   - 预处理图像
   - 使用YOLO V8模型进行推理
   - 后处理检测结果

3. **追踪流程**:
   - 计算检测目标与画面中心的偏移
   - 使用PID控制器计算舵机角度调整量
   - 通过串口发送舵机控制命令

4. **Web交互**:
   - 显示实时视频流和检测结果
   - 提供舵机手动控制界面
   - 提供自动追踪开关

## 配置参数说明

主要配置参数位于`src/config/settings.py`文件中，包括：

- **MODEL_CONFIG**: 模型相关配置
- **PID_CONFIG**: PID控制器配置
- **SERVO_TRACKING_CONFIG**: 舵机追踪配置
- **TARGET_TRACKING_CONFIG**: 目标跟踪配置
- **SERVO_HARDWARE_CONFIG**: 舵机硬件配置
- **KALMAN_FILTER_CONFIG**: 卡尔曼滤波器配置
- **FLASK_CONFIG**: Flask服务器配置
- **DETECTION_CONFIG**: 检测相关配置

## 使用方法

1. 安装依赖:
   ```bash
   pip install -r requirements.txt
   ```

2. 运行系统:

   a. 使用启动脚本（推荐）:
   ```bash
   ./start.sh [选项]
   ```

   b. 直接使用Python命令:
   ```bash
   python src/main.py [参数]
   ```

3. 访问Web界面:
   ```
   http://<设备IP>:5000
   ```

### 启动脚本选项

```
-h, --help            显示帮助信息
-n, --no-servo        禁用舵机控制功能
-p, --port PORT       指定Arduino串口设备路径 (默认: /dev/ttyS1)
-b, --baudrate RATE   指定串口波特率 (默认: 115200)
-c, --conf CONF       指定置信度阈值 (默认: 0.6)
-i, --iou IOU         指定IoU阈值 (默认: 0.45)
```
