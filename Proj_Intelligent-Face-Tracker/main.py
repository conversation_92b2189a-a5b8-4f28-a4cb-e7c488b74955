import cv2
import argparse
import logging
import os
from src.config.settings import LOGGING_CONFIG, MODEL_CONFIG, SERIAL_CONFIG, CAMERA_CONFIG
from src.detection_core.yolo_detect import YOLO11_Detect
from src.web.app import WebApp

# 配置日志
logging.basicConfig(**LOGGING_CONFIG)
logger = logging.getLogger("RDK_YOLO")

def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('--model-path', type=str, 
                       default=MODEL_CONFIG['default_model_path'],
                       help="""BPU量化*.bin模型的路径。
                           RDK X3(模块): Bernoulli2。
                           RDK Ultra: Bayes。
                           RDK X5(模块): Bayes-e。
                           RDK S100: Nash-e。
                           RDK S100P: Nash-m。""")
    parser.add_argument('--test-img', type=str, 
                       default=MODEL_CONFIG['default_test_img'],
                       help='加载测试图像的路径。')
    parser.add_argument('--img-save-path', type=str,
                       default=MODEL_CONFIG['default_img_save_path'],
                       help='保存测试图像的路径。')
    parser.add_argument('--classes-num', type=int,
                       default=MODEL_CONFIG['default_classes_num'],
                       help='检测的类别数量。')
    parser.add_argument('--reg', type=int,
                       default=MODEL_CONFIG['default_reg'],
                       help='DFL回归层。')
    parser.add_argument('--iou-thres', type=float,
                       default=MODEL_CONFIG['default_iou_thres'],
                       help='IoU阈值。')
    parser.add_argument('--conf-thres', type=float,
                       default=MODEL_CONFIG['default_conf_thres'],
                       help='置信度阈值。')
    parser.add_argument('--serial-port', type=str,
                       default=SERIAL_CONFIG['port'],
                       help='Arduino串口设备路径，默认为/dev/ttyS1。')
    parser.add_argument('--baudrate', type=int,
                       default=115200,
                       help='串口波特率。')
    parser.add_argument('--no-servo', action='store_true',
                       help='禁用舵机控制功能。')
    # 摄像头参数，使用settings.py中的默认值
    parser.add_argument('--camera-path', type=str,
                       default=CAMERA_CONFIG['default_camera_path'],
                       help='摄像头设备路径，例如 /dev/video0。')
    parser.add_argument('--width', type=int,
                       default=CAMERA_CONFIG['width'],
                       help='视频帧宽度。')
    parser.add_argument('--height', type=int,
                       default=CAMERA_CONFIG['height'],
                       help='视频帧高度。')
    return parser.parse_args()

def setup_camera(camera_path=CAMERA_CONFIG['default_camera_path'], width=CAMERA_CONFIG['width'], height=CAMERA_CONFIG['height']):
    # 优先使用camera_path
    if not camera_path:
        logger.error(f"❌ 未提供有效的摄像头路径!")
        exit(1)
        
    cap = cv2.VideoCapture(camera_path)

    if not cap.isOpened():
        logger.error(f"❌ 无法打开摄像头: {camera_path}")
        exit(1)
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
    return cap

def main():
    # 解析命令行参数
    opt = parse_args()
    logger.info(opt)

    try:
        # 初始化模型
        model = YOLO11_Detect(opt.model_path, opt.conf_thres, opt.iou_thres)
        
        # 设置摄像头
        camera = setup_camera(opt.camera_path, opt.width, opt.height)
        
        # 创建并运行Web应用
        app = WebApp(model, camera, opt.serial_port, opt.baudrate, disable_servo=opt.no_servo)
        # 注意：舵机控制器在WebApp中初始化
        app.run()
    
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序出错: {str(e)}")
    finally:
        # 清理资源
        if 'camera' in locals():
            camera.release()
        cv2.destroyAllWindows()

if __name__ == "__main__":
    main() 