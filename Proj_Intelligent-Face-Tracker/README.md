# RDK YOLO V8 人脸检测与舵机控制系统

## 项目概述

本项目是基于RDKX5平台的YOLO V8目标检测系统，主要实现了人脸检测和舵机控制功能。系统通过摄像头实时捕获视频流，使用YOLO V8模型进行人脸检测，并可以通过舵机控制云台实现对检测目标的自动跟踪。

## 系统架构

项目采用模块化设计，主要包含以下组件：

1. **模型推理模块**：使用YOLO V8模型进行人脸检测
2. **Web界面模块**：提供用户交互界面，显示检测结果和控制舵机
3. **舵机控制模块**：通过串口与Arduino通信，控制云台舵机运动
4. **坐标计算模块**：计算检测目标与画面中心的偏移，用于自动跟踪

### 目录结构

```
├── docs/                       # 文档目录
│   ├── 人脸追踪逻辑流程.drawio    # 人脸追踪逻辑流程图
│   └── 项目结构说明.md           # 项目结构详细说明
│
├── model/                      # 模型目录
│   └── yolo11n_face_detect_bayese_320x320_nv12.bin  # YOLO V8人脸检测模型
│
├── pic/                        # 图片资源目录
│   └── 周杰伦.jpg               # 测试图片
│
├── src/                        # 源代码目录
│   ├── config/                 # 配置文件目录
│   │   └── settings.py         # 系统配置参数
│   │
│   ├── detection_core/         # 检测核心模块
│   │   ├── base_model.py       # 模型基类
│   │   └── yolo_detect.py      # YOLO检测实现
│   │
│   ├── utils/                  # 工具函数目录
│   │   ├── coordinate.py       # 坐标计算
│   │   ├── kalman_filter.py    # 卡尔曼滤波器
│   │   ├── pid_controller.py   # PID控制器
│   │   ├── serial_protocol.py  # 串口通信协议
│   │   ├── servo_controller.py # 舵机控制器
│   │   ├── servo_protocol.py   # 舵机通信协议
│   │   └── servo_tracking.py   # 舵机追踪
│   │
│   ├── web/                    # Web应用模块
│   │   ├── app.py              # Flask应用
│   │   ├── static/             # 静态资源
│   │   └── templates/          # HTML模板
│   │
│
├── main.py                     # 主程序入口
├── README.md                   # 项目说明
├── requirements.txt            # 依赖列表
└── version.md                  # 版本历史
```

## 功能特性

1. **实时人脸检测**：使用YOLO V8模型进行实时人脸检测
2. **Web界面控制**：通过浏览器访问系统，查看检测结果和控制舵机
3. **舵机云台控制**：支持手动和自动两种控制模式
4. **自动目标跟踪**：可以自动调整舵机位置，使检测到的人脸保持在画面中心
5. **参数可配置**：支持通过命令行参数或配置文件调整系统参数

## 技术实现

### 模型推理

- 使用`hobot_dnn`库加载和推理BPU量化模型
- 支持多种RDK硬件平台：X3、Ultra、X5、S100、S100P
- 模型输入尺寸为320x320，支持NV12格式输入

### 舵机控制

- 通过串口与Arduino通信，使用自定义协议控制舵机
- 支持水平（左右）和垂直（上下）两个方向的舵机控制
- 实现了PID控制算法，使目标跟踪更加平滑

### Web界面

- 使用Flask框架构建Web应用
- 提供实时视频流显示和舵机控制界面
- 支持通过滑块手动控制舵机位置
- 提供自动跟踪功能开关

## 使用方法

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行系统

#### 使用启动脚本（推荐）

```bash
# 基本使用
./start.sh

# 显示帮助信息
./start.sh --help

# 禁用舵机控制
./start.sh --no-servo

# 自定义参数
./start.sh --port /dev/ttyS0 --baudrate 9600 --conf 0.5 --iou 0.4
```

#### 直接使用Python命令

```bash
python main.py [参数]
```

### 命令行参数

#### 模型相关参数
- `--model-path`：BPU量化*.bin模型的路径

#### 图像处理参数
- `--test-img`：加载测试图像的路径
- `--img-save-path`：保存测试图像的路径
- `--classes-num`：检测的类别数量
- `--reg`：DFL回归层
- `--iou-thres`：IoU阈值，用于非极大值抑制
- `--conf-thres`：置信度阈值，用于过滤检测结果

#### 硬件控制参数
- `--serial-port`：Arduino串口设备路径，默认为/dev/ttyS1
- `--baudrate`：串口波特率，默认为115200
- `--no-servo`：禁用舵机控制功能，设置此参数将不使用舵机

#### 摄像头参数
- `--camera-path`: 指定摄像头设备路径, 例如 `/dev/video0`
- `--width`: 设置摄像头捕捉画面的宽度
- `--height`: 设置摄像头捕捉画面的高度

#### 使用示例
```bash
# 基本使用
python main.py --model-path model/yolo11n_face_detect_bayese_320x320_nv12.bin

# 自定义参数
python main.py \
    --model-path model/yolo11n_face_detect_bayese_320x320_nv12.bin \
    --conf-thres 0.5 \
    --iou-thres 0.45 \
    --serial-port /dev/ttyS1 \
    --baudrate 115200

# 禁用舵机控制
python main.py --model-path model/yolo11n_face_detect_bayese_320x320_nv12.bin --no-servo
```

### 访问Web界面

系统启动后，通过浏览器访问：`http://<设备IP>:5000`

## 开发与扩展

- 可以通过修改`src/detection_core/yolo_detect.py`支持其他目标检测模型
- 可以通过修改`src/config/settings.py`调整系统参数
- 可以通过修改`src/web/templates/`目录下的HTML文件自定义Web界面
- 更多详细信息请参考`docs/项目结构说明.md`
