import logging

# 日志配置
LOGGING_CONFIG = {
    'level': logging.INFO,
    'format': '[%(name)s] [%(asctime)s.%(msecs)03d] [%(levelname)s] %(message)s',
    'datefmt': '%H:%M:%S'
}

# 模型配置
MODEL_CONFIG = {
    'default_model_path': 'model/yolo11n_face_detect_bayese_320x320_nv12.bin',  # 默认模型文件路径
    'default_test_img': '../pic/周杰伦.jpg',        # 默认测试图片路径
    'default_img_save_path': 'output.jpg',          # 默认检测结果图片保存路径
    'default_classes_num': 1,                       # 默认检测类别数（本例为人脸检测，类别为1）
    'default_reg': 16,                              # DFL分布回归的bin数（分布式回归，决定边界框精度）
    'default_iou_thres': 0.45,                      # 默认IoU阈值（用于NMS筛选重叠框）
    'default_conf_thres': 0.6,                      # 默认置信度阈值（低于此值的检测框会被过滤）
    'input_image_size': 320,                        # 输入图像尺寸（模型输入要求的宽高，正方形）
    'dfl_bins': 16,                                 # DFL分布回归的bin数（与default_reg一致，便于统一管理）
    'anchor_shapes': [                              # 各特征层anchor参数（size为特征图尺寸，stride为步长）
        {'size': 40, 'stride': 8},                  # 第一层特征图，40x40，步长8
        {'size': 20, 'stride': 16},                 # 第二层特征图，20x20，步长16
        {'size': 10, 'stride': 32}                  # 第三层特征图，10x10，步长32
    ]
}

# 摄像头配置
CAMERA_CONFIG = {
    'default_camera_path': '/dev/video0', # 默认摄像头设备路径
    'width': 640,            # 默认帧宽度
    'height': 480,           # 默认帧高度
}

# Flask配置
FLASK_CONFIG = {
    'host': '0.0.0.0',
    'port': 5000,
    'debug': False
}

# 检测配置
DETECTION_CONFIG = {
    'colors': [
        (56, 56, 255)
    ],
    'class_names': ["face"]
}

# PID控制器配置
PID_CONFIG = {
    'pan': {
        'kp': 0.6,   # 降低比例系数，减少过冲
        'ki': 0.0,     # 禁用积分作用
        'kd': 0.0,    # 适中的微分系数
        'smoothing_factor': 0.9,
        'max_integral': 20.0,  # 积分上限
        'first_update_factor': 0.3,  # 首次更新的缩减因子，降低为0.3
        'output_limit': 100,  # PID输出限幅
        'integral_error_threshold': 10  # 积分增益减小的误差阈值
    },
    'tilt': {
        'kp': 0.6,   # 降低比例系数，减少过冲
        'ki': 0.0,     # 禁用积分作用
        'kd': 0.0,    # 适中的微分系数
        'smoothing_factor': 0.9,
        'max_integral': 20.0,  # 积分上限
        'first_update_factor': 0.3,  # 首次更新的缩减因子，降低为0.3
        'output_limit': 100,  # PID输出限幅
        'integral_error_threshold': 10  # 积分增益减小的误差阈值
    }
}

# 卡尔曼滤波器配置
KALMAN_FILTER_CONFIG = {
    'dt': 0.1,                      # 时间步长（秒）
    'process_noise_scale': 0.01,    # 过程噪声系数
    'measurement_noise_scale': 0.1, # 测量噪声系数
    'prediction_time_factor': 1.5,  # 预测时间系数，用于动态调整预测时间
    'velocity_threshold': 50,       # 速度阈值，超过此值认为是快速运动
    'acceleration_enabled': True,   # 是否考虑加速度（二阶模型）
    'adaptive_noise': True          # 是否启用自适应噪声调整
}

# 串口配置
SERIAL_CONFIG = {
    'port': '/dev/ttyS1',      # 串口端口号
    'baudrate': 115200         # 串口波特率
}


# 舵机硬件配置
SERVO_HARDWARE_CONFIG = {
    'pan_pin': 3,   # 水平舵机引脚 (左右)
    'tilt_pin': 4,  # 垂直舵机引脚 (上下)
    'supported_pins': [2, 3, 4, 6, 44, 45, 46, 7, 10], # 协议支持的所有舵机引脚列表
    'init_pan_angle': 135.0,   # 舵机水平初始/中心角度 (改为 135)
    'init_tilt_angle': 135.0,  # 舵机垂直初始/中心角度 (改为 135)
    'min_pan': 0.0,           # 水平最小角度
    'max_pan': 270.0,          # 水平最大角度
    'min_tilt': 0.0,          # 垂直最小角度
    'max_tilt': 270.0         # 垂直最大角度
}


# 舵机追踪配置
SERVO_TRACKING_CONFIG = {
    'face_timeout': 0.8,            # 人脸超时时间（秒）
    'movement_threshold': 3,        # 移动阈值
    'angle_adjust_factor': 0.05,    # 角度调整系数，略微降低以减缓反应
    'min_servo_interval': 0.01,     # 最小时间间隔，优化为更短的间隔
    'distance_threshold': 80,       # 距离阈值
    'sleep_interval': 0.03,         # 线程循环等待时间
    'first_detection_factor': 0.25, # 首次检测的调整因子，降低为0.25
    'max_angle_change': 8.0,        # 单次最大角度变化限制，减小为8度
    'center_deadzone': 60,          # 中心死区半径，当目标与中心点距离小于此值时不进行追踪
    'prediction_enabled': True,     # 是否启用运动预测
    'prediction_time': 0.2,         # 预测未来时间（秒）
    'prediction_weight': 0.7,       # 预测位置权重，控制预测位置与当前位置的融合比例
    'max_prediction_distance': 200  # 最大预测距离（像素），超过此距离则降低预测权重
}

# 目标跟踪配置
TARGET_TRACKING_CONFIG = {
    # 评分权重配置
    'confidence_weight': 0.25,       # 置信度权重
    'size_weight': 0.75,             # 面积权重
    'persistence_weight': 0.2,      # 持久性权重
    'position_weight': 0.3,         # 位置连续性权重

    # 目标跟踪参数
    'target_switch_threshold': 0.2, # 目标切换阈值，新目标评分需高于此阈值才会切换
    'target_timeout': 1.0,           # 目标超时时间（秒）
    'max_history_count': 10,         # 历史记录最大计数
    'max_position_distance': 100,    # 最大匹配距离（像素）

    # 方向判断参数
    'direction_threshold': 10        # 方向判断阈值（像素）
}




