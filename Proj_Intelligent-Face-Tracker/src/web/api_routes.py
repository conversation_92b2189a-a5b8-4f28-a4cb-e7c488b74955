import time
import json
import logging
import numpy as np
from flask import request, jsonify, Response, stream_with_context
from src.config.settings import SERVO_HARDWARE_CONFIG

# 这个文件需要访问 WebApp 实例的状态和控制器
# 我们通过一个注册函数来传递 app 和 web_app 实例

logger = logging.getLogger("RDK_YOLO_API")

def register_api_routes(app, web_app_instance):
    """注册所有用于远程控制的 API 路由"""
    
    servo_controller = web_app_instance.servo_controller
    state_lock = web_app_instance.state_lock
    disable_servo = web_app_instance.disable_servo
    _update_servo_angle = web_app_instance._update_servo_angle # 引用内部方法
    coord_calculator = web_app_instance.coord_calculator # toggle_tracking 需要

    @app.route('/move_servo', methods=['POST'])
    def move_servo():
        if disable_servo or servo_controller is None:
            return jsonify({'success': False, 'message': '舵机控制功能已禁用'})
            
        try:
            data = request.json
            pin = int(data.get('pin'))
            angle = float(data.get('angle'))
            
            if pin not in [SERVO_HARDWARE_CONFIG['pan_pin'], SERVO_HARDWARE_CONFIG['tilt_pin']]:
                return jsonify({'success': False, 'message': '无效的舵机引脚'})
            
            if not servo_controller.connected:
                return jsonify({'success': False, 'message': '舵机控制器未连接'})
            
            logger.info(f"API: 绝对移动舵机: 引脚={pin}, 角度={angle}")
            axis = 'pan' if pin == SERVO_HARDWARE_CONFIG['pan_pin'] else 'tilt'
            _update_servo_angle(axis, angle)
            # 使用 web_app_instance 访问状态
            actual_angle = web_app_instance.current_pan_angle if axis == 'pan' else web_app_instance.current_tilt_angle
            
            success = servo_controller.move_servos(pin, actual_angle)
            
            if success:
                return jsonify({'success': True, 'message': f'舵机 {pin} ({axis}) 已移动到 {actual_angle:.1f} 度'})
            else:
                return jsonify({'success': False, 'message': '舵机控制失败'})
        
        except Exception as e:
            logger.error(f"API /move_servo 错误: {str(e)}")
            return jsonify({'success': False, 'message': f'错误: {str(e)}'})
            
    @app.route('/get_servo_angles', methods=['GET'])
    def get_servo_angles():
        if disable_servo:
             return jsonify({'success': False, 'message': '舵机控制功能已禁用'})
             
        with state_lock:
            angles = {
                'pan': round(web_app_instance.current_pan_angle, 1),
                'tilt': round(web_app_instance.current_tilt_angle, 1)
            }
        return jsonify({'success': True, 'angles': angles})
        
    @app.route('/move_servo_relative', methods=['POST'])
    def move_servo_relative():
        if disable_servo or servo_controller is None:
            return jsonify({'success': False, 'message': '舵机控制功能已禁用'})
            
        try:
            data = request.json
            axis = data.get('axis')
            delta = float(data.get('delta'))
            
            if axis not in ['pan', 'tilt']:
                return jsonify({'success': False, 'message': '无效的舵机轴 (需要 "pan" 或 "tilt")'})
            
            if not servo_controller.connected:
                return jsonify({'success': False, 'message': '舵机控制器未连接'})
                
            with state_lock:
                current_angle = web_app_instance.current_pan_angle if axis == 'pan' else web_app_instance.current_tilt_angle
                
            new_angle = current_angle + delta
            pin = SERVO_HARDWARE_CONFIG['pan_pin'] if axis == 'pan' else SERVO_HARDWARE_CONFIG['tilt_pin']
            
            logger.info(f"API: 相对移动舵机: 轴={axis}, 变化量={delta}, 当前={current_angle:.1f}, 目标={new_angle:.1f}")
            
            _update_servo_angle(axis, new_angle)
            actual_angle = web_app_instance.current_pan_angle if axis == 'pan' else web_app_instance.current_tilt_angle
            
            success = servo_controller.move_servos(pin, actual_angle)
            
            if success:
                return jsonify({'success': True, 'message': f'{axis.capitalize()} 舵机移动 {delta:+.1f} 度到 {actual_angle:.1f} 度'})
            else:
                return jsonify({'success': False, 'message': '舵机控制失败'})

        except Exception as e:
            logger.error(f"API /move_servo_relative 错误: {str(e)}")
            return jsonify({'success': False, 'message': f'错误: {str(e)}'})
            
    @app.route('/get_detection_status', methods=['GET'])
    def get_detection_status():
        """
        (旧接口，保留兼容性) 获取最新的人脸检测状态 (仅布尔值)
        建议使用 /get_face_count 获取更详细信息
        """
        with state_lock:
            status = web_app_instance.last_detection_result.copy()
        face_detected = status.get('face_count', 0) > 0 # 从数量推断布尔值
        timestamp = status.get('timestamp', 0)
        is_fresh = (time.time() - timestamp) < 5.0
        return jsonify({
            'success': True, 
            'detection_status': {
                'face_detected': face_detected, 
                'timestamp': timestamp,
                'is_fresh': is_fresh
            }
        })
        
    @app.route('/get_face_count', methods=['GET'])
    def get_face_count():
        """获取当前画面中检测到的人脸数量"""
        with state_lock:
            result = web_app_instance.last_detection_result.copy()
        
        face_count = result.get('face_count', 0)
        timestamp = result.get('timestamp', 0)
        is_fresh = (time.time() - timestamp) < 5.0 # 5秒内算新鲜
        
        return jsonify({
            'success': True,
            'face_count': face_count,
            'face_detected': face_count > 0, # 方便直接判断
            'timestamp': timestamp,
            'is_fresh': is_fresh
        })
        
    @app.route('/reset_position', methods=['POST'])
    def reset_position():
        if disable_servo or servo_controller is None:
            return jsonify({'success': False, 'message': '舵机控制功能已禁用'})
            
        try:
            logger.info("API: 重置舵机位置")
            pan_pin = SERVO_HARDWARE_CONFIG['pan_pin']
            tilt_pin = SERVO_HARDWARE_CONFIG['tilt_pin']
            reset_pan = SERVO_HARDWARE_CONFIG.get('init_pan_angle', 135.0)
            reset_tilt = SERVO_HARDWARE_CONFIG.get('init_tilt_angle', 135.0)
            
            _update_servo_angle('pan', reset_pan)
            servo_controller.move_servos(pan_pin, web_app_instance.current_pan_angle)
            time.sleep(0.1)
            _update_servo_angle('tilt', reset_tilt)
            servo_controller.move_servos(tilt_pin, web_app_instance.current_tilt_angle)
            
            return jsonify({'success': True, 'message': f'舵机位置已重置为 Pan={web_app_instance.current_pan_angle:.1f}, Tilt={web_app_instance.current_tilt_angle:.1f}'})
        
        except Exception as e:
            logger.error(f"API /reset_position 错误: {str(e)}")
            return jsonify({'success': False, 'message': f'错误: {str(e)}'})
            
    @app.route('/send_test_data', methods=['GET'])
    def send_test_data(): # 保持为 GET，SSE 通常用 GET
        if disable_servo or servo_controller is None:
            error_data = json.dumps({"type": "status", "status": "error", "message": "舵机控制功能已禁用", "success": False})
            return Response(f"data: {error_data}\n\n", mimetype='text/event-stream')
        
        def stream_generator():
            def sse_callback(**kwargs):
                if 'pan' in kwargs and 'tilt' in kwargs:
                    _update_servo_angle('pan', kwargs['pan'])
                    _update_servo_angle('tilt', kwargs['tilt'])
                    data = json.dumps({
                        "type": "update", 
                        "pan": round(web_app_instance.current_pan_angle, 1),
                        "tilt": round(web_app_instance.current_tilt_angle, 1)
                    })
                elif 'status' in kwargs:
                    data = json.dumps({
                        "type": "status", 
                        "status": kwargs.get('status'), 
                        "message": kwargs.get('message'), 
                        "success": kwargs.get('success')
                    })
                else:
                    return None
                return f"data: {data}\n\n"
            
            try:
                logger.info("API: 开始流式传输云台自检更新")
                for update_data in servo_controller.send_test_data(update_callback=sse_callback):
                    if update_data:
                        yield update_data
                logger.info("API: 云台自检流式传输完成")
            except Exception as e:
                logger.error(f"API /send_test_data 流式传输错误: {str(e)}")
                error_data = json.dumps({"type": "status", "status": "error", "message": f"服务器内部错误: {str(e)}", "success": False})
                yield f"data: {error_data}\n\n"
        
        return Response(stream_with_context(stream_generator()), mimetype='text/event-stream')
            
    @app.route('/toggle_tracking', methods=['POST'])
    def toggle_tracking():
        if disable_servo or servo_controller is None:
            return jsonify({'success': False, 'message': '舵机控制功能已禁用'})
        
        try:
            data = request.json
            enabled_req = data.get('enabled') # 请求的状态 (True/False/None)
            
            with state_lock:
                current_enabled_state = web_app_instance.auto_tracking_enabled
            
            # 确定目标状态
            if enabled_req is None: # 如果请求未指定，则切换状态
                target_enabled_state = not current_enabled_state
            else: # 如果请求指定了状态
                target_enabled_state = bool(enabled_req)
                
            # 检查是否需要改变状态
            if target_enabled_state == current_enabled_state:
                status_str = "已启动" if current_enabled_state else "已停止"
                logger.warning(f"API: 自动跟踪已经是 {status_str} 状态，无需操作")
                return jsonify({'success': True, 'message': f'自动跟踪{status_str}', 'state': current_enabled_state})
                
            # 执行状态切换
            if target_enabled_state:
                logger.info("API: 请求启动人脸自动跟踪")
                success = servo_controller.start_auto_tracking(coord_calculator)
                if success:
                    with state_lock:
                        web_app_instance.auto_tracking_enabled = True
                    return jsonify({'success': True, 'message': '自动跟踪已启动', 'state': True})
                else:
                    return jsonify({'success': False, 'message': '启动自动跟踪失败', 'state': False})
            else:
                logger.info("API: 请求停止人脸自动跟踪")
                success = servo_controller.stop_auto_tracking()
                if success:
                    with state_lock:
                        web_app_instance.auto_tracking_enabled = False
                    return jsonify({'success': True, 'message': '自动跟踪已停止', 'state': False})
                else:
                    return jsonify({'success': False, 'message': '停止自动跟踪失败', 'state': True}) 
        
        except Exception as e:
            logger.error(f"API /toggle_tracking 错误: {str(e)}")
            with state_lock:
                current_state = web_app_instance.auto_tracking_enabled
            return jsonify({'success': False, 'message': f'错误: {str(e)}', 'state': current_state})

    logger.info("API 路由已准备好注册") 