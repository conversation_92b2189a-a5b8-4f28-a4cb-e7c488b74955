/* 基本样式 */
body {
    font-family: "Microsoft YaHei", <PERSON><PERSON>, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f7fa;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
}

h1 {
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
}

.video-container {
    margin-bottom: 20px;
    width: 100%;
    text-align: center;
}

img {
    max-width: 100%;
    border: 2px solid #333;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.content-wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
    width: 100%;
}

.controls {
    flex: 1;
    min-width: 300px;
    max-width: 100%;
}

.servo-display {
    flex: 1;
    min-width: 300px;
    max-width: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.control-group {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.control-group h3 {
    margin-top: 0;
    color: #2c3e50;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.slider-container {
    margin: 15px 0;
}

.slider-container label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.slider-container .slider-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.slider-value {
    font-weight: bold;
    color: #2196F3;
}

input[type="range"] {
    width: 100%;
    height: 10px;
    -webkit-appearance: none;
    appearance: none;
    background: #ddd;
    outline: none;
    border-radius: 5px;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: #2196F3;
    cursor: pointer;
    border-radius: 50%;
}

.buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 15px;
}

button {
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    background-color: #4CAF50;
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #45a049;
}

button.primary {
    background-color: #2196F3;
}

button.primary:hover {
    background-color: #0b7dda;
}

button.danger {
    background-color: #f44336;
}

button.danger:hover {
    background-color: #d32f2f;
}

/* 状态反馈动画 */
.status {
    width: 100%;
    padding: 15px;
    border-radius: 4px;
    background-color: #e0e0e0;
    text-align: center;
    font-weight: bold;
    margin-top: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.status::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, transparent, #2196F3, transparent);
    animation: loading 2s linear infinite;
    opacity: 0;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

.status.success-animation {
    background-color: rgba(76, 175, 80, 0.2);
    color: #2e7d32;
    animation: pulse 1s ease;
}

.status.error-animation {
    background-color: rgba(244, 67, 54, 0.2);
    color: #c62828;
    animation: shake 0.5s ease;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    20%, 60% { transform: translateX(-5px); }
    40%, 80% { transform: translateX(5px); }
}

/* 虚拟舵机样式 - 适配Three.js */
.servo-visual {
    width: 300px;
    height: 300px;
    position: relative;
    background: linear-gradient(145deg, #e6e9f0, #eef1f5);
    border-radius: 10px;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

#3d-container {
    width: 100% !important;
    height: 100% !important;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
}

canvas {
    display: block !important;
    width: 100% !important;
    height: 100% !important;
    outline: none;
}

.scene {
    width: 200px;
    height: 200px;
    position: relative;
    transform-style: preserve-3d;
    transform: rotateX(35deg);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: rotateX(35deg) translateY(0); }
    50% { transform: rotateX(35deg) translateY(-5px); }
}

.base {
    width: 120px;
    height: 120px;
    background: linear-gradient(145deg, #363636, #2a2a2a);
    border-radius: 60px;
    position: absolute;
    left: 50%;
    top: 60%;
    transform: translate(-50%, -50%);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3), inset 0 2px 5px rgba(255,255,255,0.1);
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 4px solid #444;
    overflow: hidden;
}

.base::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 10px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: scan 3s linear infinite;
}

@keyframes scan {
    0% { transform: translateY(-50px) rotate(0deg); }
    100% { transform: translateY(50px) rotate(360deg); }
}

.platform {
    width: 90px;
    height: 90px;
    background: linear-gradient(145deg, #444, #333);
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transform-style: preserve-3d;
    z-index: 2;
    box-shadow: 0 3px 10px rgba(0,0,0,0.2), inset 0 1px 3px rgba(255,255,255,0.1);
}

.platform::before {
    content: '';
    position: absolute;
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, #555, #444);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-shadow: inset 0 0 5px rgba(0,0,0,0.5);
}

.pan-axis {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    transform-style: preserve-3d;
    transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.tilt-housing {
    width: 70px;
    height: 70px;
    background: linear-gradient(145deg, #2196F3, #1976D2);
    border-radius: 10px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transform-style: preserve-3d;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3), inset 0 1px 3px rgba(255,255,255,0.2);
}

.tilt-housing::before {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    background: #0d47a1;
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-shadow: inset 0 0 5px rgba(0,0,0,0.5);
}

.tilt-axis {
    width: 100%;
    height: 100%;
    position: absolute;
    transform-style: preserve-3d;
    transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.camera-body {
    width: 50px;
    height: 40px;
    background: linear-gradient(145deg, #333, #222);
    border-radius: 5px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) translateZ(15px) rotateX(-90deg);
    transform-style: preserve-3d;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    z-index: 4;
    border: 1px solid #444;
}

.camera-body::before {
    content: '';
    position: absolute;
    width: 8px;
    height: 8px;
    background: #f44336;
    border-radius: 50%;
    top: 5px;
    right: 5px;
    box-shadow: 0 0 5px #f44336;
    animation: blink 2s infinite;
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.camera-lens {
    width: 25px;
    height: 25px;
    background: radial-gradient(circle, #222, #000);
    border: 2px solid #444;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) translateZ(5px);
    box-shadow: inset 0 0 10px rgba(0,0,0,0.8), 0 0 5px rgba(0,0,0,0.5);
    overflow: hidden;
}

.lens-reflection {
    width: 10px;
    height: 10px;
    background: radial-gradient(circle, rgba(255,255,255,0.8), transparent);
    border-radius: 50%;
    position: absolute;
    top: 25%;
    left: 25%;
    animation: move-reflection 4s ease-in-out infinite;
}

@keyframes move-reflection {
    0%, 100% { transform: translate(0, 0); }
    25% { transform: translate(2px, 2px); }
    50% { transform: translate(0, 4px); }
    75% { transform: translate(-2px, 2px); }
}

.pan-indicator {
    width: 140px;
    height: 4px;
    background: linear-gradient(90deg, rgba(244, 67, 54, 0.3), rgba(244, 67, 54, 0.7), rgba(244, 67, 54, 0.3));
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    border-radius: 2px;
    pointer-events: none;
    box-shadow: 0 0 5px rgba(244, 67, 54, 0.5);
}

.tilt-indicator {
    width: 4px;
    height: 70px;
    background: linear-gradient(180deg, rgba(33, 150, 243, 0.3), rgba(33, 150, 243, 0.7), rgba(33, 150, 243, 0.3));
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9;
    border-radius: 2px;
    pointer-events: none;
    box-shadow: 0 0 5px rgba(33, 150, 243, 0.5);
}

.control-dot {
    width: 12px;
    height: 12px;
    background: radial-gradient(circle, #fff, #f5f5f5);
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 12;
    box-shadow: 0 0 10px rgba(0,0,0,0.5), inset 0 0 2px rgba(0,0,0,0.3);
    pointer-events: none;
}

.angle-info {
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 20;
    box-shadow: 0 3px 10px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255,255,255,0.1);
    pointer-events: none;
}

.angle-info:hover {
    background: rgba(33, 150, 243, 0.8);
    transform: translateX(-50%) translateY(-3px);
}

/* 添加响应式设计 */
@media (max-width: 768px) {
    .content-wrapper {
        flex-direction: column;
    }
    
    .controls, .servo-display {
        max-width: 100%;
    }
    
    .servo-visual {
        width: 250px;
        height: 250px;
    }
}

/* 添加触摸设备支持 */
@media (hover: none) {
    .preset-button:hover {
        display: none;
    }
    
    .preset-button:active {
        display: none;
    }
}

/* 提示样式 */
.tooltip {
    font-size: 12px;
    color: #666;
    font-weight: normal;
}

.control-tips {
    background-color: rgba(33, 150, 243, 0.1);
    border-left: 3px solid #2196F3;
    padding: 10px 15px;
    border-radius: 4px;
    margin-top: 15px;
    font-size: 14px;
    color: #555;
}

.control-tips p {
    margin: 0;
}

/* 添加焦点样式 */
.servo-visual:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.5), inset 0 0 50px rgba(0,0,0,0.1), 0 10px 20px rgba(0,0,0,0.1);
}

/* 删除预设按钮相关样式 */
.preset-buttons {
    display: none;
}

.preset-button {
    display: none;
}

.preset-button::after {
    display: none;
}

.preset-button:hover {
    display: none;
}

.preset-button:hover::after {
    display: none;
}

.preset-button:active {
    display: none;
} 