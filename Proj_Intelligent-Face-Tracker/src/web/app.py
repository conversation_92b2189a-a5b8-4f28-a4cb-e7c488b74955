from flask import Flask, Response, request, jsonify, render_template, stream_with_context
import cv2
import logging
import time
import json
from src.config.settings import FLASK_CONFIG, SERVO_HARDWARE_CONFIG
from src.detection_core.yolo_detect import draw_detection
from src.utils.coordinate import CoordinateCalculator
from src.utils.servo_controller import ServoController
import numpy as np
import socket
from threading import Lock # 新增 Lock 用于线程安全
# 导入新的路由注册函数
from .api_routes import register_api_routes

logger = logging.getLogger("RDK_YOLO")

class WebApp:
    def __init__(self, model, camera, serial_port='/dev/ttyS0', baudrate=115200, disable_servo=False):
        self.model = model
        self.camera = camera
        self.app = Flask(__name__)
        # 获取摄像头分辨率
        frame_width = int(self.camera.get(cv2.CAP_PROP_FRAME_WIDTH))
        frame_height = int(self.camera.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        # 初始化坐标计算器
        self.coord_calculator = CoordinateCalculator(frame_width, frame_height)
        
        # 舵机控制标志
        self.disable_servo = disable_servo
        
        # 自动跟踪标志
        self.auto_tracking_enabled = False
        
        # 新增：存储状态的变量和锁
        self.state_lock = Lock()
        # 从配置中读取舵机初始角度，并限制在硬件范围内
        initial_pan = SERVO_HARDWARE_CONFIG.get('init_pan_angle', 135.0)
        initial_tilt = SERVO_HARDWARE_CONFIG.get('init_tilt_angle', 135.0)
        min_pan = SERVO_HARDWARE_CONFIG.get('min_pan', 0.0)
        max_pan = SERVO_HARDWARE_CONFIG.get('max_pan', 270.0)
        min_tilt = SERVO_HARDWARE_CONFIG.get('min_tilt', 0.0)
        max_tilt = SERVO_HARDWARE_CONFIG.get('max_tilt', 270.0)
        self.current_pan_angle = np.clip(initial_pan, min_pan, max_pan)
        self.current_tilt_angle = np.clip(initial_tilt, min_tilt, max_tilt)
        self.last_detection_result = {'face_count': 0, 'timestamp': 0}
        
        # 初始化舵机控制器
        self.servo_controller = None
        if not self.disable_servo:
            logger.info(f"初始化舵机控制器: 串口={serial_port}, 波特率={baudrate}")
            self.servo_controller = ServoController(serial_port, baudrate)
            try:
                if self.servo_controller.connect(): # connect 现在会尝试初始化舵机位置
                    # 连接成功后，从舵机控制器获取实际初始角度 (如果connect成功初始化了)
                    with self.state_lock:
                        self.current_pan_angle = self.servo_controller.current_pan
                        self.current_tilt_angle = self.servo_controller.current_tilt
                    logger.info(f"舵机控制器初始化成功, 当前角度 Pan={self.current_pan_angle:.1f}, Tilt={self.current_tilt_angle:.1f}")
                else:
                     # 如果连接或初始化失败，则禁用舵机
                    logger.error("连接或初始化舵机控制器失败, 将禁用舵机功能。")
                    self.disable_servo = True
            except Exception as e:
                logger.error(f"连接或初始化舵机控制器时发生异常: {str(e)}")
                self.disable_servo = True # 如果连接失败，则禁用舵机
        
        # 设置路由
        self._setup_routes()

    # 新增：线程安全地更新舵机角度
    def _update_servo_angle(self, axis: str, angle: float):
        with self.state_lock:
            if axis == 'pan':
                self.current_pan_angle = np.clip(angle, 
                                                SERVO_HARDWARE_CONFIG.get('min_pan', 0), 
                                                SERVO_HARDWARE_CONFIG.get('max_pan', 270)) # 使用配置的最大值
            elif axis == 'tilt':
                self.current_tilt_angle = np.clip(angle, 
                                                  SERVO_HARDWARE_CONFIG.get('min_tilt', 0), 
                                                  SERVO_HARDWARE_CONFIG.get('max_tilt', 270)) # 使用配置的最大值
            else:
                logger.warning(f"尝试更新无效的舵机轴: {axis}")

    # 修改：线程安全地更新检测结果（人脸数量）
    def _update_detection_result(self, face_count: int):
        with self.state_lock:
            self.last_detection_result = {
                'face_count': face_count,
                'timestamp': time.time()
            }

    def _setup_routes(self):
        @self.app.route('/')
        def index():
            # 根据舵机控制是否禁用，返回不同的模板
            if self.disable_servo:
                 # 确保传递给 disabled.html 的变量是预期的
                 return render_template('disabled.html', 
                                        tracking_state='Disabled', 
                                        pan_angle=self.current_pan_angle, 
                                        tilt_angle=self.current_tilt_angle)
            else:
                # 将舵机引脚号、初始角度和跟踪状态传递给模板
                with self.state_lock: # 读取状态时加锁
                    tracking_state = "ON" if self.auto_tracking_enabled else "OFF"
                    pan_angle = self.current_pan_angle
                    tilt_angle = self.current_tilt_angle
                return render_template(
                    'index.html',
                    pan_pin=SERVO_HARDWARE_CONFIG['pan_pin'], 
                    tilt_pin=SERVO_HARDWARE_CONFIG['tilt_pin'],
                    initial_pan=round(pan_angle, 1),
                    initial_tilt=round(tilt_angle, 1),
                    tracking_state=tracking_state
                )
        
        @self.app.route('/video_feed')
        def video_feed():
            return Response(self._generate_frames(), 
                          mimetype='multipart/x-mixed-replace; boundary=frame')
        
        # --- 注册来自 api_routes.py 的 API 路由 ---
        register_api_routes(self.app, self)

    def _draw_center_and_info(self, frame, bbox, offset_x, offset_y, distance, direction):
        """在图像上绘制中心点和偏移信息"""
        h, w = frame.shape[:2]
        # 绘制屏幕中心点已在_generate_frames中完成，这里不再重复
        
        # 绘制目标中心点
        x1, y1, x2, y2 = bbox
        target_center = ((x1 + x2)//2, (y1 + y2)//2)
        cv2.circle(frame, target_center, 5, (0, 0, 255), -1) # 红色圆点
        
        # 绘制连接线 (从屏幕中心到目标中心)
        cv2.line(frame, (w//2, h//2), target_center, (255, 0, 0), 2) # 蓝色线
        
        # 添加文字信息
        info_text = f"Offset: X={offset_x:+.0f}, Y={offset_y:+.0f}, Dist={distance:.0f}px, Dir={direction}" # 英文标签避免乱码
        cv2.putText(frame, info_text, (10, h-20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2) # 红色文字
        
        # 添加舵机控制状态 (线程安全读取)
        with self.state_lock:
            tracking_state = self.auto_tracking_enabled
            
        if self.disable_servo or self.servo_controller is None:
            status_text = "Servo: Disabled"
        else:
            if tracking_state:
                status_text = "Servo: Enabled (Tracking ON)"
            else:
                status_text = "Servo: Enabled (Tracking OFF)"
        # 统一状态文本颜色为绿色
        cv2.putText(frame, status_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

    def _generate_frames(self):
        while True:
            try:
                success, frame = self.camera.read()
                if not success:
                    logger.error("❌ 无法读取摄像头画面")
                    time.sleep(1) # 等待摄像头恢复
                    continue

                # 进行推理
                input_tensor = self.model.bgr2nv12(frame)
                outputs = self.model.c2numpy(self.model.forward(input_tensor))
                ids, scores, bboxes = self.model.postProcess(outputs)
                
                # 修改：更新人脸检测数量状态
                face_count = len(bboxes)
                self._update_detection_result(face_count)

                # 更新目标信息并获取最优目标
                target_id, target_bbox = self.coord_calculator.update_targets(bboxes, scores)

                # 渲染所有检测结果
                for class_id, score, bbox in zip(ids, scores, bboxes):
                    # 绘制红色的原始检测框和标签
                    draw_detection(frame, bbox, score, class_id)
                    
                    # --- 修改开始 ---
                    # 检查是否为当前跟踪的目标，并且自动跟踪已启用
                    with self.state_lock: # 线程安全地读取跟踪状态
                        tracking_enabled = self.auto_tracking_enabled
                    
                    if tracking_enabled and target_id and target_bbox is not None and np.array_equal(bbox, target_bbox):
                        # 如果自动跟踪已启用，才绘制绿色的追踪框和文字
                        x1, y1, x2, y2 = bbox
                        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2) # 绿色框
                        cv2.putText(frame, "Tracking", (x1, y1-10), 
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2) # 绿色文字
                    # --- 修改结束 ---

                # 绘制屏幕中心点
                h, w = frame.shape[:2]
                cv2.circle(frame, (w//2, h//2), 5, (0, 255, 0), -1) # 绿色中心点

                # 如果有跟踪目标，计算偏移并绘制相关信息 (现在只绘制中心点连线等，不再绘制追踪框本身)
                if target_bbox is not None:
                    # 计算坐标偏移
                    offset_x, offset_y, distance = self.coord_calculator.calculate_offset(target_bbox)
                    direction = self.coord_calculator.get_direction(offset_x, offset_y)
                    
                    # 绘制中心点连线和偏移信息
                    self._draw_center_and_info(frame, target_bbox, offset_x, offset_y, distance, direction)
                else:
                    # 没有检测到目标，只显示舵机控制状态
                    with self.state_lock: # 线程安全读取
                        tracking_state = self.auto_tracking_enabled
                        
                    if self.disable_servo or self.servo_controller is None:
                        status_text = "Servo: Disabled"
                    else:
                        if tracking_state:
                            status_text = "Servo: Enabled (Tracking ON)"
                        else:
                            status_text = "Servo: Enabled (Tracking OFF)"
                    # 统一状态文本颜色为绿色
                    cv2.putText(frame, status_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                    cv2.putText(frame, "No target detected", (10, h-20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2) # 红色文字

                # 降低图像质量和分辨率以减少数据量和延迟
                frame_small = cv2.resize(frame, (640, 480))
                encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 70]
                ret, buffer = cv2.imencode('.jpg', frame_small, encode_param)
                if not ret:
                    logger.error("❌ 无法编码图像")
                    continue

                frame_bytes = buffer.tobytes()
                yield (b'--frame\r\n' b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')

            except Exception as e:
                logger.error(f"❌ 处理帧时发生错误: {str(e)}")
                # 发生错误时短暂暂停，避免日志刷屏
                time.sleep(0.5)
                continue

    def __del__(self):
        # 关闭舵机控制器
        if hasattr(self, 'servo_controller') and self.servo_controller is not None:
            # 停止自动跟踪
            # 线程安全地检查和停止
            with self.state_lock:
                 if self.auto_tracking_enabled:
                     logger.info("WebApp 销毁前停止自动跟踪...")
                     self.servo_controller.stop_auto_tracking()
                     self.auto_tracking_enabled = False
            # 断开连接    
            self.servo_controller.disconnect()
            logger.info("舵机控制器已断开连接")
        # 释放摄像头 (如果需要)
        if hasattr(self, 'camera') and self.camera.isOpened():
            self.camera.release()
            logger.info("摄像头已释放")

    def run(self):
        try:
            # 从配置获取 host, port 和 debug 状态
            host = FLASK_CONFIG.get('host', '0.0.0.0') 
            port = FLASK_CONFIG.get('port', 5000) # 从配置读取端口
            debug_mode = FLASK_CONFIG.get('debug', False)
            # 使用配置中的端口记录日志
            logger.info(f"Web服务器启动: http://{host}:{port}/")
            self.app.run(
                host=host,
                port=port, # 使用从配置读取的端口
                debug=debug_mode
            )
        except Exception as e:
            logger.error(f"❌ Web服务器运行错误: {str(e)}")
            raise 