"""
文件名: yolo_detect.py
功能: 实现YOLOv8模型的推理、后处理（解码、NMS、坐标还原等）及检测框绘制。
"""
import cv2
import numpy as np
from scipy.special import softmax
import logging
from time import time
from src.detection_core.base_model import BaseModel
from src.config.settings import DETECTION_CONFIG, MODEL_CONFIG

logger = logging.getLogger("RDK_YOLO")

class YOLO11_Detect(BaseModel):
    """
    YOLOv8检测模型类，继承自BaseModel，负责模型推理输出的解码、后处理、NMS等。
    """
    def __init__(self, 
                model_file: str, 
                conf: float, 
                iou: float
                ):
        """
        初始化YOLOv8检测模型，准备量化系数、anchor、阈值等。
        Args:
            model_file (str): 模型文件路径
            conf (float): 置信度阈值
            iou (float): IOU阈值
        """
        super().__init__(model_file)
        # 将反量化系数准备好, 只需要准备一次
        self.s_bboxes_scale = self.quantize_model[0].outputs[0].properties.scale_data[np.newaxis, :]
        self.m_bboxes_scale = self.quantize_model[0].outputs[1].properties.scale_data[np.newaxis, :]
        self.l_bboxes_scale = self.quantize_model[0].outputs[2].properties.scale_data[np.newaxis, :]
        logger.info(f"{self.s_bboxes_scale.shape=}, {self.m_bboxes_scale.shape=}, {self.l_bboxes_scale.shape=}")

        # DFL求期望的系数, 只需要生成一次
        self.dfl_bins = MODEL_CONFIG.get('dfl_bins', 16)
        self.weights_static = np.array([i for i in range(self.dfl_bins)]).astype(np.float32)[np.newaxis, np.newaxis, :]
        logger.info(f"{self.weights_static.shape = }")

        # anchors, 只需要生成一次
        anchor_shapes = MODEL_CONFIG.get('anchor_shapes', [
            {'size': 40, 'stride': 8},
            {'size': 20, 'stride': 16},
            {'size': 10, 'stride': 32}
        ])
        # 兼容性处理
        self.s_anchor = np.stack([
            np.tile(np.linspace(0.5, anchor_shapes[0]['size']-0.5, anchor_shapes[0]['size']), reps=anchor_shapes[0]['size']),
            np.repeat(np.arange(0.5, anchor_shapes[0]['size']+0.5, 1), anchor_shapes[0]['size'])
        ], axis=0).transpose(1,0)
        self.m_anchor = np.stack([
            np.tile(np.linspace(0.5, anchor_shapes[1]['size']-0.5, anchor_shapes[1]['size']), reps=anchor_shapes[1]['size']),
            np.repeat(np.arange(0.5, anchor_shapes[1]['size']+0.5, 1), anchor_shapes[1]['size'])
        ], axis=0).transpose(1,0)
        self.l_anchor = np.stack([
            np.tile(np.linspace(0.5, anchor_shapes[2]['size']-0.5, anchor_shapes[2]['size']), reps=anchor_shapes[2]['size']),
            np.repeat(np.arange(0.5, anchor_shapes[2]['size']+0.5, 1), anchor_shapes[2]['size'])
        ], axis=0).transpose(1,0)
        logger.info(f"{self.s_anchor.shape = }, {self.m_anchor.shape = }, {self.l_anchor.shape = }")

        # 输入图像大小, 一些阈值, 提前计算好
        self.input_image_size = MODEL_CONFIG.get('input_image_size', 320)
        self.conf = conf
        self.iou = iou
        self.conf_inverse = -np.log(1/conf - 1)
        logger.info("iou threshol = %.2f, conf threshol = %.2f"%(iou, conf))
        logger.info("sigmoid_inverse threshol = %.2f"%self.conf_inverse)

    def postProcess(self, outputs: list[np.ndarray]) -> tuple[list]:
        """
        对模型输出进行后处理，包括解码、阈值筛选、NMS、坐标还原等。
        Args:
            outputs (list[np.ndarray]): 模型推理输出
        Returns:
            tuple[list]: (类别ID, 置信度, 边界框)
        """
        begin_time = time()
        # reshape 修改为对应的尺寸
        s_bboxes = outputs[1].reshape(-1, 64)  # 40x40 -> 1600
        s_clses = outputs[0].reshape(-1, 1)
        
        m_bboxes = outputs[3].reshape(-1, 64)  # 20x20 -> 400
        m_clses = outputs[2].reshape(-1, 1)
        
        l_bboxes = outputs[5].reshape(-1, 64)  # 10x10 -> 100
        l_clses = outputs[4].reshape(-1, 1)

        # 计算每个尺度的最大置信度及有效索引
        s_max_scores = np.max(s_clses, axis=1)
        s_valid_indices = np.flatnonzero(s_max_scores >= self.conf_inverse)
        if len(s_valid_indices) > s_bboxes.shape[0]:
            s_valid_indices = s_valid_indices[:s_bboxes.shape[0]]

        m_max_scores = np.max(m_clses, axis=1)
        m_valid_indices = np.flatnonzero(m_max_scores >= self.conf_inverse)
        if len(m_valid_indices) > m_bboxes.shape[0]:
            m_valid_indices = m_valid_indices[:m_bboxes.shape[0]]

        l_max_scores = np.max(l_clses, axis=1)
        l_valid_indices = np.flatnonzero(l_max_scores >= self.conf_inverse)
        if len(l_valid_indices) > l_bboxes.shape[0]:
            l_valid_indices = l_valid_indices[:l_bboxes.shape[0]]

        # 获取每个有效目标的类别ID和置信度
        s_ids = np.argmax(s_clses[s_valid_indices, : ], axis=1)
        s_scores = s_max_scores[s_valid_indices]

        m_ids = np.argmax(m_clses[m_valid_indices, : ], axis=1)
        m_scores = m_max_scores[m_valid_indices]

        l_max_scores = np.max(l_clses, axis=1)
        l_valid_indices = np.flatnonzero(l_max_scores >= self.conf_inverse)  # 得到大于阈值分数的索引，此时为小数字
        l_ids = np.argmax(l_clses[l_valid_indices, : ], axis=1)
        l_scores = l_max_scores[l_valid_indices]

        # 3个Classify分类分支：Sigmoid计算
        s_scores = 1 / (1 + np.exp(-s_scores))
        m_scores = 1 / (1 + np.exp(-m_scores))
        l_scores = 1 / (1 + np.exp(-l_scores))

        # 3个Bounding Box分支：筛选
        s_bboxes_float32 = s_bboxes[s_valid_indices,:]
        m_bboxes_float32 = m_bboxes[m_valid_indices,:]
        l_bboxes_float32 = l_bboxes[l_valid_indices,:]

        # 3个Bounding Box分支：dist2bbox (ltrb2xyxy)
        s_ltrb_indices = np.sum(softmax(s_bboxes_float32.reshape(-1, 4, 16), axis=2) * self.weights_static, axis=2)
        s_anchor_indices = self.s_anchor[s_valid_indices, :]
        s_x1y1 = s_anchor_indices - s_ltrb_indices[:, 0:2]
        s_x2y2 = s_anchor_indices + s_ltrb_indices[:, 2:4]
        s_dbboxes = np.hstack([s_x1y1, s_x2y2])*8

        m_ltrb_indices = np.sum(softmax(m_bboxes_float32.reshape(-1, 4, 16), axis=2) * self.weights_static, axis=2)
        m_anchor_indices = self.m_anchor[m_valid_indices, :]
        m_x1y1 = m_anchor_indices - m_ltrb_indices[:, 0:2]
        m_x2y2 = m_anchor_indices + m_ltrb_indices[:, 2:4]
        m_dbboxes = np.hstack([m_x1y1, m_x2y2])*16

        l_ltrb_indices = np.sum(softmax(l_bboxes_float32.reshape(-1, 4, 16), axis=2) * self.weights_static, axis=2)
        l_anchor_indices = self.l_anchor[l_valid_indices,:]
        l_x1y1 = l_anchor_indices - l_ltrb_indices[:, 0:2]
        l_x2y2 = l_anchor_indices + l_ltrb_indices[:, 2:4]
        l_dbboxes = np.hstack([l_x1y1, l_x2y2])*32

        # 大中小特征层阈值筛选结果拼接
        dbboxes = np.concatenate((s_dbboxes, m_dbboxes, l_dbboxes), axis=0)
        scores = np.concatenate((s_scores, m_scores, l_scores), axis=0)
        ids = np.concatenate((s_ids, m_ids, l_ids), axis=0)

        # NMS非极大值抑制，去除冗余框
        indices = cv2.dnn.NMSBoxes(dbboxes, scores, self.conf, self.iou)

        # 还原到原始的img尺度
        bboxes = dbboxes[indices] * np.array([self.x_scale, self.y_scale, self.x_scale, self.y_scale])
        bboxes = bboxes.astype(np.int32)

        # logger.debug("\033[1;31m" + f"Post Process time = {1000*(time() - begin_time):.2f} ms" + "\033[0m")

        return ids[indices], scores[indices], bboxes


def draw_detection(img: np.array, 
                   bbox: tuple[int, int, int, int],
                   score: float, 
                   class_id: int) -> None:
    """
    在图像上绘制检测框和标签。
    参数:
        img (np.array): 输入图像
        bbox (tuple[int, int, int, int]): 边界框坐标 (x1, y1, x2, y2)
        score (float): 检测置信度
        class_id (int): 检测类别ID
    """
    x1, y1, x2, y2 = bbox
    color = DETECTION_CONFIG['colors'][class_id%20]
    cv2.rectangle(img, (x1, y1), (x2, y2), color, 2)
    label = f"{DETECTION_CONFIG['class_names'][class_id]}: {score:.2f}"
    (label_width, label_height), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)
    label_x, label_y = x1, y1 - 10 if y1 - 10 > label_height else y1 + 10
    cv2.rectangle(
        img, (label_x, label_y - label_height), (label_x + label_width, label_y + label_height), color, cv2.FILLED
    )
    cv2.putText(img, label, (label_x, label_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1, cv2.LINE_AA) 