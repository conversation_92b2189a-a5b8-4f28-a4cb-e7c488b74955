# 工具模块说明文档 (Utils)

## 模块概述
本目录包含RDK YOLO演示项目的核心工具类实现，主要提供舵机控制、串口通信、坐标变换等基础功能支持。

## 文件说明

### 1. servo_tracking.py
舵机跟踪控制实现
- 核心功能：
  * 自动跟踪线程管理
  * PID控制器集成
  * 平滑舵机控制
  * 中心死区控制
  * 首次检测特殊处理
  * 超时处理机制
- 技术特点：
  * 支持水平和垂直双舵机协同控制
  * 基于PID的平滑舵机控制
  * 中心死区控制机制
  * 首次检测降速防过冲
  * 角度变化限制机制
  * 完整的日志记录系统
- 主要参数：
  * face_timeout: 人脸超时时间（0.8秒）
  * movement_threshold: 移动阈值（4像素）
  * angle_adjust_factor: 角度调整系数（0.08）
  * min_servo_interval: 最小移动间隔（0.012秒）
  * sleep_interval: 线程循环间隔（0.03秒）
  * center_deadzone: 中心死区半径（15像素）
  * first_detection_factor: 首次检测调整因子（0.3）
  * max_angle_change: 单次最大角度变化限制（10.0度）
- 核心接口：
  * start_tracking: 启动自动跟踪
  * stop_tracking: 停止自动跟踪
  * _tracking_task: 跟踪任务实现
- 控制策略：
  * 中心死区控制：目标在中心区域时暂停追踪
  * 首次检测降速：降低首次检测时的调整量避免过冲
  * 平滑控制：通过PID控制器和角度变化限制实现
  * 同时控制两个舵机：使用move_servos_together同时发送命令
  * 超时处理：自动处理目标丢失情况
  * 移动阈值控制：小偏移不调整舵机

### 2. pid_controller.py
PID控制器实现
- 核心功能：
  * 基础PID控制算法
  * 首次更新特殊处理
  * 积分项限制机制
  * 大误差积分抑制
  * 输出限幅处理
- 关键特性：
  * 首次更新缩减响应防过冲
  * 积分饱和保护机制
  * 误差阈值积分控制
  * 配置文件参数导入
  * 安全的微分计算
- 主要参数：
  * kp: 比例系数（默认从配置文件导入，备用值0.02）
  * ki: 积分系数（默认从配置文件导入，备用值0.0）
  * kd: 微分系数（默认从配置文件导入，备用值0.08）
  * max_integral: 积分上限值（默认20.0）
  * first_update_factor: 首次更新缩减因子（默认0.5）
- 核心接口:
  * update(error, dt): 根据误差更新PID控制器并返回控制输出
  * reset(): 重置PID控制器状态
- 实现特点：
  * 首次更新只使用比例控制，并应用缩减因子
  * 误差大于阈值时降低积分增益，减少过冲
  * 积分项有上限限制，防止积分饱和
  * 微分计算添加时间保护，防止除零错误
  * 输出限制在±100范围内，防止过大调整

### 3. servo_controller.py
舵机底层控制实现
- 主要功能：
  * 舵机连接管理
  * 基础角度控制
  * 多舵机同步控制
  * 自动跟踪控制
  * 自检程序执行
  * 线程安全控制
- 技术特点：
  * 支持水平(pan)和垂直(tilt)双舵机控制
  * 角度范围：0-270度（实现自动限制）
  * 初始位置：水平135度，垂直90度
  * 线程锁机制保证并发安全
  * 指令发送状态记录
  * 完整的日志记录系统
- 核心接口：
  * connect: 连接舵机设备
  * disconnect: 断开连接
  * is_connected: 检查连接状态
  * move_servos: 控制单个舵机角度
  * move_servos_together: 同时控制多个舵机
  * start_auto_tracking: 启动自动跟踪
  * stop_auto_tracking: 停止自动跟踪
  * send_test_data: 执行自检程序
- 自检功能：
  * 中心位置校准（回到135度位置）
  * 十字运动测试（上、下、左、右）
  * 圆形运动测试（8点圆周运动）
  * Z字形运动测试（5点Z形移动）
  * 位置恢复功能（返回初始位置）
- 实现特点：
  * 串口通信自动错误处理和重试
  * 角度范围自动限制（0-270度）
  * 舵机角度状态实时记录
  * 通过字典格式传递多舵机控制指令
  * 线程锁保证串口资源安全访问
  * 异常情况详细日志记录

### 4. servo_protocol.py
舵机通信协议实现
- 核心功能：
  * 协议数据生成
  * 校验和计算
  * 角度数据转换
  * 测试数据生成
  * 云台舵机控制
- 技术特点：
  * 支持9路舵机控制
  * 角度精度：0.1度
  * 帧格式：帧头(0xAA,0xBB) + 数据 + 校验和 + 帧尾(0xCC,0xDD)
  * 完整的日志记录
- 主要参数：
  * 云台舵机引脚：
    - 水平舵机(PAN): 引脚2
    - 垂直舵机(TILT): 引脚3
  * 支持舵机引脚：2,3,4,6,44,45,46,7,10
- 核心接口：
  * generate_protocol: 生成协议数据
  * calculate_checksum: 计算校验和
  * angle_to_bytes: 角度数据转换
  * generate_test_data: 生成测试数据

### 5. coordinate.py
坐标计算与目标追踪工具

- 主要功能：
  * 目标检测框中心点计算
  * 屏幕中心偏移量计算
  * 目标方向判断
  * 智能目标选择
  * 目标持久性跟踪
  * 防抖动目标切换
  * 空间位置关联追踪

- 技术特点：
  * 实时坐标计算
  * 基于评分的目标选择
  * 支持多目标场景
  * 高效的计算处理
  * 目标历史记忆功能
  * 平滑目标切换机制
  * 基于位置的目标ID关联

- 核心接口：
  * update_targets: 更新目标信息并基于综合评分选择最优目标
  * calculate_offset: 计算目标与屏幕中心的偏移量
  * get_direction: 获取目标相对屏幕中心的方向描述
  * _cleanup_old_targets: 清理超时目标记录
  * _associate_targets: 基于位置匹配关联当前目标和历史目标

- 目标选择策略：
  * 置信度权重（70%）：目标检测的置信度分数
  * 面积权重（30%）：归一化后的目标检测框面积
  * 持久性权重（20%）：目标连续被检测的稳定性
  * 位置权重（30%）：目标位置的连续性评分
  * 当前目标加成：防止频繁切换的目标稳定性机制
  * 综合评分计算：score = 基础评分 + 持久性评分 * 持久性权重 + 位置连续性评分 * 位置权重 + 当前目标加成
    * 基础评分 = 0.7 * confidence + 0.3 * normalized_area

- 关键参数：
  * confidence_weight: 置信度权重（默认0.7）
  * size_weight: 面积权重（默认0.3）
  * persistence_weight: 持久性权重（默认0.2）
  * position_weight: 位置连续性权重（默认0.3）
  * max_position_distance: 最大匹配距离（默认100像素）
  * target_switch_threshold: 目标切换阈值（默认0.15）
  * target_timeout: 目标超时时间（默认1.0秒）
  * max_history_count: 历史记录最大计数（默认10）
  * direction_threshold: 方向判断阈值（10像素）

- 实现特点：
  * 面积归一化处理，避免大值影响
  * 加权评分机制，平衡各项指标
  * 目标切换阈值机制，避免频繁切换
  * 目标历史记录，实现记忆功能
  * 空间位置连续性追踪，确保目标ID一致性
  * 目标ID基于UUID生成，确保唯一性
  * 基于欧氏距离的目标匹配算法
  * 超时目标自动清理
  * 实时日志输出，便于调试优化

- 使用建议：
  * 可通过调整权重参数优化目标选择策略
  * 增大target_switch_threshold可减少目标切换频率
  * 减小max_position_distance可提高目标匹配精度
  * 增大position_weight可增强位置连续性对评分的影响
  * 减小persistence_weight可降低历史因素影响
  * 根据实际场景需求平衡各项参数
  * 观察日志输出，进行针对性优化

### 6. serial_protocol.py
串口通信协议实现
- 核心功能：
  * 串口设备连接管理
  * 协议数据帧生成
  * 数据校验和计算
  * 角度数据转换
  * 数据发送控制
  * 异常处理和日志记录
- 技术特点：
  * 支持9路舵机同时控制
  * 帧格式：帧头(0xAA,0xBB) + 数据 + 校验和 + 帧尾(0xCC,0xDD)
  * 角度精度：0.1度（内部放大10倍处理）
  * 舵机数据字典格式传递
  * 完整的错误处理和异常捕获
  * 详细的日志记录系统
- 主要参数：
  * 默认串口：/dev/ttyS1
  * 默认波特率：115200
  * 支持舵机引脚：2,3,4,6,44,45,46,7,10
  * 帧头：0xAA,0xBB
  * 帧尾：0xCC,0xDD
- 核心接口：
  * connect: 连接串口设备
  * disconnect: 断开连接
  * generate_protocol: 生成协议数据
  * send_data: 发送数据
  * calculate_checksum: 计算校验和
  * angle_to_bytes: 角度转换为两字节数据
- 协议格式详解：
  * 帧头：2字节(0xAA,0xBB)
  * 舵机数量：1字节
  * 舵机数据：每个舵机3字节（1字节引脚+2字节角度）
  * 未使用舵机：填充3字节0
  * 校验和：1字节(帧头+数据的总和取低8位)
  * 帧尾：2字节(0xCC,0xDD)
- 实现特点：
  * 角度值内部放大10倍保留一位小数精度
  * 最多支持9个舵机，超出会抛出ValueError异常
  * 使用字典格式{引脚:角度}传递舵机控制数据
  * 自动计算校验和防止传输错误
  * 发送数据时记录十六进制日志便于调试
  * 完整的异常捕获和错误处理机制

## 使用说明
1. 舵机控制相关：
   - 使用`servo_controller.py`进行基础控制
   - 通过`servo_tracking.py`实现目标跟踪
   - `pid_controller.py`提供运动控制算法支持

2. 通信相关：
   - `serial_protocol.py`处理基础串口通信
   - `servo_protocol.py`处理舵机专用协议

3. 坐标处理：
   - 使用`coordinate.py`进行各类坐标转换

