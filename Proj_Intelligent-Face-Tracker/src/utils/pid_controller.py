import logging

# 尝试从settings导入默认配置
try:
    from src.config.settings import PID_CONFIG
    DEFAULT_KP = PID_CONFIG['pan']['kp']
    DEFAULT_KI = PID_CONFIG['pan']['ki']
    DEFAULT_KD = PID_CONFIG['pan']['kd']
    DEFAULT_MAX_INTEGRAL = PID_CONFIG['pan'].get('max_integral', 20.0)
    DEFAULT_FIRST_UPDATE_FACTOR = PID_CONFIG['pan'].get('first_update_factor', 0.5)
    DEFAULT_OUTPUT_LIMIT = PID_CONFIG['pan'].get('output_limit', 100)
    DEFAULT_INTEGRAL_ERROR_THRESHOLD = PID_CONFIG['pan'].get('integral_error_threshold', 10)
except ImportError:
    # 使用硬编码的默认值作为后备
    DEFAULT_KP = 0.02
    DEFAULT_KI = 0.0
    DEFAULT_KD = 0.08
    DEFAULT_MAX_INTEGRAL = 20.0
    DEFAULT_FIRST_UPDATE_FACTOR = 0.5
    DEFAULT_OUTPUT_LIMIT = 100
    DEFAULT_INTEGRAL_ERROR_THRESHOLD = 10

logger = logging.getLogger("RDK_YOLO")

class PIDController:
    """
    基础PID控制器类
    """
    def __init__(self, kp=DEFAULT_KP, ki=DEFAULT_KI, kd=DEFAULT_KD, 
                 max_integral=DEFAULT_MAX_INTEGRAL, 
                 first_update_factor=DEFAULT_FIRST_UPDATE_FACTOR,
                 output_limit=DEFAULT_OUTPUT_LIMIT,
                 integral_error_threshold=DEFAULT_INTEGRAL_ERROR_THRESHOLD):
        """
        初始化PID控制器
        
        Args:
            kp (float): 比例系数
            ki (float): 积分系数
            kd (float): 微分系数
            max_integral (float): 积分上限值
            first_update_factor (float): 首次更新的缩减因子
            output_limit (float): PID输出限幅
            integral_error_threshold (float): 积分增益减小的误差阈值
        """
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.prev_error = 0
        self.integral = 0
        self.first_update = True  # 首次更新标志
        self.max_integral = max_integral  # 积分上限
        self.first_update_factor = first_update_factor  # 首次更新的缩减因子
        self.output_limit = output_limit  # PID输出限幅
        self.integral_error_threshold = integral_error_threshold  # 积分增益减小的误差阈值
        
    def update(self, error, dt, distance=None):
        """
        更新PID控制器
        
        Args:
            error (float): 当前误差
            dt (float): 时间间隔
            distance (float, optional): 不再使用
        
        Returns:
            float: 控制输出
        """
        # 首次更新特殊处理
        if self.first_update:
            # 首次更新不计算积分和微分，只使用比例项
            self.prev_error = error
            self.integral = 0
            self.first_update = False
            # 返回减弱后的比例控制
            return self.kp * error * self.first_update_factor
        
        # 积分项
        # 当误差接近0时积分增益减小，防止振荡
        if abs(error) < self.integral_error_threshold:
            self.integral += error * dt
        else:
            # 大误差时，以较小的权重积累积分
            self.integral += error * dt * 0.3
        
        # 限制积分项，防止积分饱和
        self.integral = max(-self.max_integral, min(self.max_integral, self.integral))
        
        # 微分项，添加滤波防止微分项噪声过大
        derivative = (error - self.prev_error) / max(dt, 0.001)
        
        # 计算输出
        output = (self.kp * error + 
                 self.ki * self.integral + 
                 self.kd * derivative)
        
        # 限制输出范围，防止过大调整
        output = max(-self.output_limit, min(self.output_limit, output))
        
        # 更新状态
        self.prev_error = error
        
        return output
    
    def reset(self):
        """
        重置PID控制器状态
        """
        self.prev_error = 0
        self.integral = 0
        self.first_update = True  # 重置首次更新标志
