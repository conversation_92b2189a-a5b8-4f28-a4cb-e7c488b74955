import numpy as np
import time
import logging
import uuid
from src.config.settings import TARGET_TRACKING_CONFIG, KALMAN_FILTER_CONFIG
from src.utils.kalman_filter import KalmanFilter

logger = logging.getLogger("RDK_YOLO")

class CoordinateCalculator:
    def __init__(self, frame_width: int, frame_height: int):
        """
        初始化坐标计算器
        
        Args:
            frame_width (int): 视频帧宽度
            frame_height (int): 视频帧高度
        """
        self.frame_width = frame_width
        self.frame_height = frame_height
        self.center_x = frame_width // 2
        self.center_y = frame_height // 2
        self.latest_bbox = None
        self.latest_update_time = 0
        
        # 从配置文件加载参数
        # 评分权重配置
        self.confidence_weight = TARGET_TRACKING_CONFIG['confidence_weight']
        self.size_weight = TARGET_TRACKING_CONFIG['size_weight']
        self.persistence_weight = TARGET_TRACKING_CONFIG['persistence_weight']
        self.position_weight = TARGET_TRACKING_CONFIG['position_weight']
        
        # 目标跟踪参数
        self.target_switch_threshold = TARGET_TRACKING_CONFIG['target_switch_threshold']
        self.target_timeout = TARGET_TRACKING_CONFIG['target_timeout']
        self.max_history_count = TARGET_TRACKING_CONFIG['max_history_count']
        self.max_position_distance = TARGET_TRACKING_CONFIG['max_position_distance']
        
        # 方向判断参数
        self.direction_threshold = TARGET_TRACKING_CONFIG['direction_threshold']
        
        # 初始化目标跟踪状态
        self.current_target_id = None  # 当前正在追踪的目标ID
        self.target_history = {}  # 目标历史记录 {target_id: {"count": 连续检测次数, "last_seen": 最后检测时间, "position": (x,y)}}
        
        # 初始化卡尔曼滤波器
        self.kalman_filters = {}  # 每个目标ID对应一个滤波器
        self.kalman_dt = KALMAN_FILTER_CONFIG['dt']
        self.process_noise_scale = KALMAN_FILTER_CONFIG['process_noise_scale']
        self.measurement_noise_scale = KALMAN_FILTER_CONFIG['measurement_noise_scale']
        
        # 预测配置
        self.prediction_enabled = True  # 默认启用预测
        self.last_predicted_position = None
        self.prediction_confidence = 0.0

    def update_targets(self, bboxes, scores):
        """
        更新目标信息，基于检测框大小、置信度、位置连续性和目标持久性的综合评分选择最优目标
        
        Args:
            bboxes: 检测到的边界框列表，格式为[(x1, y1, x2, y2), ...]
            scores: 对应的置信度分数列表
            
        Returns:
            tuple: (目标ID, 边界框)
        """
        current_time = time.time()
        
        # 如果没有检测到目标，执行预测逻辑
        if len(bboxes) == 0:
            # 如果当前有追踪的目标且启用了预测
            if self.current_target_id is not None and self.prediction_enabled:
                predicted_bbox = self._predict_target_position(self.current_target_id, current_time)
                if predicted_bbox is not None:
                    logger.debug(f"目标丢失但使用预测位置: ID={self.current_target_id}, 预测边界框={predicted_bbox}")
                    self.latest_bbox = predicted_bbox
                    self.latest_update_time = current_time
                    self.prediction_confidence -= 0.2  # 每次预测，降低预测置信度
                    
                    # 如果预测置信度过低，停止预测
                    if self.prediction_confidence <= 0:
                        logger.debug(f"预测置信度过低，停止预测当前目标: ID={self.current_target_id}")
                        self.current_target_id = None
                        self.latest_bbox = None
                        self.latest_update_time = 0
                        self.last_predicted_position = None
                        return None, None
                    
                    return self.current_target_id, self.latest_bbox
            
            # 没有可预测的目标或预测失败，清除数据
            self.latest_bbox = None
            self.latest_update_time = 0
            return None, None
        
        # 清理过期目标
        self._cleanup_old_targets(current_time)
        
        # 计算每个检测框的中心点和面积
        centers = []
        areas = []
        for bbox in bboxes:
            x1, y1, x2, y2 = bbox
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2
            centers.append((center_x, center_y))
            area = (x2 - x1) * (y2 - y1)
            areas.append(area)
        
        # 将当前检测结果与历史目标关联
        target_ids = self._associate_targets(centers, areas, bboxes, scores, current_time)
        
        # 对面积进行归一化处理
        if len(areas) > 0:
            max_area = max(areas)
            normalized_areas = [area / max_area for area in areas]
        else:
            normalized_areas = [0] * len(bboxes)
        
        # 计算综合评分
        combined_scores = []
        persistence_scores = []
        position_scores = []
        
        for i, target_id in enumerate(target_ids):
            # 计算持久性评分（归一化）
            persistence_score = self.target_history[target_id]["count"] / self.max_history_count
            persistence_scores.append(persistence_score)
            
            # 计算位置连续性评分
            position_score = self.target_history[target_id].get("position_score", 0.0)
            position_scores.append(position_score)
            
            # 基础评分 = confidence_weight * 置信度 + size_weight * 归一化面积
            base_score = (self.confidence_weight * scores[i] + 
                         self.size_weight * normalized_areas[i])
            
            # 当前目标的持久性加成（如果是当前正在追踪的目标）
            current_target_bonus = 0
            if self.current_target_id == target_id:
                current_target_bonus = self.target_switch_threshold
            
            # 最终评分 = 基础评分 + 持久性评分 * 持久性权重 + 位置连续性评分 * 位置权重 + 当前目标加成
            final_score = (base_score + 
                          persistence_score * self.persistence_weight + 
                          position_score * self.position_weight + 
                          current_target_bonus)
            
            combined_scores.append(final_score)
            
            logger.debug(f"目标评分: ID={target_id}, 置信度={scores[i]:.2f}, "
                        f"面积={areas[i]}, 持久性={persistence_score:.2f}, "
                        f"位置连续性={position_score:.2f}, 最终评分={final_score:.2f}")
        
        # 获取评分最高的目标
        if combined_scores:
            best_idx = np.argmax(combined_scores)
            best_target_id = target_ids[best_idx]
            best_score = combined_scores[best_idx]
            
            # 如果当前已有追踪目标，且不是评分最高的目标，则应用切换阈值
            if (self.current_target_id is not None and 
                self.current_target_id in self.target_history and
                self.current_target_id != best_target_id):
                
                current_target_idx = target_ids.index(self.current_target_id) if self.current_target_id in target_ids else -1
                
                # 如果当前目标仍在检测列表中
                if current_target_idx >= 0:
                    current_score = combined_scores[current_target_idx]
                    
                    # 如果新目标评分没有显著高于当前目标，继续追踪当前目标
                    if best_score - current_score < self.target_switch_threshold:
                        logger.debug(f"保持当前目标: ID={self.current_target_id}, "
                                    f"当前评分={current_score:.2f}, 最佳评分={best_score:.2f}, "
                                    f"差值={best_score - current_score:.2f} < 阈值{self.target_switch_threshold}")
                        best_idx = current_target_idx
                        best_target_id = self.current_target_id
                    else:
                        logger.debug(f"切换目标: 从ID={self.current_target_id}到ID={best_target_id}, "
                                    f"评分差值={best_score - current_score:.2f} >= 阈值{self.target_switch_threshold}")
            
            # 更新当前追踪目标
            self.current_target_id = best_target_id
            self.latest_bbox = bboxes[best_idx]
            self.latest_update_time = current_time
            
            # 更新预测相关信息
            if self.prediction_enabled:
                self._update_prediction(best_target_id, centers[best_idx], current_time)
                self.prediction_confidence = 1.0  # 重置预测置信度
            
            logger.debug(f"选择目标: ID={best_target_id}, 置信度={scores[best_idx]:.2f}, "
                         f"面积={areas[best_idx]}, 持久性={persistence_scores[best_idx]:.2f}, "
                         f"位置连续性={position_scores[best_idx]:.2f}, 最终评分={best_score:.2f}")
            
            return best_target_id, self.latest_bbox
        
        # 如果没有有效评分，使用原始的置信度选择方法作为后备策略
        best_idx = np.argmax(scores)
        best_target_id = target_ids[best_idx]
        self.current_target_id = best_target_id
        self.latest_bbox = bboxes[best_idx]
        self.latest_update_time = current_time
        
        # 更新预测相关信息
        if self.prediction_enabled:
            self._update_prediction(best_target_id, centers[best_idx], current_time)
            self.prediction_confidence = 1.0  # 重置预测置信度
        
        return best_target_id, self.latest_bbox
    
    def _update_prediction(self, target_id, center, current_time):
        """
        更新目标的卡尔曼滤波器
        
        Args:
            target_id: 目标ID
            center: 目标中心坐标 (x, y)
            current_time: 当前时间戳
        """
        # 如果目标没有对应的卡尔曼滤波器，创建一个
        if target_id not in self.kalman_filters:
            self.kalman_filters[target_id] = KalmanFilter(
                dt=self.kalman_dt,
                process_noise_scale=self.process_noise_scale,
                measurement_noise_scale=self.measurement_noise_scale
            )
        
        # 更新卡尔曼滤波器
        estimated_position, estimated_velocity = self.kalman_filters[target_id].update(center, current_time)
        
        # 保存目标的速度信息到历史记录中
        if target_id in self.target_history:
            self.target_history[target_id]["velocity"] = estimated_velocity
            
            # 计算速度大小
            velocity_magnitude = np.sqrt(estimated_velocity[0]**2 + estimated_velocity[1]**2)
            self.target_history[target_id]["velocity_magnitude"] = velocity_magnitude
            
            logger.debug(f"目标速度更新: ID={target_id}, 速度=({estimated_velocity[0]:.1f}, {estimated_velocity[1]:.1f}), "
                        f"速度大小={velocity_magnitude:.1f}")
    
    def _predict_target_position(self, target_id, current_time):
        """
        预测目标未来位置
        
        Args:
            target_id: 目标ID
            current_time: 当前时间戳
            
        Returns:
            tuple or None: 预测的边界框 (x1, y1, x2, y2) 或 None
        """
        # 检查目标是否存在且有对应的滤波器
        if (target_id not in self.target_history or 
            target_id not in self.kalman_filters or 
            not self.kalman_filters[target_id].initialized):
            return None
        
        # 获取最后一次更新时间和位置
        last_seen = self.target_history[target_id]["last_seen"]
        last_bbox = self.target_history[target_id]["bbox"]
        
        # 如果数据太旧，不进行预测
        if current_time - last_seen > self.target_timeout * 1.5:
            return None
        
        # 计算预测时间（考虑目标运动速度）
        prediction_time = current_time - last_seen
        
        # 使用卡尔曼滤波器预测位置
        predicted_position = self.kalman_filters[target_id].predict(prediction_time)
        
        if predicted_position is None:
            return None
        
        # 保存预测位置
        self.last_predicted_position = predicted_position
        
        # 提取原始边界框的宽度和高度
        x1, y1, x2, y2 = last_bbox
        width = x2 - x1
        height = y2 - y1
        
        # 计算预测的边界框
        pred_x1 = int(predicted_position[0] - width / 2)
        pred_y1 = int(predicted_position[1] - height / 2)
        pred_x2 = int(predicted_position[0] + width / 2)
        pred_y2 = int(predicted_position[1] + height / 2)
        
        # 确保边界框在图像范围内
        pred_x1 = max(0, pred_x1)
        pred_y1 = max(0, pred_y1)
        pred_x2 = min(self.frame_width, pred_x2)
        pred_y2 = min(self.frame_height, pred_y2)
        
        # 检查预测框是否有效（宽度和高度必须大于0）
        if pred_x2 <= pred_x1 or pred_y2 <= pred_y1:
            return None
        
        predicted_bbox = (pred_x1, pred_y1, pred_x2, pred_y2)
        
        logger.debug(f"目标位置预测: ID={target_id}, 原始边界框={last_bbox}, "
                    f"预测边界框={predicted_bbox}, 预测时间={prediction_time:.3f}秒")
        
        return predicted_bbox
    
    def _associate_targets(self, centers, areas, bboxes, scores, current_time):
        """
        将当前检测到的目标与历史目标关联，基于位置连续性
        
        Args:
            centers: 检测框中心点列表 [(x, y), ...]
            areas: 检测框面积列表
            bboxes: 检测框列表
            scores: 置信度列表
            current_time: 当前时间戳
            
        Returns:
            list: 关联后的目标ID列表
        """
        target_ids = []
        
        # 如果历史记录为空，创建新目标
        if not self.target_history:
            for i, (center, area, bbox, score) in enumerate(zip(centers, areas, bboxes, scores)):
                target_id = f"target_{uuid.uuid4().hex[:8]}"
                self.target_history[target_id] = {
                    "count": 1,
                    "last_seen": current_time,
                    "position": center,
                    "bbox": bbox,
                    "score": score,
                    "area": area,
                    "position_score": 0.0,
                    "velocity": (0, 0),
                    "velocity_magnitude": 0.0
                }
                target_ids.append(target_id)
            return target_ids
        
        # 构建距离矩阵
        history_centers = [info["position"] for info in self.target_history.values()]
        history_ids = list(self.target_history.keys())
        
        # 计算每个当前目标到每个历史目标的距离
        distance_matrix = np.zeros((len(centers), len(history_centers)))
        
        for i, current_center in enumerate(centers):
            for j, history_center in enumerate(history_centers):
                # 计算欧氏距离
                distance = np.sqrt((current_center[0] - history_center[0])**2 + 
                                 (current_center[1] - history_center[1])**2)
                distance_matrix[i, j] = distance
        
        # 为每个当前目标找到最近的历史目标
        assigned_history_indices = []
        
        for i in range(len(centers)):
            current_center = centers[i]
            current_area = areas[i]
            current_bbox = bboxes[i]
            current_score = scores[i]
            
            min_distance_idx = -1
            min_distance = float('inf')
            
            # 查找最近的未分配历史目标
            for j in range(len(history_centers)):
                if j in assigned_history_indices:
                    continue
                    
                distance = distance_matrix[i, j]
                
                # 使用卡尔曼滤波器预测的位置进行辅助匹配
                target_id = history_ids[j]
                if (self.prediction_enabled and 
                    target_id in self.kalman_filters and 
                    self.kalman_filters[target_id].initialized):
                    
                    # 获取历史位置和时间
                    last_position = self.target_history[target_id]["position"]
                    last_time = self.target_history[target_id]["last_seen"]
                    
                    # 预测当前时间的位置
                    dt = current_time - last_time
                    predicted_position = self.kalman_filters[target_id].predict(dt)
                    
                    if predicted_position is not None:
                        # 计算预测位置与当前位置的距离
                        pred_distance = np.sqrt((current_center[0] - predicted_position[0])**2 + 
                                             (current_center[1] - predicted_position[1])**2)
                        
                        # 将预测距离与欧氏距离融合，赋予预测距离更高权重
                        distance = distance * 0.3 + pred_distance * 0.7
                
                # 如果距离超过阈值，不考虑匹配
                if distance > self.max_position_distance:
                    continue
                
                if distance < min_distance:
                    min_distance = distance
                    min_distance_idx = j
            
            # 如果找到匹配的历史目标
            if min_distance_idx >= 0:
                target_id = history_ids[min_distance_idx]
                assigned_history_indices.append(min_distance_idx)
                
                # 计算位置连续性评分（距离越近，分数越高）
                position_score = 1.0 - min(min_distance / self.max_position_distance, 1.0)
                
                # 更新目标历史信息
                self.target_history[target_id]["count"] = min(
                    self.target_history[target_id]["count"] + 1,
                    self.max_history_count
                )
                self.target_history[target_id]["last_seen"] = current_time
                self.target_history[target_id]["position"] = current_center
                self.target_history[target_id]["bbox"] = current_bbox
                self.target_history[target_id]["score"] = current_score
                self.target_history[target_id]["area"] = current_area
                self.target_history[target_id]["position_score"] = position_score
                
                target_ids.append(target_id)
            else:
                # 创建新目标
                target_id = f"target_{uuid.uuid4().hex[:8]}"
                self.target_history[target_id] = {
                    "count": 1,
                    "last_seen": current_time,
                    "position": current_center,
                    "bbox": current_bbox,
                    "score": current_score,
                    "area": current_area,
                    "position_score": 0.0,
                    "velocity": (0, 0),
                    "velocity_magnitude": 0.0
                }
                target_ids.append(target_id)
        
        return target_ids
    
    def _cleanup_old_targets(self, current_time):
        """
        清理超时的目标记录
        
        Args:
            current_time: 当前时间戳
        """
        to_remove = []
        for target_id, info in self.target_history.items():
            if current_time - info["last_seen"] > self.target_timeout:
                to_remove.append(target_id)
        
        for target_id in to_remove:
            # 清理卡尔曼滤波器
            if target_id in self.kalman_filters:
                del self.kalman_filters[target_id]
                
            del self.target_history[target_id]
            
            # 如果当前目标被移除，重置当前目标ID
            if self.current_target_id == target_id:
                self.current_target_id = None
    
    def calculate_offset(self, bbox: tuple[int, int, int, int]) -> tuple[float, float, float]:
        """
        计算检测框中心点与屏幕中心点的偏移距离
        
        Args:
            bbox (tuple): 边界框坐标 (x1, y1, x2, y2)
            
        Returns:
            tuple: (x偏移量, y偏移量, 距离) 单位为像素
                  x偏移量: 正值表示目标在中心点右侧
                  y偏移量: 正值表示目标在中心点下方
        """
        if bbox is None:
            return 0, 0, 0
            
        x1, y1, x2, y2 = bbox
        target_center_x = (x1 + x2) // 2
        target_center_y = (y1 + y2) // 2
        
        offset_x = target_center_x - self.center_x
        offset_y = target_center_y - self.center_y
        distance = np.sqrt(offset_x**2 + offset_y**2)
        
        return offset_x, offset_y, distance

    def get_direction(self, offset_x: float, offset_y: float) -> str:
        """
        根据偏移量获取方向描述
        
        Args:
            offset_x (float): x轴偏移量
            offset_y (float): y轴偏移量
            
        Returns:
            str: 方向描述
        """
        if abs(offset_x) < self.direction_threshold and abs(offset_y) < self.direction_threshold:
            return "center"
        
        directions = []
        if offset_x > self.direction_threshold:
            directions.append("right")
        elif offset_x < -self.direction_threshold:
            directions.append("left")
            
        if offset_y > self.direction_threshold:
            directions.append("down")
        elif offset_y < -self.direction_threshold:
            directions.append("up")
            
        return "-".join(directions)
