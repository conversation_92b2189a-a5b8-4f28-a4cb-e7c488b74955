import serial
import time
import logging
import threading
import math
from src.utils.servo_protocol import ServoProtocol
from src.utils.servo_tracking import ServoTracker
from src.config.settings import SERVO_HARDWARE_CONFIG

logger = logging.getLogger("RDK_YOLO")

class ServoController:
    """
    舵机控制器类，负责与Arduino通信并控制舵机运动
    """
    def __init__(self, port='/dev/ttyS1', baudrate=115200):
        """
        初始化舵机控制器
        
        Args:
            port (str): 串口设备名
            baudrate (int): 波特率
        """
        self.port = port
        self.baudrate = baudrate
        self.serial = None
        self.connected = False
        self.lock = threading.Lock()
        
        # 当前舵机角度（从配置读取初始值）
        self.current_pan = SERVO_HARDWARE_CONFIG.get('init_pan_angle', 135.0)   # 水平舵机初始角度 (中间位置)
        self.current_tilt = SERVO_HARDWARE_CONFIG.get('init_tilt_angle', 135.0)  # 垂直舵机初始角度 (中间位置)
        
        # 初始化协议处理器
        self.protocol = ServoProtocol()
        
        # 初始化跟踪器
        self.tracker = ServoTracker(self)
        
    def connect(self):
        """
        连接到Arduino
        
        Returns:
            bool: 是否成功连接
        """
        try:
            # 打开串口
            logger.info(f"尝试连接串口: {self.port}, 波特率: {self.baudrate}")
            self.serial = serial.Serial(self.port, self.baudrate, timeout=1)
            self.connected = True
            logger.info(f"已连接到串口设备，端口: {self.port}")
            
            # 等待串口稳定
            time.sleep(1)
            
            # 发送测试数据
            test_data = self.protocol.generate_test_data()
            self.serial.write(test_data)
            logger.info(f"发送测试数据: {test_data.hex().upper()}")
            
            # 初始化舵机位置
            logger.info("初始化舵机位置...")
            self.move_servos(self.protocol.PAN_SERVO_PIN, self.current_pan)
            time.sleep(0.5)
            self.move_servos(self.protocol.TILT_SERVO_PIN, self.current_tilt)
            
            return True
        except Exception as e:
            logger.error(f"连接串口设备失败: {str(e)}")
            self.connected = False
            return False
    
    def disconnect(self):
        """
        断开与串口设备的连接
        """
        if self.serial and self.serial.is_open:
            self.serial.close()
            self.connected = False
            logger.info("已断开与串口设备的连接")
    
    def is_connected(self):
        """
        检查是否已连接到串口设备
        
        Returns:
            bool: 是否已连接
        """
        return self.connected
    
    def move_servos(self, pin, angle):
        """
        移动指定舵机到指定角度
        
        Args:
            pin (int): 舵机引脚
            angle (float): 目标角度 (0-270度)
            
        Returns:
            bool: 是否成功移动舵机
        """
        if not self.connected:
            logger.error("未连接到串口设备，无法控制舵机")
            return False
        
        # 限制角度范围
        angle = max(0, min(270, angle))
        
        # 更新当前角度
        if pin == self.protocol.PAN_SERVO_PIN:
            self.current_pan = angle
        elif pin == self.protocol.TILT_SERVO_PIN:
            self.current_tilt = angle
        
        # 构建协议数据
        servo_data = {}
        servo_data[pin] = angle
        
        # 生成协议
        protocol_data = self.protocol.generate_protocol(servo_data)
        
        # 发送数据
        with self.lock:
            try:
                # 直接发送二进制数据
                logger.info(f"发送的十六进制数据: {protocol_data.hex().upper()}")
                bytes_written = self.serial.write(protocol_data)
                logger.info(f"已发送舵机控制命令: 引脚={pin}, 角度={angle}, 字节数={bytes_written}")
                
                # 等待一小段时间，确保命令被处理
                time.sleep(0.1)
                
                return True
            except Exception as e:
                logger.error(f"发送舵机控制命令失败: {str(e)}")
                return False
    
    def move_servos_together(self, servo_angles):
        """
        同时控制多个舵机到指定角度
        
        Args:
            servo_angles (dict): 舵机角度字典，格式为 {引脚: 角度}
            
        Returns:
            bool: 是否成功移动舵机
        """
        if not self.connected:
            logger.error("未连接到串口设备，无法控制舵机")
            return False
        
        # 限制角度范围并更新当前角度
        for pin, angle in servo_angles.items():
            angle = max(0, min(270, angle))
            servo_angles[pin] = angle
            
            # 更新当前角度记录
            if pin == self.protocol.PAN_SERVO_PIN:
                self.current_pan = angle
            elif pin == self.protocol.TILT_SERVO_PIN:
                self.current_tilt = angle
        
        # 生成协议
        protocol_data = self.protocol.generate_protocol(servo_angles)
        
        # 发送数据
        with self.lock:
            try:
                # 直接发送二进制数据
                logger.info(f"发送的十六进制数据: {protocol_data.hex().upper()}")
                bytes_written = self.serial.write(protocol_data)
                logger.info(f"已同时发送多个舵机控制命令: 舵机数={len(servo_angles)}, 字节数={bytes_written}")
                
                # 等待一小段时间，确保命令被处理
                time.sleep(0.1)
                
                return True
            except Exception as e:
                logger.error(f"发送舵机控制命令失败: {str(e)}")
                return False
    
    def start_auto_tracking(self, coord_calculator):
        """
        开始自动跟踪
        
        Args:
            coord_calculator: 坐标计算器实例
            
        Returns:
            bool: 是否成功启动跟踪
        """
        return self.tracker.start_tracking(coord_calculator)
    
    def stop_auto_tracking(self):
        """
        停止自动跟踪
        
        Returns:
            bool: 是否成功停止跟踪
        """
        return self.tracker.stop_tracking()
    
    def send_test_data(self, update_callback=None):
        """
        执行二维云台自检程序 (作为生成器)
        
        自检流程包括:
        1. 先回到中心位置
        2. 执行"十字"运动 (上下左右)
        3. 执行"圆形"运动
        4. 执行"Z字形"运动
        5. 最后回到中心位置
        
        Args:
            update_callback (function, optional): 用于发送角度更新的回调函数。
                                                回调函数应接受 pan_angle 和 tilt_angle。
                                                例如: callback(pan=135, tilt=90)
                                                如果为 None，则不发送更新。
        
        Yields:
            dict: 包含当前舵机角度或状态信息。
                  例如: {"type": "update", "pan": 135, "tilt": 90}
                        {"type": "status", "message": "自检完成", "success": True}
        """
        if not self.connected:
            logger.error("未连接到串口设备，无法执行云台自检")
            if update_callback:
                yield update_callback(status="error", message="未连接到串口设备")
            return
            
        # 定义一个辅助函数来移动舵机并发送更新
        def move_and_update(pin, angle, delay=0.5):
            # 发送更新 *之前* 移动，这样前端可以立即看到目标角度
            pan = self.current_pan if pin != self.protocol.PAN_SERVO_PIN else angle
            tilt = self.current_tilt if pin != self.protocol.TILT_SERVO_PIN else angle
            if update_callback:
                yield update_callback(pan=pan, tilt=tilt)
            
            # 移动舵机
            success = self.move_servos(pin, angle)
            if not success:
                logger.error(f"移动舵机 {pin} 到 {angle} 失败")
                # 可以选择在这里停止自检或继续
            time.sleep(delay)

        # 定义同时移动并更新的辅助函数
        def move_together_and_update(pan_angle, tilt_angle, delay=0.3):
             # 发送更新 *之前* 移动
             if update_callback:
                 yield update_callback(pan=pan_angle, tilt=tilt_angle)
             
             success = self.move_servos_together({self.protocol.PAN_SERVO_PIN: pan_angle, self.protocol.TILT_SERVO_PIN: tilt_angle})
             if not success:
                 logger.error(f"同时移动舵机失败: pan={pan_angle}, tilt={tilt_angle}")
             time.sleep(delay)

        try:
            logger.info("开始执行二维云台自检程序...")
            if update_callback:
                yield update_callback(status="running", message="开始自检...")
            
            # 保存当前位置，以便自检结束后恢复
            original_pan = self.current_pan
            original_tilt = self.current_tilt
            
            # 定义中心位置
            center_pan = SERVO_HARDWARE_CONFIG.get('init_pan_angle', 135.0)
            center_tilt = SERVO_HARDWARE_CONFIG.get('init_tilt_angle', 135.0) # 通常垂直居中是90或135，根据实际舵机

            # 步骤1: 回到中心位置
            logger.info(f"步骤1: 回到中心位置 ({center_pan}, {center_tilt})")
            yield from move_together_and_update(center_pan, center_tilt, delay=1.0)
            
            # 步骤2: 执行"十字"运动
            logger.info("步骤2: 执行十字运动")
            # 定义运动范围 (相对于中心点)
            move_range_pan = 90  # 左右摆动范围
            move_range_tilt = 90 # 上下摆动范围
            # 向上
            yield from move_and_update(self.protocol.TILT_SERVO_PIN, max(0, center_tilt - move_range_tilt), delay=0.8)
            # 回中
            yield from move_and_update(self.protocol.TILT_SERVO_PIN, center_tilt, delay=0.5)
            # 向下
            yield from move_and_update(self.protocol.TILT_SERVO_PIN, min(270, center_tilt + move_range_tilt), delay=0.8)
            # 回中
            yield from move_and_update(self.protocol.TILT_SERVO_PIN, center_tilt, delay=0.5)
            # 向左
            yield from move_and_update(self.protocol.PAN_SERVO_PIN, max(0, center_pan - move_range_pan), delay=0.8)
            # 回中
            yield from move_and_update(self.protocol.PAN_SERVO_PIN, center_pan, delay=0.5)
            # 向右
            yield from move_and_update(self.protocol.PAN_SERVO_PIN, min(270, center_pan + move_range_pan), delay=0.8)
            # 回中
            yield from move_and_update(self.protocol.PAN_SERVO_PIN, center_pan, delay=1.0)
            
            # 步骤3: 执行"圆形"运动 (简化版，8个点)
            logger.info("步骤3: 执行圆形运动")
            radius = 60 # 圆形半径（角度）
            circle_points = []
            for angle_deg in range(0, 360, 45):
                rad = math.radians(angle_deg)
                pan_offset = radius * math.cos(rad)
                tilt_offset = radius * math.sin(rad)
                # 限制角度
                pan = max(0, min(270, center_pan + pan_offset))
                tilt = max(0, min(270, center_tilt - tilt_offset)) # 注意 tilt 角度方向
                circle_points.append((pan, tilt))
            
            for pan, tilt in circle_points:
                yield from move_together_and_update(pan, tilt, delay=0.3)
            # 加一个回到起点的动作，使圆闭合
            yield from move_together_and_update(circle_points[0][0], circle_points[0][1], delay=0.3)
            
            # 回中
            yield from move_together_and_update(center_pan, center_tilt, delay=1.0)
            
            # 步骤4: 执行"Z字形"运动
            logger.info("步骤4: 执行Z字形运动")
            z_range = 75 # Z字形摆动幅度
            z_points = [
                (max(0, center_pan - z_range), max(0, center_tilt - z_range)),    # 左上
                (min(270, center_pan + z_range), max(0, center_tilt - z_range)),   # 右上
                (max(0, center_pan - z_range), center_tilt),   # 左中
                (min(270, center_pan + z_range), min(270, center_tilt + z_range)),  # 右下
                (max(0, center_pan - z_range), min(270, center_tilt + z_range)),   # 左下
            ]
            
            for pan, tilt in z_points:
                yield from move_together_and_update(pan, tilt, delay=0.4)
            
            # 回中
            yield from move_together_and_update(center_pan, center_tilt, delay=1.0)
            
            # 步骤5: 恢复到原始位置
            logger.info("步骤5: 恢复到原始位置")
            yield from move_together_and_update(original_pan, original_tilt, delay=0.5)
            
            logger.info("二维云台自检程序执行完毕")
            if update_callback:
                yield update_callback(status="complete", message="云台自检完成！舵机工作正常", success=True)
            
        except Exception as e:
            logger.error(f"执行云台自检程序时出错: {str(e)}")
            if update_callback:
                yield update_callback(status="error", message=f"自检过程出错: {str(e)}", success=False)
