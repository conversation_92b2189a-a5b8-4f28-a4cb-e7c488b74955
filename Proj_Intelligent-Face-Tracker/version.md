# 该文件用于记录软件的版本变更信息

## 版本 1.1.6
- **feat:** 新增 `face_tracker_server.py`，提供 MCP (Model Context Protocol) 服务，将原有的 RESTful API 封装为标准的 MCP 工具。
- **chore:** 在 `requirements.txt` 中添加 `httpx` 和 `mcp[cli]` 依赖，以支持 MCP 服务器的运行。
- **docs:** 更新 `README.md` (待办)，以包含关于如何运行 MCP 服务器的说明。

## 版本 1.1.5
- **refactor:** 项目名称由 `rdk_yolo_v8_demo` 正式变更为 `IntelligentFaceTracker`，以更好地反映其核心功能。
- **chore:** 新增 `start.sh` 快速启动脚本，用于自动化虚拟环境创建和依赖安装，简化项目启动流程。
- **chore:** 新增 `.gitignore` 文件，以忽略项目中的临时文件和虚拟环境目录。
- **docs:** 修正 `README.md` 中的项目结构图，将 `main.py` 的位置更新至根目录。
- **docs:** 为 `README.md` 补充了缺失的摄像头相关命令行参数说明 (`--camera-path`, `--width`, `--height`)。
- **docs:** 修正 `README.md` 中错误的启动命令，从 `python src/main.py` 更新为 `python main.py`。


## 版本 1.1.4
- 移除 `main.py` 中的 `--camera-id` 命令行参数，统一使用 `--camera-path`。
- 在 `src/config/settings.py` 中新增 `CAMERA_CONFIG` 配置项，用于统一管理摄像头默认路径、分辨率等参数。
- 更新 `main.py` 以从 `CAMERA_CONFIG` 加载摄像头相关参数的默认值。
- 修复 `src/web/app.py` 中Web服务器端口硬编码问题，改为从 `src/config/settings.py` 中的 `FLASK_CONFIG` 读取端口号。

## 版本 1.1.3
- 修改了web/app.py当中人脸检测框的绘制逻辑，现在如果没有开启人脸追踪，就只会显示带有置信度的检测框，不会显示追踪框。

## 版本 1.1.2
- 添加utils当中的Algorithm_README.md，解释说明coordinate.py当中的算法。

## 版本 1.1.1
- 修改utils的README
- 为coordinate.py添加“目标选择策略”，具体信息见utils的README信息。

## 版本 1.1.0

- 为了防止舵机过冲、和静态场景下突然发生震荡的情况，进行了一些修复，增加了一些参数。
1. 目标检测与过滤逻辑
目标选择: 选择置信度最高的目标进行追踪
超时判断: 目标数据超过face_timeout(0.8秒)后重置PID控制器
中心死区: 距离中心点小于center_deadzone(15像素)时暂停追踪
偏移阈值: 偏移量小于movement_threshold(3像素)时不调整舵机
2. PID控制逻辑
首次检测特殊处理: 首次检测使用first_update_factor(0.3)减弱响应
积分项限制: 使用max_integral(20.0)限制积分项，防止积分饱和
大误差特殊处理: 当误差大时积分增益降低(0.3权重)，防止过冲
3. 舵机控制逻辑
角度计算: 通过PID输出乘以angle_adjust_factor(0.05)转换为角度变化
角度限制: 使用max_angle_change(8.0)限制单次角度变化幅度
时间控制: 控制命令间隔不小于min_servo_interval(0.01秒)
同步控制: 水平和垂直舵机同时控制，提高运动平滑性
4. 状态重置逻辑
PID重置: 目标丢失或超时后重置PID状态，避免历史影响
首次检测标记: 目标丢失后恢复首次检测标记
区域检查: 每次循环都重新检查目标是否在死区内
影响追踪效果的关键参数排序
按影响程度从大到小排序：
PID参数(kp, kd): 直接决定响应强度和抑制振荡能力
角度调整因子(angle_adjust_factor): 影响舵机移动幅度
首次检测因子(first_detection_factor): 影响首次响应强度
最大角度变化(max_angle_change): 限制舵机单次移动幅度
中心死区半径(center_deadzone): 控制系统稳定性和灵敏度
移动阈值(movement_threshold): 决定小偏移是否触发调整
舵机控制间隔(min_servo_interval): 影响响应速度

## 版本 1.0.9

- 删除了之前复杂的舵机追踪逻辑，保留：
  - PID控制
  - 首次检测的过冲控制

## 版本 1.0.9

- 删除了无用的README文件

## 版本 1.0.8

- 更新了每个模块文件夹对应的README文档

## 版本 1.0.7 

- 移除了inference_demo.py以及脚本对应的RAEDME文档。保持工作空间的清洁。

## 版本 1.0.6 

- 拆分原来的舵机控制函数servo_controller.py为新的四个文件
  - pid_controller.py：实现PID控制算法，负责计算舵机运动所需的控制量
  - servo_controller.py：主控制器，负责协调各个模块的工作，提供高层控制接口
  - servo_protocol.py：定义与Arduino的通信协议，处理底层串口通信
  - servo_tracking.py：实现人脸跟踪算法，计算目标位置与舵机角度的映射关系

## 版本 1.0.5 

- 修复了人脸自动跟踪功能的多项问题：
  - 修复了"The truth value of an array with more than one element is ambiguous"错误
  - 校正了舵机运动方向，使其正确响应目标位置
  - 优化了PID控制参数(kp=0.05, ki=0.02, kd=0.01)，减少了超调量
  - 添加了时间戳验证机制，防止在没有检测到人脸时舵机自动移动
  - 实现了平滑的初始化过程，避免第一次检测到人脸时舵机突然跳转
  - 提高了舵机运动的流畅度，通过增加调整系数从0.05到0.1

## 版本 1.0.4 

- 添加了基于YOLO模型的人脸自动跟踪功能：
  - 实现了`ServoController`类中的`_tracking_task`方法，支持多线程自动跟踪
  - 集成了PID控制器，实现舵机角度的平滑调整
  - 在Web应用中添加了启用/禁用人脸跟踪的界面控件
  - 增加了`/toggle_tracking`路由处理自动跟踪的开启和关闭
  - 优化了前端JavaScript，支持追踪状态显示和人机交互

## 版本 1.0.3

- 修改了rdk_yolo_v8_demo下inference_demo.py对应的README文件的文件名称，和其他的README命名进行区分

## 版本 1.0.2

- 新增version.md文件，用于记录软件的版本变更信息
- 修改app.py当中_draw_center_and_info函数的显示内容，由中文改为英文，避免乱码问题
- 修改setting.py，调整输出信息等级为INFO

## 版本 1.0.1

新增src文件夹的README文件，解释说明整个src文件夹当中的代码功能

## 版本 1.0.0

目前已经完成的功能：
- 1、人脸检测
- 2、舵机串口控制
- 3、网页端交互
目录结构：

```bash
rdk_yolo_v8_demo
├── inference_demo.py                # 推理演示脚本，用于展示 YOLO 模型的推理功能
├── pic                              # 图片文件夹，存放测试用的图片
│   └── face1.jpg                    # 测试图片，用于模型推理
├── README.md                        # 项目说明文档，包含项目介绍、使用方法和注意事项
├── src                              # 源代码文件夹，包含项目的主要逻辑
│   ├── config                       # 配置文件夹，存放项目的配置文件
│   │   ├── __pycache__              # Python 缓存文件夹，存放编译后的字节码文件
│   │   │   └── settings.cpython-310.pyc  # settings.py 的编译缓存文件
│   │   └── settings.py              # 配置文件，存放项目的全局配置参数
│   ├── __init__.py                  # 初始化文件，将 src 文件夹标记为 Python 包
│   ├── main.py                      # 主程序入口文件，启动项目的核心逻辑
│   ├── models                       # 模型文件夹，存放模型相关的代码
│   │   ├── base_model.py            # 基础模型类，定义模型的通用逻辑
│   │   ├── __pycache__              # Python 缓存文件夹，存放编译后的字节码文件
│   │   │   ├── base_model.cpython-310.pyc  # base_model.py 的编译缓存文件
│   │   │   └── yolo_detect.cpython-310.pyc  # yolo_detect.py 的编译缓存文件
│   │   └── yolo_detect.py           # YOLO 检测模型的具体实现
│   ├── utils                        # 工具文件夹，存放项目中使用到的工具函数
│   │   ├── coordinate.py            # 坐标转换工具，用于处理坐标相关的逻辑
│   │   ├── __pycache__              # Python 缓存文件夹，存放编译后的字节码文件
│   │   │   ├── coordinate.cpython-310.pyc  # coordinate.py 的编译缓存文件
│   │   │   └── servo_control.cpython-310.pyc  # servo_control.py 的编译缓存文件
│   │   └── servo_control.py         # 舵机控制工具，用于控制舵机的运动
│   └── web                          # Web 应用文件夹，存放与 Web 相关的代码
│       ├── app.py                   # Web 应用的主程序文件，启动 Web 服务
│       ├── __pycache__              # Python 缓存文件夹，存放编译后的字节码文件
│       │   └── app.cpython-310.pyc  # app.py 的编译缓存文件
│       ├── static                   # 静态资源文件夹，存放 CSS、JS、HTML 等静态文件
│       │   ├── css                  # CSS 文件夹，存放样式文件
│       │   │   └── style.css        # 样式文件，定义 Web 页面的样式
│       │   ├── debug.html           # 调试页面，用于调试和测试 Web 功能
│       │   ├── display.html         # 显示页面，用于展示模型的推理结果
│       │   ├── js                   # JavaScript 文件夹，存放前端逻辑代码
│       │   │   ├── servo-3d.js      # 3D 舵机控制逻辑，用于可视化舵机运动
│       │   │   ├── servo-control.js # 舵机控制逻辑，用于控制舵机
│       │   │   └── test-stl.js      # STL 文件测试逻辑，用于测试 3D 模型加载
│       │   └── models               # 3D 模型文件夹，存放 STL 格式的 3D 模型文件
│       │       ├── base.stl         # 基础 3D 模型文件
│       │       ├── pan.stl          # 水平舵机 3D 模型文件
│       │       ├── README.md        # 3D 模型说明文档
│       │       └── tilt.stl         # 垂直舵机 3D 模型文件
│       └── templates                # 模板文件夹，存放 HTML 模板文件
│           ├── disabled.html        # 禁用页面模板，用于显示禁用状态
│           └── index.html           # 首页模板，Web 应用的默认首页
└── yolo11n_face_detect_bayese_320x320_nv12.bin  # YOLO 模型文件，用于人脸检测的二进制模型~
```   

主要功能说明：
- inference_demo.py: 用于演示 YOLO 模型的推理功能，加载模型并对图片进行检测。
- src/main.py: 项目的主程序入口，启动整个应用的核心逻辑。
- src/models/yolo_detect.py: 实现 YOLO 模型的具体检测逻辑。
- src/utils/servo_control.py: 控制舵机运动的工具函数。
- src/web/app.py: 启动 Web 服务，提供前端页面和 API 接口。
- src/web/static/js/servo-control.js: 前端 JavaScript 代码，用于控制舵机。
- src/web/templates/index.html: Web 应用的首页模板。~