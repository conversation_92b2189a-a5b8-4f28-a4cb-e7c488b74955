# LLM Vision Control MCP服务器配置文件
# LLM Vision Control MCP Server Configuration

# Web服务配置
web:
  host: "0.0.0.0"          # 监听地址，0.0.0.0表示监听所有接口
  port: 5003               # Web服务端口
  debug: false             # 是否启用调试模式

# MCP服务配置
mcp:
  name: "llm-vision-control"  # MCP服务名称
  host: "0.0.0.0"             # MCP服务监听地址
  port: 8003                  # MCP服务端口
  timeout: 30.0               # HTTP客户端超时时间（秒）

# 摄像头配置
camera:
  path: "/dev/video0"      # 摄像头设备路径
  width: 640               # 图像宽度
  height: 480              # 图像高度
  fps: 30                  # 帧率

# 大语言模型配置
llm:
  api_key: ""              # SiliconFlow API密钥（从环境变量SILICONFLOW_API_KEY读取）
  api_url: "https://api.siliconflow.cn/v1/chat/completions"  # API地址
  model_name: "Qwen/Qwen2-VL-72B-Instruct"  # 模型名称
  max_tokens: 1024         # 最大token数
  temperature: 0.1         # 温度参数
  default_prompt: "请仔细观察图片中的所有主要物体，并使用JSON格式返回它们的边界框坐标。格式：{\"coordinates\": [[x1, y1, x2, y2], ...]}"

# 日志配置
logging:
  level: "INFO"            # 日志级别：DEBUG, INFO, WARNING, ERROR
  format: "[%(name)s] [%(asctime)s] [%(levelname)s] %(message)s"  # 日志格式
  date_format: "%Y-%m-%d %H:%M:%S"  # 时间格式
  file_path: null          # 日志文件路径，null表示不写入文件
  max_file_size: 10485760  # 日志文件最大大小（字节），10MB
  backup_count: 5          # 日志文件备份数量
