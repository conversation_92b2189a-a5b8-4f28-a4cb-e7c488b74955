# LLM Vision Control MCP服务器

基于大语言模型的智能视觉控制系统 - FastMCP服务器版本

## 概述

这是一个基于FastMCP协议的智能视觉系统，提供：

- **实时视频流**: 摄像头实时视频流和MJPEG输出
- **AI视觉理解**: 基于Qwen2.5-VL多模态大语言模型的物体检测
- **Web API**: RESTful API接口，支持按需检测和状态查询
- **MCP协议支持**: 可与AI助手（如Claude、ChatGPT等）无缝集成
- **统一服务器架构**: 同时运行Web服务和MCP服务

## 架构特点

### 新架构优势
- **双服务架构**: Web服务(Flask) + MCP服务(FastMCP)同时运行
- **模块化设计**: 清晰的包结构，易于维护和扩展
- **配置管理**: 基于YAML文件和环境变量的灵活配置
- **异步支持**: MCP服务支持异步操作，提高性能
- **线程安全**: 多线程环境下的数据共享和同步

### 与原版本对比
| 特性 | 原版本 | MCP版本 |
|------|--------|---------|
| 协议支持 | 仅HTTP API | HTTP API + MCP协议 |
| AI助手集成 | 需要手动调用API | 原生MCP工具支持 |
| 服务架构 | 单一Flask应用 | 统一服务器(Web+MCP) |
| 配置管理 | 环境变量 | YAML + 环境变量 |
| 异步支持 | 无 | 完整异步支持 |

## 快速开始

### 1. 环境准备

```bash
# 克隆项目（如果需要）
cd /home/<USER>/Desktop/proj_llm-vision-control

# 创建虚拟环境（推荐）
python3 -m venv venv
source venv/bin/activate

# 安装MCP版本依赖
pip install -r requirements_mcp.txt
```

### 2. 配置设置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量，设置API密钥
nano .env
```

在`.env`文件中设置：
```bash
SILICONFLOW_API_KEY=your_actual_api_key_here
```

### 3. 启动服务器

#### 方法1: 使用启动脚本（推荐）
```bash
./bin/start-mcp-server.sh
```

#### 方法2: 直接运行
```bash
# 使用默认配置
python3 main.py

# 或使用自定义配置
python3 main.py --config config/custom.yaml
```

#### 方法3: 使用模块方式
```bash
python3 -m llm_vision_mcp.main --config config/default.yaml
```

### 4. 访问服务

启动后，您将看到类似输出：
```
LLM Vision Control MCP服务器启动完成
==================================================
服务访问地址:
  Web界面 (本地): http://127.0.0.1:5003
  MCP服务 (本地): http://127.0.0.1:8003/mcp/
  局域网访问地址:
    Web界面: http://*************:5003 (主要)
    MCP服务: http://*************:8003/mcp/ (主要)
==================================================
```

## API接口

### Web API端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/` | GET | 服务信息和端点列表 |
| `/video_feed` | GET | 实时视频流(MJPEG) |
| `/api/detect` | POST | 按需物体检测 |
| `/api/measurements` | GET | 获取最新检测结果 |
| `/api/status` | GET | 获取系统状态 |
| `/api/health` | GET | 健康检查 |

### MCP工具函数

| 工具名称 | 描述 |
|----------|------|
| `detect_objects` | 检测图像中的物体 |
| `get_latest_measurements` | 获取最新检测结果 |
| `get_system_status` | 获取系统状态 |
| `get_video_stream_url` | 获取视频流URL |

## 配置说明

### 配置文件结构 (`config/default.yaml`)

```yaml
# Web服务配置
web:
  host: "0.0.0.0"
  port: 5003
  debug: false

# MCP服务配置  
mcp:
  name: "llm-vision-control"
  host: "0.0.0.0"
  port: 8003
  timeout: 30.0

# 摄像头配置
camera:
  path: "/dev/video0"
  width: 640
  height: 480
  fps: 30

# LLM配置
llm:
  api_key: ""  # 从环境变量读取
  api_url: "https://api.siliconflow.cn/v1/chat/completions"
  model_name: "Qwen/Qwen2-VL-72B-Instruct"
  max_tokens: 1024
  temperature: 0.1
  default_prompt: "请仔细观察图片..."

# 日志配置
logging:
  level: "INFO"
  format: "[%(name)s] [%(asctime)s] [%(levelname)s] %(message)s"
  file_path: null  # 不写入文件
```

### 环境变量覆盖

可以通过环境变量覆盖配置文件中的设置：

```bash
# API密钥（必需）
SILICONFLOW_API_KEY=your_key

# 服务端口
WEB_PORT=5003
MCP_PORT=8003

# 摄像头设置
CAMERA_PATH=/dev/video0
CAMERA_WIDTH=640
CAMERA_HEIGHT=480

# 日志设置
LOG_LEVEL=DEBUG
LOG_FILE_PATH=logs/app.log
```

## 与AI助手集成

### MCP客户端配置示例

在支持MCP的AI助手中添加服务器配置：

```json
{
  "mcpServers": {
    "llm-vision-control": {
      "command": "curl",
      "args": [
        "-X", "POST",
        "http://localhost:8003/mcp/",
        "-H", "Content-Type: application/json"
      ],
      "transport": "sse"
    }
  }
}
```

### 使用示例

在AI助手中，您可以直接使用以下命令：

```
请帮我检测当前摄像头画面中的物体
```

AI助手将自动调用`detect_objects`工具并返回检测结果。

## 故障排除

### 常见问题

1. **摄像头无法打开**
   - 检查摄像头设备路径：`ls /dev/video*`
   - 确保摄像头未被其他程序占用
   - 检查权限：`sudo chmod 666 /dev/video0`

2. **API密钥错误**
   - 确保在`.env`文件中正确设置了`SILICONFLOW_API_KEY`
   - 验证API密钥是否有效

3. **端口被占用**
   - 修改配置文件中的端口号
   - 或使用环境变量：`WEB_PORT=5004 MCP_PORT=8004`

4. **依赖安装失败**
   - 确保使用Python 3.8+
   - 尝试升级pip：`pip install --upgrade pip`
   - 使用虚拟环境避免冲突

### 日志调试

启用详细日志：
```bash
LOG_LEVEL=DEBUG python3 main.py
```

或在配置文件中设置：
```yaml
logging:
  level: "DEBUG"
  file_path: "logs/debug.log"
```

## 开发说明

### 项目结构

```
proj_llm-vision-control/
├── llm_vision_mcp/           # MCP包
│   ├── config/               # 配置管理
│   ├── core/                 # 核心功能
│   ├── services/             # 服务层
│   ├── api/                  # API路由
│   ├── utils/                # 工具函数
│   └── main.py               # 统一服务器入口
├── config/                   # 配置文件
├── bin/                      # 启动脚本
├── src/                      # 原版本代码（保留）
├── main.py                   # 新版本入口
├── requirements_mcp.txt      # MCP版本依赖
└── README_MCP.md            # 本文档
```

### 扩展开发

要添加新的MCP工具函数：

1. 在`llm_vision_mcp/services/mcp_service.py`中添加工具
2. 使用`@self.mcp.tool`装饰器注册
3. 重启服务器使更改生效

示例：
```python
@self.mcp.tool
async def new_tool(param: str) -> Dict[str, Any]:
    """新工具函数"""
    # 实现逻辑
    return {"result": "success"}
```

## 版本信息

- **版本**: 2.0.0 (MCP版本)
- **基于**: FastMCP框架
- **兼容性**: 保持与原版本API的完全兼容
- **Python要求**: 3.8+

## 许可证

与原项目相同的许可证。
