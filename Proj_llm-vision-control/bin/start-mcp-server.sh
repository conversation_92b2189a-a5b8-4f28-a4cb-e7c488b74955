#!/bin/bash
# LLM Vision Control MCP服务器启动脚本
# LLM Vision Control MCP Server Start Script

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "LLM Vision Control MCP服务器启动脚本"
echo "项目根目录: $PROJECT_ROOT"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到python3命令"
    exit 1
fi

# 进入项目根目录
cd "$PROJECT_ROOT"

# 检查依赖文件
if [ ! -f "requirements_mcp.txt" ]; then
    echo "错误: 未找到requirements_mcp.txt文件"
    exit 1
fi

# 检查是否存在虚拟环境
if [ -d "venv" ]; then
    echo "激活虚拟环境..."
    source venv/bin/activate
elif [ -d ".venv" ]; then
    echo "激活虚拟环境..."
    source .venv/bin/activate
else
    echo "警告: 未找到虚拟环境，使用系统Python"
fi

# 安装依赖（如果需要）
echo "检查依赖..."
pip install -r requirements_mcp.txt

# 检查环境变量
if [ -f ".env" ]; then
    echo "发现.env文件，将加载环境变量"
else
    echo "警告: 未找到.env文件，请确保已设置必要的环境变量"
fi

# 设置Python路径
export PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH"

# 启动服务器
echo "启动LLM Vision Control MCP服务器..."
echo "按Ctrl+C停止服务器"
echo "================================"

# 解析命令行参数
CONFIG_FILE=""
if [ "$1" = "--config" ] || [ "$1" = "-c" ]; then
    if [ -n "$2" ]; then
        CONFIG_FILE="--config $2"
        echo "使用配置文件: $2"
    else
        echo "错误: --config参数需要指定配置文件路径"
        exit 1
    fi
elif [ -f "config/default.yaml" ]; then
    CONFIG_FILE="--config config/default.yaml"
    echo "使用默认配置文件: config/default.yaml"
fi

# 启动服务器
python3 -m llm_vision_mcp.main $CONFIG_FILE
