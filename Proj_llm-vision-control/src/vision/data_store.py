import time
import threading
from typing import List, Dict, Any, Optional

class DataStore:
    """
    A thread-safe in-memory store for detection results.
    """
    def __init__(self):
        self._lock = threading.Lock()
        self._last_update_time: float = 0.0
        self._last_prompt: str = ""
        self._last_inference_time: float = 0.0
        self._last_coordinates: List[List[int]] = []
        self._last_description: str = ""

    def update(self, 
               prompt: str, 
               coordinates: List[List[int]], 
               description: str,
               inference_time: float):
        """
        Update the store with new detection results.
        """
        with self._lock:
            self._last_prompt = prompt
            self._last_coordinates = coordinates
            self._last_description = description
            self._last_inference_time = inference_time
            self._last_update_time = time.time()

    def get_latest(self) -> Dict[str, Any]:
        """
        Get the latest detection results.
        """
        with self._lock:
            return {
                "prompt": self._last_prompt,
                "coordinates": self._last_coordinates,
                "description": self._last_description,
                "inference_time": self._last_inference_time,
                "last_update_timestamp": self._last_update_time,
                "detection_count": len(self._last_coordinates)
            }

    def get_coordinates(self) -> List[List[int]]:
        """
        Get only the latest coordinates.
        """
        with self._lock:
            return self._last_coordinates.copy()

# Global instance of the data store
data_store = DataStore()