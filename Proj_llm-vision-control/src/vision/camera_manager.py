import cv2
import threading
import time
import logging
from typing import Optional, <PERSON>ple
import numpy as np

from src.config import config

logger = logging.getLogger(__name__)

class CameraManager:
    """
    Manages the camera connection and frame grabbing in a separate thread.
    """
    def __init__(self):
        self._camera_path = config.CAMERA_PATH
        self._width = config.FRAME_WIDTH
        self._height = config.FRAME_HEIGHT
        
        self._camera: Optional[cv2.VideoCapture] = None
        self._latest_frame: Optional[np.ndarray] = None
        self._frame_lock = threading.Lock()
        self._is_running = False
        self._thread: Optional[threading.Thread] = None

    def start(self):
        """
        Starts the camera and the frame grabbing thread.
        """
        if self._is_running:
            logger.warning("Camera manager is already running.")
            return

        try:
            logger.info(f"Opening camera at: {self._camera_path}")
            self._camera = cv2.VideoCapture(self._camera_path)
            if not self._camera.isOpened():
                raise RuntimeError(f"Could not open camera at {self._camera_path}")

            self._camera.set(cv2.CAP_PROP_FRAME_WIDTH, self._width)
            self._camera.set(cv2.CAP_PROP_FRAME_HEIGHT, self._height)
            
            actual_width = self._camera.get(cv2.CAP_PROP_FRAME_WIDTH)
            actual_height = self._camera.get(cv2.CAP_PROP_FRAME_HEIGHT)
            logger.info(f"Camera opened. Requested: {self._width}x{self._height}, Actual: {int(actual_width)}x{int(actual_height)}")

            self._is_running = True
            self._thread = threading.Thread(target=self._run, daemon=True)
            self._thread.start()
            logger.info("Camera manager started.")

        except Exception as e:
            logger.error(f"Failed to start camera manager: {e}")
            self._is_running = False
            if self._camera:
                self._camera.release()

    def _run(self):
        """
        The main loop for the frame grabbing thread.
        """
        while self._is_running:
            if self._camera:
                ret, frame = self._camera.read()
                if ret:
                    with self._frame_lock:
                        self._latest_frame = frame
                else:
                    logger.warning("Failed to grab frame from camera.")
                    time.sleep(0.1)
            else:
                time.sleep(0.1)

    def get_latest_frame(self) -> Optional[np.ndarray]:
        """
        Returns the most recent frame from the camera.
        """
        with self._frame_lock:
            if self._latest_frame is not None:
                return self._latest_frame.copy()
        return None

    def stop(self):
        """
        Stops the frame grabbing thread and releases the camera.
        """
        logger.info("Stopping camera manager...")
        self._is_running = False
        if self._thread:
            self._thread.join()
        
        if self._camera:
            self._camera.release()
        
        logger.info("Camera manager stopped.")

# Global instance of the camera manager
camera_manager = CameraManager()