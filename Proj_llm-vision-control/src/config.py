import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """
    Application configuration class.
    Loads settings from environment variables.
    """
    # SiliconFlow API and Model Configuration
    SILICONFLOW_API_KEY = os.getenv("SILICONFLOW_API_KEY")
    SILICONFLOW_API_URL = os.getenv("SILICONFLOW_API_URL", "https://api.siliconflow.cn/v1/chat/completions")
    MODEL_NAME = os.getenv("MODEL_NAME", "Qwen/Qwen2-VL-72B-Instruct")

    # Web Server Configuration
    HOST = os.getenv("HOST", "0.0.0.0")
    PORT = int(os.getenv("PORT", 5003))

    # Camera Configuration
    CAMERA_PATH = os.getenv("CAMERA_PATH", "/dev/video0")
    FRAME_WIDTH = int(os.getenv("FRAME_WIDTH", 640))
    FRAME_HEIGHT = int(os.getenv("FRAME_HEIGHT", 480))

    # Default prompt for detection
    DEFAULT_PROMPT = "请仔细观察图片中的所有主要物体，并使用JSON格式返回它们的边界框坐标。"

# Instantiate the config
config = Config()