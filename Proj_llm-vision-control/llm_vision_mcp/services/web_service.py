#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Web服务
Web Service

基于Flask的Web服务，提供：
- 实时视频流
- 检测API接口
- 系统状态查询
"""

import logging
import threading
import time
from typing import Optional

from flask import Flask
from flask_cors import CORS

from ..config.settings import Settings
from ..core.vision_system import VisionSystem

logger = logging.getLogger("WebService")


class WebService:
    """Web服务类"""
    
    def __init__(self, config: Settings):
        """
        初始化Web服务
        
        Args:
            config: 系统配置
        """
        self.config = config
        self.vision_system: Optional[VisionSystem] = None
        self.app: Optional[Flask] = None
        self.running = False
        
        # 初始化视觉系统
        self._initialize_vision_system()
        
        # 初始化Flask应用
        self._initialize_app()
        
        logger.info("Web服务初始化完成")
    
    def _initialize_vision_system(self):
        """初始化视觉系统"""
        try:
            self.vision_system = VisionSystem(self.config)
            logger.info("视觉系统初始化成功")
        except Exception as e:
            logger.error(f"视觉系统初始化失败: {e}")
            raise
    
    def _initialize_app(self):
        """初始化Flask应用"""
        self.app = Flask(__name__)
        CORS(self.app)
        
        # 注册路由
        self._register_routes()
        
        logger.info("Flask应用初始化完成")
    
    def _register_routes(self):
        """注册路由"""
        from ..api.routes import register_routes
        register_routes(self.app, self.vision_system)
    
    def start(self):
        """启动Web服务"""
        if self.running:
            logger.warning("Web服务已在运行")
            return
        
        try:
            # 启动视觉系统
            self.vision_system.start()
            
            # 等待摄像头初始化
            time.sleep(2)
            
            self.running = True
            logger.info("Web服务启动成功")
            
        except Exception as e:
            logger.error(f"Web服务启动失败: {e}")
            raise
    
    def run(self):
        """运行Web服务（阻塞）"""
        if not self.running:
            self.start()
        
        try:
            logger.info(f"Flask服务器启动: http://{self.config.web.host}:{self.config.web.port}")
            self.app.run(
                host=self.config.web.host,
                port=self.config.web.port,
                debug=self.config.web.debug,
                threaded=True,
                use_reloader=False  # 避免在多线程环境中重载
            )
        except Exception as e:
            logger.error(f"Flask服务器运行错误: {e}")
            raise
    
    def stop(self):
        """停止Web服务"""
        if not self.running:
            return
        
        try:
            # 停止视觉系统
            if self.vision_system:
                self.vision_system.stop()
            
            self.running = False
            logger.info("Web服务已停止")
            
        except Exception as e:
            logger.error(f"停止Web服务时出错: {e}")
    
    def get_status(self) -> dict:
        """获取服务状态"""
        if not self.vision_system:
            return {"status": "not_initialized"}
        
        return {
            "status": "running" if self.running else "stopped",
            "camera_running": self.vision_system.camera_manager.is_running(),
            "last_detection": self.vision_system.data_store.get_latest()
        }
