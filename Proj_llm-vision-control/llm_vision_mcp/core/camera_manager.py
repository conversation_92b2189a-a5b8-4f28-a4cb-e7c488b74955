#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
摄像头管理器
Camera Manager

负责摄像头的初始化、帧捕获和资源管理
"""

import cv2
import logging
import threading
import time
from typing import Optional
import numpy as np

from ..config.settings import CameraConfig

logger = logging.getLogger("CameraManager")


class CameraManager:
    """摄像头管理器"""
    
    def __init__(self, config: CameraConfig):
        """
        初始化摄像头管理器
        
        Args:
            config: 摄像头配置
        """
        self.config = config
        self.cap: Optional[cv2.VideoCapture] = None
        self.latest_frame: Optional[np.ndarray] = None
        self.frame_lock = threading.Lock()
        self.capture_thread: Optional[threading.Thread] = None
        self.running = False
        
        logger.info(f"摄像头管理器初始化完成，设备路径: {config.path}")
    
    def start(self):
        """启动摄像头"""
        if self.running:
            logger.warning("摄像头已在运行")
            return
        
        try:
            # 初始化摄像头
            self.cap = cv2.VideoCapture(self.config.path)
            if not self.cap.isOpened():
                raise RuntimeError(f"无法打开摄像头: {self.config.path}")
            
            # 设置摄像头参数
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.config.width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.config.height)
            self.cap.set(cv2.CAP_PROP_FPS, self.config.fps)
            
            # 获取实际设置的参数
            actual_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            actual_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            actual_fps = self.cap.get(cv2.CAP_PROP_FPS)
            
            logger.info(f"摄像头参数设置完成: {actual_width}x{actual_height}@{actual_fps}fps")
            
            # 启动捕获线程
            self.running = True
            self.capture_thread = threading.Thread(target=self._capture_loop, daemon=True)
            self.capture_thread.start()
            
            logger.info("摄像头启动成功")
            
        except Exception as e:
            logger.error(f"摄像头启动失败: {e}")
            self.stop()
            raise
    
    def stop(self):
        """停止摄像头"""
        if not self.running:
            return
        
        try:
            self.running = False
            
            # 等待捕获线程结束
            if self.capture_thread and self.capture_thread.is_alive():
                self.capture_thread.join(timeout=5)
            
            # 释放摄像头资源
            if self.cap:
                self.cap.release()
                self.cap = None
            
            # 清空最新帧
            with self.frame_lock:
                self.latest_frame = None
            
            logger.info("摄像头已停止")
            
        except Exception as e:
            logger.error(f"停止摄像头时出错: {e}")
    
    def _capture_loop(self):
        """摄像头捕获循环"""
        logger.info("摄像头捕获线程启动")
        
        while self.running and self.cap and self.cap.isOpened():
            try:
                ret, frame = self.cap.read()
                if ret and frame is not None:
                    # 更新最新帧
                    with self.frame_lock:
                        self.latest_frame = frame.copy()
                else:
                    logger.warning("摄像头读取帧失败")
                    time.sleep(0.1)
                    
            except Exception as e:
                logger.error(f"摄像头捕获错误: {e}")
                time.sleep(0.1)
        
        logger.info("摄像头捕获线程结束")
    
    def get_latest_frame(self) -> Optional[np.ndarray]:
        """
        获取最新帧
        
        Returns:
            最新的图像帧，如果没有则返回None
        """
        with self.frame_lock:
            if self.latest_frame is not None:
                return self.latest_frame.copy()
            return None
    
    def is_running(self) -> bool:
        """
        检查摄像头是否在运行
        
        Returns:
            True如果摄像头在运行，否则False
        """
        return self.running and self.cap is not None and self.cap.isOpened()
    
    def get_frame_info(self) -> dict:
        """
        获取帧信息
        
        Returns:
            包含帧信息的字典
        """
        if not self.cap:
            return {"error": "摄像头未初始化"}
        
        return {
            "width": int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
            "height": int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
            "fps": self.cap.get(cv2.CAP_PROP_FPS),
            "running": self.is_running(),
            "has_frame": self.latest_frame is not None
        }
