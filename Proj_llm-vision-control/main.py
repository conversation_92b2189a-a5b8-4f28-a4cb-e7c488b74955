#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
LLM Vision Control System - MCP服务器版本
基于大语言模型的智能视觉控制系统 - FastMCP服务器

主要功能：
1. 实时摄像头视频流
2. AI视觉理解和物体检测
3. Web界面和API接口
4. MCP协议支持，可与AI助手集成
5. 统一服务器架构

作者: Assistant
版本: 2.0.0 (MCP版本)
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from llm_vision_mcp.main import main as mcp_main

def main():
    """主函数 - 启动MCP服务器"""
    print("LLM Vision Control System - MCP服务器版本")
    print("=" * 50)
    print("正在启动统一服务器（Web + MCP）...")
    print("=" * 50)

    # 运行异步主函数
    asyncio.run(mcp_main())

if __name__ == "__main__":
    main()