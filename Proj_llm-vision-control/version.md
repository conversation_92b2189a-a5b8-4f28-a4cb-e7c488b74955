# 更新日志

记录更新内容，提交的时候无需将日期一起放入commit当中。
 
**更新类型说明：**
- 类型(type)：
  - feat: 功能变更（新增/修改功能）
  - remove: 删除内容（文件/代码/资源）
  - refactor: 代码重构（结构调整/优化）
  - docs: 文档相关（README/注释/手册）  
  - fix: 问题修复（BUG/逻辑错误）
  - ui: 界面调整（布局/样式/交互）
  - chore: 日常维护（构建/配置/清理）
  - test: 测试相关（用例/框架）

项目版本号管理说明：
本项目采用语义化版本控制（Semantic Versioning）的概念进行版本管理，并结合自定义的递增规则，以反映项目的开发进度和功能变化。版本号格式为：V{MAJOR}.{MINOR}.{PATCH}.{BUILD}。

**版本号组成说明：**

*   **MAJOR (主版本号)**：
    *   当进行了**不兼容的 API 修改**或**引入了重大新功能**时，主版本号会递增。这通常意味着需要对依赖此版本的外部组件或用户进行修改才能兼容。
    *   **递增时机：** 当有全局性的、底层架构的、或对外部接口/用户体验有重大影响的不兼容变更时。

*   **MINOR (次版本号)**：
    *   当以**向下兼容**的方式添加了**新功能**或进行了**功能性改进**时，次版本号会递增。
    *   **递增时机：** 当添加了新的功能模块、新的 API 端点、或者对现有功能进行了显著的、向下兼容的增强时。

*   **PATCH (补丁版本号)**：
    *   当进行了**向下兼容的问题修复**时，补丁版本号会递增。
    *   **递增时机：** 当修复了 BUG、逻辑错误、UI 问题等不引入新功能且不破坏兼容性的变更时。

*   **BUILD (构建版本号)**：
    *   此版本号用于区分在**同一个 PATCH 版本号下**进行的**多次提交**。通常用于**同一天**内进行的小幅迭代、测试或连续的 BUG 修复，但这些变更不足以独立提升 PATCH 版本号。
    *   **递增时机：** 当在同一天内，对代码进行了多次小幅度的修改、调试或连续的提交，这些变更属于同一个 PATCH 版本范围，且希望区分每次具体的提交时。 BUILD 版本号会从 `1` 开始递增。

**版本号递增规则总结：**

1.  **MAJOR 递增时：** MINOR、PATCH 和 BUILD 版本号**重置为 0**。
2.  **MINOR 递增时：** PATCH 和 BUILD 版本号**重置为 0**。
3.  **PATCH 递增时：** BUILD 版本号**重置为 0**。
4.  **BUILD 递增时：** 仅 BUILD 版本号递增。

**示例：**

*   `V1.0.0` -> `V2.0.0` (重大不兼容变更)
*   `V1.0.0` -> `V1.1.0` (新增向下兼容功能)
*   `V1.0.0` -> `V1.0.1` (修复向下兼容 BUG)
*   `V1.0.1` -> `V1.0.1.1` (同一天在 V1.0.1 版本下进行了小改动)
*   `V1.0.1.1` -> `V1.0.1.2` (同一天在 V1.0.1 版本下又进行了小改动)
*   `V1.0.1.2` -> `V1.0.2` (修复了另一个 BUG，或进入了新的一天进行 PATCH 修复)
*   `V1.0.2` -> `V1.1.0` (添加了新的向下兼容功能)

---

V2.0.0 feat(mcp): 完整重构为FastMCP服务器架构，实现统一Web+MCP双服务模式

日期：2025-08-03

**类型**: `feat`
**范围**: `mcp`

**说明**:
本次提交完成了 `llm-vision-control` 项目从传统Flask应用到FastMCP服务器的完整架构重构。新架构实现了Web服务和MCP服务的统一管理，支持通过MCP协议为AI助手提供视觉控制能力，同时保持原有的Web API功能完整性。

**变更内容**:

*   **1. 创建MCP包架构 (`feat`, `architecture`)**:
    *   **文件**: `llm_vision_mcp/`
    *   **变更**: 创建了完整的MCP包结构，包括 `config`, `core`, `services`, `api`, `utils` 等模块，实现了清晰的模块化架构和职责分离。

*   **2. 实现统一服务器管理 (`feat`, `server`)**:
    *   **文件**: `llm_vision_mcp/main.py`
    *   **变更**: 实现了 `UnifiedServer` 类，统一管理Web服务（后台线程）和MCP服务（主线程异步），支持优雅启动和停止，包含完整的服务生命周期管理。

*   **3. 构建MCP服务层 (`feat`, `mcp`)**:
    *   **文件**: `llm_vision_mcp/services/mcp_service.py`
    *   **变更**: 基于FastMCP框架实现MCP服务，提供4个核心工具函数：
        *   `detect_objects`: 物体检测功能
        *   `get_latest_measurements`: 获取最新检测数据
        *   `get_system_status`: 系统状态查询
        *   `get_video_stream_url`: 获取视频流地址

*   **4. 重构Web服务架构 (`refactor`, `web`)**:
    *   **文件**: `llm_vision_mcp/services/web_service.py`, `llm_vision_mcp/api/routes.py`
    *   **变更**: 将原有Flask应用重构为服务类封装，保持所有原有API端点功能，并优化了路由注册和错误处理机制。

*   **5. 实现配置管理系统 (`feat`, `config`)**:
    *   **文件**: `llm_vision_mcp/config/`, `config/default.yaml`
    *   **变更**: 创建了基于YAML文件和环境变量的分层配置系统，支持深度合并、环境变量优先级和配置验证，确保API密钥正确加载。

*   **6. 重构核心视觉模块 (`refactor`, `vision`)**:
    *   **文件**: `llm_vision_mcp/core/`
    *   **变更**: 将原有的摄像头管理、检测引擎、数据存储等模块重构为新的包结构，保持功能完整性的同时提高了模块间的解耦度。

*   **7. 创建启动脚本和文档 (`chore`, `deployment`)**:
    *   **文件**: `bin/start-mcp-server.sh`, `README.md`, `.env.example`
    *   **变更**: 提供了MCP服务器启动脚本，更新了项目文档和环境变量模板，支持快速部署和配置。

*   **8. 更新项目入口和依赖 (`chore`, `main`)**:
    *   **文件**: `main.py`, `requirements.txt`
    *   **变更**: 更新了项目主入口点以调用MCP服务器，添加了FastMCP、httpx、PyYAML等新依赖，移除了冗余的MCP专用依赖文件。

**技术特性**:
*   **双服务架构**: 同时运行Web服务和MCP服务，Web服务在后台线程运行，MCP服务在主线程异步运行。
*   **MCP协议支持**: 完整实现MCP协议，支持AI助手通过标准化接口调用视觉控制功能。
*   **异步通信**: MCP服务通过异步HTTP客户端与Web服务通信，确保高性能和响应性。
*   **配置优先级**: 环境变量优先于配置文件，确保敏感信息（如API密钥）的正确加载。
*   **服务发现**: 启动时自动显示所有可用的访问地址，包括本地和局域网地址，以及视频流端点。
*   **优雅关闭**: 支持信号处理和优雅关闭，确保资源正确释放。
*   **向后兼容**: 保持所有原有Web API功能，确保现有客户端无需修改即可继续使用。

**破坏性变更**:
*   项目架构从单一Flask应用重构为MCP服务器，主入口点变更为MCP服务启动。
*   配置系统从纯环境变量改为YAML+环境变量的混合模式。
*   包结构从 `src/` 改为 `llm_vision_mcp/`，需要更新导入路径。

---

V1.0.0 feat(vision): 初始化LLM视觉控制系统，实现按需检测核心框架

日期：2025-07-22

**类型**: `feat`
**范围**: `vision`

**说明**:
本次提交完成了 `llm-vision-control` 项目的从零到一的搭建。该项目是一个基于Qwen2.5-VL多模态大语言模型的智能视觉系统，实现了通过API按需触发、利用自然语言提示词动态检测视频流中物体的核心功能。

**变更内容**:

*   **1. 建立项目核心架构 (`feat`, `core`)**:
    *   **文件**: `llm-vision-control/`
    *   **变更**: 创建了完整的项目目录结构，包括 `src` 目录下的 `config`, `utils`, `vision`, `web` 等模块，奠定了清晰的模块化基础。

*   **2. 实现按需检测引擎 (`feat`, `detection`)**:
    *   **文件**: `src/vision/detection_engine.py`
    *   **变更**: 实现了与Qwen2.5-VL模型交互的核心检测逻辑。功能包括将图像编码为Base64、构造API请求、调用云端模型、并使用正则表达式从自然语言响应中智能解析出坐标数据。

*   **3. 构建实时视频与API服务 (`feat`, `web`)**:
    *   **文件**: `src/web/app.py`
    *   **变更**: 使用Flask构建了Web服务器，提供了多个核心API端点：
        *   `POST /api/detect`: 按需触发检测的核心接口。
        *   `GET /video_feed`: 提供带有最新检测结果的实时MJPEG视频流。
        *   `GET /api/measurements`: 获取最新一次的检测数据。
        *   `GET /api/status`: 查询系统运行状态。

*   **4. 实现摄像头与数据管理 (`feat`, `manager`)**:
    *   **文件**: `src/vision/camera_manager.py`, `src/vision/data_store.py`
    *   **变更**: 创建了在后台线程中独立运行的摄像头管理器，并实现了线程安全的内存数据存储，用于在各模块间共享检测结果。

*   **5. 创建可视化工具 (`feat`, `utils`)**:
    *   **文件**: `src/utils/visualization.py`
    *   **变更**: 实现了在视频帧上绘制边界框的可视化功能，能够正确处理并转换来自Qwen2.5-VL的1000x1000标准化坐标。

*   **6. 配置与环境管理 (`chore`, `config`)**:
    *   **文件**: `requirements.txt`, `.env.example`, `src/config.py`, `.gitignore`
    *   **变更**: 创建了项目依赖文件、环境变量模板和配置加载模块，并通过 `.gitignore` 保证了仓库的整洁。

*   **7. 建立项目入口与文档 (`docs`, `main`)**:
    *   **文件**: `main.py`, `README.md`
    *   **变更**: 提供了项目的主启动入口 `main.py`，并编写了详细的 `README.md` 文档，全面介绍了项目功能、架构、安装方法和API用法。

**技术特性**:
*   **按需检测**: 核心架构基于API调用触发，有效控制大模型API的资源消耗和成本。
*   **动态提示词**: 允许用户通过API实时改变检测目标，具有极高的灵活性。
*   **异步可视化**: 检测结果异步更新到实时视频流中，保证了用户体验的流畅性。
*   **标准化坐标处理**: 正确实现了Qwen2.5-VL模型1000x1000标准化坐标到图像像素坐标的转换。
*   **模块化设计**: 清晰的代码结构，将摄像头、检测、Web服务等功能解耦，易于维护和

---